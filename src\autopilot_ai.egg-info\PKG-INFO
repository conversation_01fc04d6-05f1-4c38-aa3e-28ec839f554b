Metadata-Version: 2.4
Name: autopilot-ai
Version: 0.1.0
Summary: AutoPilot AI - 领航AI多智能体系统
Author: AutoPilot AI Team
License: MIT
Project-URL: Homepage, https://github.com/autopilot-ai/autopilot-ai
Project-URL: Repository, https://github.com/autopilot-ai/autopilot-ai
Project-URL: Documentation, https://github.com/autopilot-ai/autopilot-ai/blob/main/README.md
Keywords: ai,agent,autogen,multi-agent
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: autogen-agentchat>=0.4.0
Requires-Dist: autogen-core>=0.4.0
Requires-Dist: fastapi>=0.104.0
Requires-Dist: uvicorn[standard]>=0.24.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pyyaml>=6.0.1
Requires-Dist: redis>=5.0.0
Requires-Dist: jinja2>=3.1.0
Requires-Dist: structlog>=23.2.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: python-dotenv>=1.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.1.0; extra == "dev"
Requires-Dist: pytest-mock>=3.12.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.6.0; extra == "dev"
Requires-Dist: pre-commit>=3.5.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.4.0; extra == "test"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "test"
Requires-Dist: pytest-cov>=4.1.0; extra == "test"
Requires-Dist: pytest-mock>=3.12.0; extra == "test"

领航AI智能体项目
