#!/usr/bin/env python3
"""
测试旅行规划系统V3重构版数据传递修复

验证"福州去莆田玩两天"的意图识别和规划结果是否正确
"""

import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.agents.travel_planner_lg.nodes import _extract_basic_info_from_query
from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.database.redis_client import get_redis_client


async def test_basic_info_extraction():
    """测试基本信息提取功能"""
    print("=" * 60)
    print("测试1: 基本信息提取功能")
    print("=" * 60)
    
    test_queries = [
        "福州去莆田玩两天",
        "从北京到上海旅游3天",
        "在杭州游玩5天",
        "去厦门旅行",
        "上海到苏州玩一天"
    ]
    
    for query in test_queries:
        print(f"\n输入: {query}")
        result = _extract_basic_info_from_query(query)
        print(f"提取结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


async def test_framework_analysis():
    """测试框架分析阶段的数据传递"""
    print("\n" + "=" * 60)
    print("测试2: 框架分析阶段数据传递")
    print("=" * 60)
    
    # 创建事件总线
    redis_client = get_redis_client()
    event_bus = UnifiedEventBus(redis_client)
    
    # 创建图实例
    graph = TravelPlannerGraphV3(event_bus=event_bus)
    
    # 准备测试输入
    input_data = {
        "user_query": "福州去莆田玩两天",
        "user_id": "test_user",
        "execution_mode": "automatic",
        "user_profile": {},
        "task_id": "test_task_001",
        "messages": [{"content": "福州去莆田玩两天"}]
    }
    
    print(f"输入数据: {json.dumps(input_data, ensure_ascii=False, indent=2)}")
    
    try:
        # 执行工作流的前几个步骤
        step_count = 0
        async for step in graph.stream(input_data):
            step_count += 1
            print(f"\n--- 步骤 {step_count} ---")
            
            for node_name, node_result in step.items():
                print(f"节点: {node_name}")
                
                if node_name == "framework_analysis":
                    # 检查框架分析结果
                    framework_analysis = node_result.get("framework_analysis", {})
                    core_intent = framework_analysis.get("core_intent", {})
                    destinations = core_intent.get("destinations", [])
                    travel_days = core_intent.get("travel_days", 0)
                    departure_city = core_intent.get("departure_city", "")
                    
                    print(f"  目的地: {destinations}")
                    print(f"  天数: {travel_days}")
                    print(f"  出发城市: {departure_city}")
                    
                    # 验证数据是否正确
                    if "莆田" in destinations and travel_days == 2:
                        print("  ✅ 框架分析数据正确!")
                    else:
                        print("  ❌ 框架分析数据错误!")
                        print(f"  期望: 目的地包含'莆田', 天数=2")
                        print(f"  实际: 目的地={destinations}, 天数={travel_days}")
                
                elif node_name == "prepare_context":
                    # 检查上下文准备结果
                    consolidated_intent = node_result.get("consolidated_intent", {})
                    print(f"  整合意图: {json.dumps(consolidated_intent, ensure_ascii=False, indent=4)}")
                    
                    # 验证数据传递
                    if consolidated_intent:
                        print("  ✅ 上下文准备完成，数据已传递!")
                    else:
                        print("  ❌ 上下文准备失败，数据传递中断!")
                
                elif node_name == "planner_agent":
                    # 检查规划器是否使用了正确的意图
                    current_action = node_result.get("current_action", {})
                    print(f"  规划器行动: {json.dumps(current_action, ensure_ascii=False, indent=4)}")
            
            # 只测试前几个步骤
            if step_count >= 4:
                break
                
    except Exception as e:
        print(f"❌ 工作流执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_end_to_end():
    """端到端测试：验证完整的数据传递链路"""
    print("\n" + "=" * 60)
    print("测试3: 端到端数据传递验证")
    print("=" * 60)
    
    # 模拟API调用
    from src.api.v3.travel_planner import router
    from fastapi.testclient import TestClient
    from fastapi import FastAPI
    
    app = FastAPI()
    app.include_router(router)
    
    client = TestClient(app)
    
    # 发送请求
    response = client.post("/api/v3/travel-planner/plan", json={
        "user_query": "福州去莆田玩两天",
        "user_id": "test_user",
        "execution_mode": "automatic"
    })
    
    print(f"API响应状态: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API调用成功")
        
        # 解析SSE流
        content = response.content.decode('utf-8')
        lines = content.split('\n')
        
        for line in lines:
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    event = data.get('event')
                    
                    if event == 'node_complete':
                        node_data = data.get('data', {})
                        node_name = node_data.get('node_name')
                        result = node_data.get('result', {})
                        
                        if node_name == 'framework_analysis':
                            framework_analysis = result.get('framework_analysis', {})
                            destinations = framework_analysis.get('core_intent', {}).get('destinations', [])
                            print(f"  框架分析目的地: {destinations}")
                            
                        elif node_name == 'planner_agent':
                            current_action = result.get('current_action', {})
                            print(f"  规划器行动: {current_action}")
                            
                except json.JSONDecodeError:
                    continue
    else:
        print(f"❌ API调用失败: {response.text}")


async def main():
    """主测试函数"""
    print("🚀 开始测试旅行规划系统V3数据传递修复")
    
    # 测试1: 基本信息提取
    await test_basic_info_extraction()
    
    # 测试2: 框架分析数据传递
    await test_framework_analysis()
    
    # 测试3: 端到端测试
    # await test_end_to_end()  # 暂时注释，因为需要启动完整的API服务
    
    print("\n" + "=" * 60)
    print("✅ 测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
