"""
MongoDB客户端

提供MongoDB数据库的连接和操作功能，包括用户、行程、记忆等数据的CRUD操作。
"""
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from urllib.parse import quote_plus
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from pymongo.errors import DuplicateKeyError, ConnectionFailure
from bson import ObjectId

from src.models.database import (
    UserDocument, ItineraryDocument, MemoryDocument, ConversationDocument,
    POIDocument, FeedbackDocument, AnalyticsDocument, AIInteractionLog,
    Collections, INDEXES, VALIDATION_RULES
)
from src.core.logger import get_logger
from src.core.config import get_settings

logger = get_logger("mongodb_client")


class MongoDBClient:
    """MongoDB客户端类"""
    
    def __init__(self):
        """初始化MongoDB客户端"""
        self.settings = get_settings()
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.logger = logger
        
    async def connect(self):
        """连接到MongoDB"""
        if self.client:
            self.logger.info("MongoDB 已经连接，跳过重复连接")
            return

        mongo_config = None  # 提前初始化
        try:
            # 构建连接URL
            mongo_config = self.settings.mongodb
            
            self.logger.info("正在连接MongoDB...", extra={
                "host": mongo_config.host,
                "port": mongo_config.port,
                "database": mongo_config.database,
                "username": mongo_config.username
            })
            
            if mongo_config.username and mongo_config.password:
                # URL编码用户名和密码中的特殊字符
                username = quote_plus(mongo_config.username)
                password = quote_plus(mongo_config.password)
                
                connection_string = (
                    f"mongodb://{username}:{password}@"
                    f"{mongo_config.host}:{mongo_config.port}/{mongo_config.database}"
                    f"?authSource=admin"
                )
            else:
                connection_string = f"mongodb://{mongo_config.host}:{mongo_config.port}"
            
            # 安全地记录连接字符串（隐藏密码）
            if mongo_config.username and mongo_config.password:
                safe_connection_string = connection_string.replace(password, '***')
            else:
                safe_connection_string = connection_string
            self.logger.info(f"MongoDB连接字符串: {safe_connection_string}")
            
            # 创建客户端，调整连接参数
            self.client = AsyncIOMotorClient(
                connection_string,
                serverSelectionTimeoutMS=30000,  # 增加到30秒
                connectTimeoutMS=20000,          # 增加到20秒
                socketTimeoutMS=20000,           # 增加到20秒
                maxPoolSize=10,
                minPoolSize=1,
                retryWrites=True,
                retryReads=True
            )
            
            # 选择数据库
            self.database = self.client[mongo_config.database]
            
            # 测试连接
            self.logger.info("正在测试MongoDB连接...")
            await self.client.admin.command('ping')
            
            # 初始化数据库
            await self._initialize_database()
            
            self.logger.info("MongoDB连接成功", extra={
                "host": mongo_config.host,
                "port": mongo_config.port,
                "database": mongo_config.database
            })
            
        except AttributeError:
             self.logger.error("配置错误: 'settings' 对象中缺少 'mongodb' 配置", exc_info=True)
             raise ConnectionFailure("配置错误: 缺少 'mongodb' 配置")
        except Exception as e:
            self.logger.error(f"MongoDB连接失败: {str(e)}", extra={
                "error_type": type(e).__name__,
                "host": mongo_config.host if mongo_config else "N/A",
                "port": mongo_config.port if mongo_config else "N/A",
                "database": mongo_config.database if mongo_config else "N/A",
            })
            raise ConnectionFailure(f"无法连接到MongoDB: {str(e)}")
            
    async def disconnect(self):
        """断开MongoDB连接"""
        if self.client:
            self.client.close()
            self.client = None
            self.database = None
            self.logger.info("MongoDB连接已断开")
            
    async def _initialize_database(self):
        """初始化数据库（创建索引和验证规则）"""
        if self.database is None:
            return
            
        try:
            # 创建集合和索引
            for collection_name, indexes in INDEXES.items():
                collection = self.database[collection_name]
                
                # 创建索引
                for index in indexes:
                    if isinstance(index, tuple):
                        await collection.create_index([index])
                    else:
                        await collection.create_index(index)
                        
                # 设置验证规则
                if collection_name in VALIDATION_RULES:
                    try:
                        await self.database.command({
                            "collMod": collection_name,
                            "validator": VALIDATION_RULES[collection_name]
                        })
                    except Exception as e:
                        # 如果集合不存在，创建带验证规则的集合
                        await self.database.create_collection(
                            collection_name,
                            validator=VALIDATION_RULES[collection_name]
                        )
                        
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
            
    def get_collection(self, collection_name: str) -> AsyncIOMotorCollection:
        """获取集合"""
        if self.database is None:
            raise RuntimeError("数据库未连接")
        return self.database[collection_name]

    # 通用数据库操作方法
    async def find_one(self, collection_name: str, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """查找单个文档"""
        try:
            collection = self.get_collection(collection_name)
            return await collection.find_one(query)
        except Exception as e:
            self.logger.error(f"查找文档失败: {str(e)}")
            raise

    async def find(self, collection_name: str, query: Dict[str, Any],
                   sort: Optional[List] = None, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """查找多个文档"""
        try:
            collection = self.get_collection(collection_name)
            cursor = collection.find(query)

            if sort:
                cursor = cursor.sort(sort)
            if limit:
                cursor = cursor.limit(limit)

            return await cursor.to_list(length=limit)
        except Exception as e:
            self.logger.error(f"查找文档失败: {str(e)}")
            raise

    async def insert_one(self, collection_name: str, document: Dict[str, Any]):
        """插入单个文档"""
        try:
            collection = self.get_collection(collection_name)
            return await collection.insert_one(document)
        except Exception as e:
            self.logger.error(f"插入文档失败: {str(e)}")
            raise

    async def update_one(self, collection_name: str, query: Dict[str, Any],
                        update: Dict[str, Any]):
        """更新单个文档"""
        try:
            collection = self.get_collection(collection_name)
            return await collection.update_one(query, update)
        except Exception as e:
            self.logger.error(f"更新文档失败: {str(e)}")
            raise

    async def delete_one(self, collection_name: str, query: Dict[str, Any]):
        """删除单个文档"""
        try:
            collection = self.get_collection(collection_name)
            return await collection.delete_one(query)
        except Exception as e:
            self.logger.error(f"删除文档失败: {str(e)}")
            raise

    async def aggregate(self, collection_name: str, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """聚合查询"""
        try:
            collection = self.get_collection(collection_name)
            cursor = collection.aggregate(pipeline)
            return await cursor.to_list(length=None)
        except Exception as e:
            self.logger.error(f"聚合查询失败: {str(e)}")
            raise
        
    # 用户相关操作
    async def create_user(self, user_data: Dict[str, Any]) -> str:
        """创建用户"""
        try:
            user_doc = UserDocument(**user_data)
            collection = self.get_collection(Collections.USERS)
            result = await collection.insert_one(user_doc.model_dump(by_alias=True, exclude={"id"}))
            
            self.logger.info(f"用户创建成功: {user_data.get('user_id')}")
            return str(result.inserted_id)
            
        except DuplicateKeyError:
            raise ValueError("用户已存在")
        except Exception as e:
            self.logger.error(f"创建用户失败: {str(e)}")
            raise
            
    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            collection = self.get_collection(Collections.USERS)
            user = await collection.find_one({"user_id": user_id})
            
            if user:
                user["_id"] = str(user["_id"])
                return user
            return None
            
        except Exception as e:
            self.logger.error(f"获取用户失败: {str(e)}")
            raise
            
    async def update_user(self, user_id: str, update_data: Dict[str, Any]) -> bool:
        """更新用户信息"""
        try:
            update_data["updated_at"] = datetime.now()
            collection = self.get_collection(Collections.USERS)
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            self.logger.error(f"更新用户失败: {str(e)}")
            raise
            
    # 行程相关操作
    async def create_itinerary(self, itinerary_data: Dict[str, Any]) -> str:
        """创建行程"""
        try:
            itinerary_doc = ItineraryDocument(**itinerary_data)
            collection = self.get_collection(Collections.ITINERARIES)
            result = await collection.insert_one(itinerary_doc.model_dump(by_alias=True, exclude={"id"}))
            
            self.logger.info(f"行程创建成功: {itinerary_data.get('trace_id')}")
            return str(result.inserted_id)
            
        except Exception as e:
            self.logger.error(f"创建行程失败: {str(e)}")
            raise
            
    async def get_itinerary(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """获取行程信息"""
        try:
            collection = self.get_collection(Collections.ITINERARIES)
            itinerary = await collection.find_one({"trace_id": trace_id})
            
            if itinerary:
                itinerary["_id"] = str(itinerary["_id"])
                return itinerary
            return None
            
        except Exception as e:
            self.logger.error(f"获取行程失败: {str(e)}")
            raise
            
    async def update_itinerary(self, trace_id: str, update_data: Dict[str, Any]) -> bool:
        """更新行程信息"""
        try:
            update_data["updated_at"] = datetime.now()
            collection = self.get_collection(Collections.ITINERARIES)
            result = await collection.update_one(
                {"trace_id": trace_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            self.logger.error(f"更新行程失败: {str(e)}")
            raise
            
    async def get_user_itineraries(
        self, 
        user_id: str, 
        limit: int = 20, 
        skip: int = 0,
        status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取用户行程列表"""
        try:
            collection = self.get_collection(Collections.ITINERARIES)
            
            # 构建查询条件
            query = {"user_id": user_id}
            if status:
                query["status"] = status
                
            # 查询行程
            cursor = collection.find(query).sort("created_at", -1).skip(skip).limit(limit)
            itineraries = await cursor.to_list(length=limit)
            
            # 转换ObjectId为字符串
            for itinerary in itineraries:
                itinerary["_id"] = str(itinerary["_id"])
                
            return itineraries
            
        except Exception as e:
            self.logger.error(f"获取用户行程列表失败: {str(e)}")
            raise
            
    # 记忆相关操作
    async def create_memory(self, memory_data: Dict[str, Any]) -> str:
        """创建记忆"""
        try:
            memory_doc = MemoryDocument(**memory_data)
            collection = self.get_collection(Collections.MEMORIES)
            result = await collection.insert_one(memory_doc.model_dump(by_alias=True, exclude={"id"}))
            
            self.logger.info(f"记忆创建成功: {memory_data.get('user_id')}")
            return str(result.inserted_id)
            
        except Exception as e:
            self.logger.error(f"创建记忆失败: {str(e)}")
            raise
            
    async def get_user_memories(
        self, 
        user_id: str, 
        memory_type: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取用户记忆"""
        try:
            collection = self.get_collection(Collections.MEMORIES)
            
            # 构建查询条件
            query = {"user_id": user_id}
            if memory_type:
                query["memory_type"] = memory_type
                
            # 按重要性和时间排序
            cursor = collection.find(query).sort([
                ("importance_score", -1),
                ("created_at", -1)
            ]).limit(limit)
            
            memories = await cursor.to_list(length=limit)
            
            # 转换ObjectId为字符串
            for memory in memories:
                memory["_id"] = str(memory["_id"])
                
            return memories
            
        except Exception as e:
            self.logger.error(f"获取用户记忆失败: {str(e)}")
            raise
            
    # POI相关操作
    async def upsert_poi(self, poi_data: Dict[str, Any]) -> str:
        """插入或更新POI"""
        try:
            poi_doc = POIDocument(**poi_data)
            collection = self.get_collection(Collections.POIS)
            
            # 使用upsert操作
            result = await collection.update_one(
                {"poi_id": poi_data["poi_id"]},
                {"$set": poi_doc.model_dump(by_alias=True, exclude={"id"})},
                upsert=True
            )
            
            if result.upserted_id:
                return str(result.upserted_id)
            else:
                # 获取已存在的文档ID
                existing = await collection.find_one({"poi_id": poi_data["poi_id"]})
                return str(existing["_id"]) if existing else ""
                
        except Exception as e:
            self.logger.error(f"POI操作失败: {str(e)}")
            raise
            
    # 分析数据操作
    async def log_analytics(self, event_data: Dict[str, Any]) -> str:
        """记录分析数据"""
        try:
            analytics_doc = AnalyticsDocument(**event_data)
            collection = self.get_collection(Collections.ANALYTICS)
            result = await collection.insert_one(analytics_doc.model_dump(by_alias=True, exclude={"id"}))
            
            return str(result.inserted_id)
            
        except Exception as e:
            self.logger.error(f"记录分析数据失败: {str(e)}")
            raise

    # AI交互日志相关操作
    async def create_interaction_log(self, log_data: Dict[str, Any]) -> str:
        """创建一个AI交互日志"""
        try:
            log_doc = AIInteractionLog(**log_data)
            collection = self.get_collection(Collections.AI_INTERACTION_LOGS)
            result = await collection.insert_one(log_doc.model_dump(by_alias=True, exclude={"id"}))
            
            self.logger.info(f"AI交互日志创建成功: {log_data.get('interaction_id')}")
            return str(result.inserted_id)
            
        except DuplicateKeyError:
            self.logger.warning(f"AI交互日志已存在: {log_data.get('interaction_id')}")
            raise ValueError("AI交互日志已存在")
        except Exception as e:
            self.logger.error(f"创建AI交互日志失败: {str(e)}")
            raise
            
    async def update_interaction_log(self, interaction_id: str, interaction_data: Dict[str, Any]) -> bool:
        """更新AI交互日志"""
        try:
            collection = self.get_collection(Collections.AI_INTERACTION_LOGS)
            result = await collection.update_one(
                {"interaction_id": interaction_id},
                {"$set": interaction_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            self.logger.error(f"更新AI交互日志失败: {str(e)}")
            raise

    async def add_step_to_log(self, interaction_id: str, step_data: Dict[str, Any]) -> bool:
        """向AI交互日志中添加一个业务步骤"""
        try:
            collection = self.get_collection(Collections.AI_INTERACTION_LOGS)
            result = await collection.update_one(
                {"interaction_id": interaction_id},
                {"$push": {"business_steps_log": step_data}}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            self.logger.error(f"向AI交互日志添加步骤失败: {str(e)}")
            raise


# 全局MongoDB客户端实例
_global_mongo_client: Optional[MongoDBClient] = None


async def get_mongo_client() -> MongoDBClient:
    """获取全局MongoDB客户端实例"""
    global _global_mongo_client
    if _global_mongo_client is None:
        _global_mongo_client = MongoDBClient()
        await _global_mongo_client.connect()
    return _global_mongo_client


async def close_mongo_client():
    """关闭全局MongoDB客户端"""
    global _global_mongo_client
    if _global_mongo_client:
        await _global_mongo_client.disconnect()
        _global_mongo_client = None
