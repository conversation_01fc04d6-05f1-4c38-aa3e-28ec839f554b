<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V3 API 测试</title>
</head>
<body>
    <h1>V3 API 测试</h1>
    <button id="testButton">测试V3 API</button>
    <div id="result"></div>

    <script>
        document.getElementById('testButton').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await fetch('/api/v3/travel-planner/plan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_query: '我在福州闽东大厦，这周末要去莆田玩两天',
                        user_id: '1',
                        execution_mode: 'automatic',
                        user_profile: {}
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                resultDiv.innerHTML = '请求成功！正在处理SSE流...<br>';
                
                // 处理SSE流
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const eventData = JSON.parse(line.slice(6));
                                resultDiv.innerHTML += `事件: ${JSON.stringify(eventData)}<br>`;
                            } catch (e) {
                                console.error('解析事件失败:', e);
                            }
                        }
                    }
                }
                
                resultDiv.innerHTML += '流处理完成！';
                
            } catch (error) {
                console.error('测试失败:', error);
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        });
    </script>
</body>
</html>
