# 项目结构文档

**版本**: 1.2

## 根目录结构

```
.
├── src/                 # 核心应用源码目录
├── config/              # 配置文件目录
├── doc/                 # 项目所有文档
├── tests/               # 分层测试目录 - 单元测试与集成测试
├── .env                 # (本地) 环境变量文件, 不提交到git
├── .env.example         # 环境变量模板文件
├── .gitignore
├── docker-compose.yml   # 用于本地开发的依赖服务
├── pyproject.toml       # 项目依赖与配置 (PEP 621)
├── README.md
└── ...
```

## `src` 核心源码目录结构

这是项目业务逻辑的核心。所有应用代码都位于此目录下。

```
src/
├── agents/                 # Agent业务逻辑实现
│   ├── __init__.py
│   ├── base.py             # (可选) 自定义Agent基类
│   ├── planner.py          # 规划Agent
│   └── researcher.py       # 研究员Agent (作为ExecutorAgent的示例)
│
├── api/                    # FastAPI相关的API接口层
│   ├── __init__.py
│   ├── endpoints/          # API路由端点
│   └── deps.py             # (可选) API依赖注入
│
├── core/                   # 项目核心组件，如配置加载
│   ├── __init__.py
│   └── config.py           # pydantic-settings 配置模型, 从 config/*.yaml 和 .env 加载配置
│
├── llms/                   # LLM服务管理
│   ├── __init__.py
│   └── manager.py          # LLMManager实现, 按角色为Agent提供llm_config的工厂
│
├── memory/                 # 分层记忆系统
│   ├── __init__.py
│   └── manager.py          # MemoryManager实现
│
├── models/                 # Pydantic数据模型 (定义应用内的数据结构, 如Plan, State), 非LLM模型文件
│   ├── __init__.py
│   ├── state.py            # AgentState 模型
│   └── plan.py             # Plan 和 Step 模型
│
├── prompts/                # 提示词工程
│   ├── __init__.py
│   ├── manager.py          # PromptManager 实现
│   └── templates/
│       ├── planner.md
│       └── researcher.md
│
├── retrieval/              # 记忆检索相关模块
│   ├── __init__.py
│   ├── embedding.py        # Embedding模型封装
│   └── reranker.py         # Reranker模型封装
│
├── tools/                  # Agent可使用的工具
│   ├── __init__.py
│   ├── registry.py         # ToolRegistry 实现
│   └── web_search.py       # 网页搜索工具示例
│
└── main.py                 # FastAPI 应用入口点
```

## `tests` 分层测试目录结构

`tests` 目录采用分层架构设计，清晰区分不同类型的测试，遵循现代Python项目的最佳实践。

```
tests/
├── __init__.py
├── README.md               # 测试结构说明文档
│
├── integration/            # 🔗 集成测试 - 端到端测试
│   ├── __init__.py
│   ├── test_agent_system.py          # Agent系统集成测试
│   ├── test_python_expert_integration.py  # Python专家Agent集成测试
│   └── test_autogen_agent_integration.py  # AutoGen框架集成测试
│
├── unit/                   # 🧩 单元测试 - 模块级测试
│   ├── __init__.py
│   ├── agents/             # Agent模块单元测试
│   │   ├── __init__.py
│   │   ├── test_simple_assistant.py  # 测试 src/agents/simple_assistant.py
│   │   └── test_python_expert.py     # 测试 src/agents/python_expert.py
│   ├── integration/        # 🔌 集成适配器单元测试
│   │   ├── __init__.py
│   │   └── test_autogen_adapter.py   # AutoGen适配器单元测试（Mock）
│   ├── core/               # 核心模块单元测试
│   │   ├── __init__.py
│   │   ├── test_config.py            # 测试 src/core/config.py
│   │   ├── test_logger.py            # 测试 src/core/logger.py
│   │   └── test_llm_manager.py       # 测试 src/core/llm_manager.py
│   ├── llms/               # LLM模块测试
│   │   ├── __init__.py
│   │   └── test_manager.py           # 测试 src/llms/manager.py
│   ├── memory/             # 记忆模块测试
│   │   ├── __init__.py
│   │   └── test_manager.py           # 测试 src/memory/manager.py
│   ├── retrieval/          # 检索模块测试
│   │   ├── __init__.py
│   │   └── test_embedding.py         # 测试 src/retrieval/embedding.py
│   ├── tools/              # 工具模块测试
│   │   ├── __init__.py
│   │   └── test_registry.py          # 测试 src/tools/registry.py
│   ├── api/                # API模块测试
│   │   └── __init__.py
│   └── templates/          # 模板相关测试
│       └── __init__.py
│
└── scripts/                # 📜 测试脚本和自动化工具
    └── test_all_agents.ps1          # 自动化测试脚本
```

### 测试分层说明

#### 🔗 集成测试 (Integration Tests)
- **位置**: `tests/integration/`
- **目的**: 测试多个模块协作的完整流程
- **特点**: 使用真实API、测试端到端功能
- **示例**: Agent创建→对话→历史管理的完整工作流

#### 🧩 单元测试 (Unit Tests)  
- **位置**: `tests/unit/`
- **目的**: 测试单个类、函数的独立功能
- **特点**: 快速执行、使用Mock、隔离外部依赖
- **结构**: 严格镜像 `src/` 目录结构

#### 📜 测试脚本 (Test Scripts)
- **位置**: `tests/scripts/`
- **目的**: 自动化测试运行、环境检查、报告生成
- **特点**: PowerShell/Bash脚本，支持多种测试模式

### 测试命名与组织约定

*   **命名约定**: 
    - 测试文件必须以 `test_` 开头
    - 测试函数和类中的测试方法也必须以 `test_` 开头
    - 集成测试文件建议以 `_integration` 结尾
*   **职责划分**: 
    - 单元测试专注于模块的内部逻辑，所有外部依赖（LLM、DB、网络API）都必须被模拟（Mock）
    - 集成测试验证模块间协作，可使用真实的外部服务
*   **目录对应**: `tests/unit/` 的内部结构必须严格镜像 `src/` 的目录结构

### 测试重组历史

**重组动机**: 解决测试文件命名冲突和职责混淆问题
- ❌ **重组前**: 测试文件混杂，存在命名冲突（如 `tests/test_python_expert.py` 与 `tests/agents/test_python_expert.py`）
- ✅ **重组后**: 清晰的分层结构，集成测试和单元测试职责明确

**重组收益**:
- 🎯 **职责清晰**: 集成测试验证端到端流程，单元测试验证模块逻辑
- 🔧 **易于维护**: 测试脚本独立管理，便于CI/CD集成
- 📈 **开发效率**: 开发时主要运行单元测试（快速），提交前运行完整测试套件
- 📚 **遵循标准**: 符合Python项目测试组织的最佳实践

## AutoGen框架集成

### 🔌 集成架构

项目现已集成Microsoft AutoGen框架，提供双轨并行的Agent系统：

#### 1. 自研Agent框架
- **位置**: `src/agents/`
- **特点**: 轻量级、高性能、完全自主控制
- **适用场景**: 单Agent工作流、简单对话、快速响应

#### 2. AutoGen框架集成
- **位置**: 集成测试中的`AutoGenIntegration`适配器
- **特点**: 多Agent协作、复杂工作流、生态丰富
- **适用场景**: 团队协作、复杂任务、工具链集成

### 🔄 配置系统复用

AutoGen集成完全复用现有配置系统：

```python
# 现有配置自动适配AutoGen
settings = get_settings()
basic_config = settings.get_llm_config_by_role("basic")     # glm-4-flash
reasoning_config = settings.get_llm_config_by_role("reasoning")  # glm-z1-flash

# 通过适配器转换为AutoGen格式
autogen_client = integration.create_model_client("basic")
```

### 🧪 测试体系扩展

#### AutoGen集成测试
- **文件**: `tests/integration/test_autogen_agent_integration.py`
- **覆盖**: 6项端到端集成测试
- **内容**: 可用性、配置转换、Agent创建、对话测试、共存性验证

#### AutoGen适配器单元测试
- **文件**: `tests/unit/integration/test_autogen_adapter.py`
- **覆盖**: 17项Mock单元测试
- **内容**: 适配器逻辑、错误处理、边界条件测试

#### 测试命令增强
```bash
# AutoGen专项测试
.\tests\scripts\test_all_agents.ps1 -AutoGen

# 跳过AutoGen测试
.\tests\scripts\test_all_agents.ps1 -SkipAutoGen
```

### 💡 架构优势

1. **框架互补**: 自研框架提供性能和控制力，AutoGen提供协作能力
2. **渐进迁移**: 可以逐步将部分功能迁移到AutoGen，无需重写
3. **生态接入**: 利用AutoGen丰富的工具和扩展生态
4. **容错设计**: AutoGen不可用时自动回退，不影响现有功能

### 📚 相关文档

- [AutoGen快速开始指南](../AUTOGEN_QUICKSTART.md) - 详细使用说明
- [测试指南](测试指南.md) - 包含AutoGen测试说明
- [进度文档](进度文档.md) - AutoGen集成开发记录 