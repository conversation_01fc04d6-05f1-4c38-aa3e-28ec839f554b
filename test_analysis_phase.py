#!/usr/bin/env python3
"""
测试分析阶段的SSE流式响应功能
"""

import asyncio
import aiohttp
import json
import sys
import time

async def test_analysis_phase():
    """测试分析阶段的SSE连接和响应"""
    
    # 测试参数
    user_id = "1"
    query = "上海3天亲子慢节奏趣味性儿童友好"
    trace_id = f"test_trace_{int(time.time())}"
    
    # SSE端点URL
    url = f"http://localhost:8000/api/travel/plan/{trace_id}/stream"
    params = {
        "user_id": user_id,
        "query": query,
        "phase": "analysis"
    }
    
    print(f"🧪 开始测试分析阶段")
    print(f"📍 URL: {url}")
    print(f"📝 查询: {query}")
    print(f"👤 用户ID: {user_id}")
    print(f"🔄 阶段: analysis")
    print("-" * 60)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                print(f"✅ SSE连接建立成功 - 状态码: {response.status}")
                
                if response.status != 200:
                    print(f"❌ 连接失败: {response.status}")
                    text = await response.text()
                    print(f"错误信息: {text}")
                    return
                
                event_count = 0
                analysis_steps_received = []
                
                # 读取SSE事件流
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        event_count += 1
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        try:
                            event_data = json.loads(data_str)
                            event_type = event_data.get('event_type')
                            payload = event_data.get('payload', {})
                            
                            print(f"📨 事件 #{event_count}: {event_type}")
                            
                            if event_type == 'analysis_step':
                                step_type = payload.get('step_type')
                                title = payload.get('title')
                                content = payload.get('content')
                                completed = payload.get('completed')
                                
                                print(f"   📊 分析步骤: {step_type}")
                                print(f"   📋 标题: {title}")
                                print(f"   📄 内容: {content}")
                                print(f"   ✅ 完成: {completed}")
                                
                                analysis_steps_received.append(step_type)
                                
                            elif event_type == 'stage_progress':
                                stage = payload.get('stage')
                                progress = payload.get('progress')
                                message = payload.get('message')
                                print(f"   🔄 阶段: {stage}, 进度: {progress}%, 消息: {message}")
                                
                            elif event_type == 'error':
                                error_msg = payload.get('error_message', '未知错误')
                                print(f"   ❌ 错误: {error_msg}")
                                break
                                
                            else:
                                print(f"   📦 载荷: {json.dumps(payload, ensure_ascii=False, indent=2)}")
                                
                        except json.JSONDecodeError as e:
                            print(f"   ⚠️  JSON解析失败: {e}")
                            print(f"   📄 原始数据: {data_str}")
                    
                    elif line.startswith('id: '):
                        event_id = line[4:]
                        print(f"🆔 事件ID: {event_id}")
                    
                    elif line == '':
                        # 空行表示事件结束
                        print("-" * 40)
                
                print(f"\n📊 测试结果总结:")
                print(f"   📨 总事件数: {event_count}")
                print(f"   📋 收到的分析步骤: {analysis_steps_received}")
                
                expected_steps = ['user_intent', 'poi_preference', 'food_preference', 'accommodation_preference']
                missing_steps = [step for step in expected_steps if step not in analysis_steps_received]
                
                if missing_steps:
                    print(f"   ⚠️  缺失的分析步骤: {missing_steps}")
                else:
                    print(f"   ✅ 所有预期的分析步骤都已收到")
                    
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 启动分析阶段测试...")
    asyncio.run(test_analysis_phase())
