#!/usr/bin/env powershell
# AutoPilot AI - 全自动Agent测试脚本
# 运行所有Agent的测试，包括单元测试、集成测试和新增的AutoGen集成测试

param(
    [switch]$Quick,       # 快速模式，跳过覆盖率报告
    [switch]$AutoGen,     # 仅运行AutoGen集成测试
    [switch]$SkipAutoGen, # 跳过AutoGen集成测试
    [switch]$Coverage     # 运行覆盖率测试
)

# 强制脚本以项目根目录为工作目录（脚本所在位置的上两级）
$ProjectRoot = Resolve-Path (Join-Path $PSScriptRoot "..\..")
Set-Location $ProjectRoot
Write-Host "已将工作目录设置为项目根目录: $ProjectRoot" -ForegroundColor DarkGray

function Write-Section {
    param([string]$Title)
    Write-Host "`n$('='*80)" -ForegroundColor Cyan
    Write-Host "  $Title" -ForegroundColor Yellow
    Write-Host "$('='*80)" -ForegroundColor Cyan
}

function Test-Command {
    param([string]$Command)
    return Get-Command $Command -ErrorAction SilentlyContinue
}

# 设置编码
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Section "AutoPilot AI Agent Test Suite"

# 检查必要的命令
if (-not (Test-Command "python")) {
    Write-Host "Error: Python not found" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "pytest")) {
    Write-Host "Error: pytest not found, please activate virtual environment" -ForegroundColor Red
    exit 1
}

# 如果仅运行AutoGen测试
if ($AutoGen) {
    Write-Section "AutoGen Integration Tests"
    Write-Host "Running AutoGen integration tests..." -ForegroundColor Blue
    try {
        python tests/integration/test_autogen_agent_integration.py
        if ($LASTEXITCODE -eq 0) {
            Write-Host "AutoGen integration tests completed" -ForegroundColor Green
        } else {
            Write-Host "AutoGen integration tests had issues" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "AutoGen integration tests failed: $_" -ForegroundColor Red
    }
    
    Write-Host "`nRunning AutoGen adapter unit tests..." -ForegroundColor Blue
    try {
        pytest tests/unit/integration/test_autogen_adapter.py -v
        if ($LASTEXITCODE -eq 0) {
            Write-Host "AutoGen adapter unit tests passed" -ForegroundColor Green
        } else {
            Write-Host "AutoGen adapter unit tests had issues" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "AutoGen adapter unit tests failed: $_" -ForegroundColor Red
    }
    
    exit 0
}

try {
    # 显示测试环境信息
    Write-Host "Test Environment Info:" -ForegroundColor Cyan
    Write-Host "   Python version: $(python --version)" -ForegroundColor Gray
    Write-Host "   Pytest version: $(pytest --version)" -ForegroundColor Gray
    Write-Host "   Working directory: $(Get-Location)" -ForegroundColor Gray

    $test_scripts = @(
        @{Name="Agent System Test"; Script="tests/integration/test_agent_system.py"; Icon="🤖"},
        @{Name="Python Expert Test"; Script="tests/integration/test_python_expert_integration.py"; Icon="🐍"}
    )

    # 添加AutoGen测试（如果未跳过）
    if (-not $SkipAutoGen) {
        $test_scripts += @{Name="AutoGen Integration Test"; Script="tests/integration/test_autogen_agent_integration.py"; Icon="🔗"}
    }

    # 运行单独的集成测试
    Write-Section "Integration Tests"
    foreach ($test in $test_scripts) {
        Write-Host "`n$($test.Icon) Running $($test.Name)..." -ForegroundColor Blue
        try {
            python $test.Script
            if ($LASTEXITCODE -eq 0) {
                Write-Host "$($test.Name) completed" -ForegroundColor Green
            } else {
                Write-Host "$($test.Name) had issues, but continuing" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "$($test.Name) failed: $_" -ForegroundColor Red
        }
    }

    # 运行单元测试
    Write-Section "Unit Tests"
    Write-Host "Running core module unit tests..." -ForegroundColor Blue
    
    if ($Quick) {
        try {
            pytest tests/unit/core/ -v --tb=short
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Core module unit tests passed" -ForegroundColor Green
            } else {
                Write-Host "Core module unit tests failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "Unit test execution failed: $_" -ForegroundColor Red
        }
    } else {
        try {
            if ($Coverage) {
                pytest tests/unit/core/ -v --cov=src/core --cov-report=term-missing --cov-report=html:reports/core_coverage
            } else {
                pytest tests/unit/core/ -v
            }
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Core module unit tests passed" -ForegroundColor Green
                if ($Coverage -and (Test-Path "reports/core_coverage/index.html")) {
                    Write-Host "Coverage report: reports/core_coverage/index.html" -ForegroundColor Cyan
                }
            } else {
                Write-Host "Core module unit tests failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "Unit test execution failed: $_" -ForegroundColor Red
        }
    }

    # 运行Agent模块单元测试
    Write-Host "`nRunning Agent module unit tests..." -ForegroundColor Blue
    if ($Quick) {
        try {
            pytest tests/unit/agents/ -v --tb=short
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Agent module unit tests passed" -ForegroundColor Green
            } else {
                Write-Host "Agent module unit tests failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "Agent unit test execution failed: $_" -ForegroundColor Red
        }
    } else {
        try {
            if ($Coverage) {
                pytest tests/unit/agents/ -v --cov=src/agents --cov-report=term-missing --cov-report=html:reports/agent_coverage
            } else {
                pytest tests/unit/agents/ -v
            }
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Agent module unit tests passed" -ForegroundColor Green
                if ($Coverage -and (Test-Path "reports/agent_coverage/index.html")) {
                    Write-Host "Coverage report: reports/agent_coverage/index.html" -ForegroundColor Cyan
                }
            } else {
                Write-Host "Agent module unit tests failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "Agent unit test execution failed: $_" -ForegroundColor Red
        }
    }

    # 运行AutoGen适配器单元测试（如果未跳过）
    if (-not $SkipAutoGen) {
        Write-Host "`nRunning AutoGen adapter unit tests..." -ForegroundColor Blue
        try {
            pytest tests/unit/integration/test_autogen_adapter.py -v
            if ($LASTEXITCODE -eq 0) {
                Write-Host "AutoGen adapter unit tests passed" -ForegroundColor Green
            } else {
                Write-Host "AutoGen adapter unit tests had issues" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "AutoGen adapter unit tests failed: $_" -ForegroundColor Red
        }
    }

    # 总结
    Write-Section "Test Summary"
    Write-Host "All tests completed!" -ForegroundColor Green
    Write-Host "Tips:" -ForegroundColor Cyan
    Write-Host "   - Use -Quick to skip coverage reports" -ForegroundColor Gray
    Write-Host "   - Use -AutoGen to run only AutoGen tests" -ForegroundColor Gray
    Write-Host "   - Use -SkipAutoGen to skip AutoGen tests" -ForegroundColor Gray
    Write-Host "   - Use -Coverage to generate coverage reports" -ForegroundColor Gray
    Write-Host "   - Check reports/ directory for detailed reports" -ForegroundColor Gray

} catch {
    Write-Host "`nTest script execution error: $_" -ForegroundColor Red
    exit 1
} 