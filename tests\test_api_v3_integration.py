"""
API V3.0 集成测试

测试新的SSE流式API端点和事件发布功能
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# 导入Flask应用和API
from src.api.v3.travel_planner import travel_planner_v3_bp
from src.services.unified_event_bus import UnifiedEventBus
from src.tools.unified_registry import unified_registry

# 确保工具被正确注册
import src.tools.travel_planner.consolidated_tools
import src.tools.travel_planner.icp_tools


class TestAPIV3Integration:
    """测试API V3.0集成"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建Flask测试客户端
        from flask import Flask
        self.app = Flask(__name__)
        self.app.register_blueprint(travel_planner_v3_bp)
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        
        # 创建模拟Redis客户端
        self.mock_redis = AsyncMock()
        self.mock_redis_client = Mock()
        self.mock_redis_client.client = self.mock_redis
    
    def test_health_check_endpoint(self):
        """测试健康检查端点"""
        with patch('src.api.v3.travel_planner.get_redis_client') as mock_get_redis:
            mock_get_redis.return_value = self.mock_redis_client

            response = self.client.get('/api/v3/travel-planner/health')

            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['status'] == 'healthy'
            assert data['version'] == '3.0'
            assert data['architecture'] == 'unified'
            assert 'components' in data
            assert 'tool_registry' in data['components']
    
    def test_get_tools_endpoint(self):
        """测试获取工具列表端点"""
        response = self.client.get('/api/v3/travel-planner/tools')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'tool_registry' in data
        assert 'action_schemas' in data
        
        # 验证工具注册表信息
        tool_registry = data['tool_registry']
        assert 'action_tools_count' in tool_registry
        assert 'planner_tools_count' in tool_registry
        assert 'action_tools' in tool_registry
        assert 'planner_tools' in tool_registry
    
    def test_plan_travel_endpoint_validation(self):
        """测试旅行规划端点的输入验证"""
        # 测试空请求
        response = self.client.post('/api/v3/travel-planner/plan')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        
        # 测试缺少user_query
        response = self.client.post(
            '/api/v3/travel-planner/plan',
            json={}
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'user_query is required' in data['error']
    
    @patch('src.api.v3.travel_planner.get_redis_client')
    @patch('src.agents.travel_planner_lg.graph_v3.TravelPlannerGraphV3')
    def test_plan_travel_sse_response(self, mock_graph_class, mock_get_redis):
        """测试旅行规划SSE响应"""
        # 设置模拟
        mock_get_redis.return_value = self.mock_redis_client
        
        # 创建模拟图实例
        mock_graph = AsyncMock()
        mock_graph_class.return_value = mock_graph
        
        # 模拟流式响应
        async def mock_stream(input_data):
            yield {
                "framework_analysis": {
                    "framework_analysis": {
                        "core_intent": {
                            "destinations": ["北京"],
                            "travel_days": 3
                        }
                    }
                }
            }
            yield {
                "preference_analysis": {
                    "preference_analysis": {
                        "attraction_preferences": {
                            "preferred_types": ["历史文化"]
                        }
                    }
                }
            }
        
        mock_graph.stream = mock_stream
        
        # 发送请求
        request_data = {
            "user_query": "我想去北京玩三天",
            "user_id": "test_user",
            "execution_mode": "automatic"
        }
        
        response = self.client.post(
            '/api/v3/travel-planner/plan',
            json=request_data
        )
        
        # 验证响应
        assert response.status_code == 200
        assert response.mimetype == 'text/event-stream'
        
        # 验证响应头
        assert 'Cache-Control' in response.headers
        assert response.headers['Cache-Control'] == 'no-cache'
        assert 'Connection' in response.headers
        assert response.headers['Connection'] == 'keep-alive'
    
    @patch('src.api.v3.travel_planner.get_redis_client')
    def test_get_task_status_endpoint(self, mock_get_redis):
        """测试获取任务状态端点"""
        mock_get_redis.return_value = self.mock_redis_client
        
        # 模拟事件总线返回状态
        mock_status = {
            "current_phase": "framework_analysis",
            "is_completed": False,
            "framework_analysis": {
                "core_intent": {
                    "destinations": ["北京"]
                }
            }
        }
        
        with patch('src.services.unified_event_bus.UnifiedEventBus') as mock_event_bus_class:
            mock_event_bus = AsyncMock()
            mock_event_bus_class.return_value = mock_event_bus
            mock_event_bus.get_task_status.return_value = mock_status
            
            response = self.client.get('/api/v3/travel-planner/status/test_task_123')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['task_id'] == 'test_task_123'
            assert 'status' in data
            assert data['status']['current_phase'] == 'framework_analysis'
    
    @patch('src.api.v3.travel_planner.get_redis_client')
    def test_get_task_status_not_found(self, mock_get_redis):
        """测试获取不存在任务的状态"""
        mock_get_redis.return_value = self.mock_redis_client
        
        with patch('src.services.unified_event_bus.UnifiedEventBus') as mock_event_bus_class:
            mock_event_bus = AsyncMock()
            mock_event_bus_class.return_value = mock_event_bus
            mock_event_bus.get_task_status.return_value = None
            
            response = self.client.get('/api/v3/travel-planner/status/nonexistent_task')
            
            assert response.status_code == 404
            data = json.loads(response.data)
            assert 'Task not found' in data['error']
    
    def test_cors_headers(self):
        """测试CORS头设置"""
        response = self.client.get('/api/v3/travel-planner/health')
        
        # 验证CORS头（由于使用了@cross_origin装饰器）
        # 注意：在测试环境中，CORS头可能不会自动添加
        # 这里主要验证端点可以正常访问
        assert response.status_code == 200
    
    def test_error_handling(self):
        """测试错误处理"""
        # 模拟内部错误
        with patch('src.api.v3.travel_planner.get_redis_client') as mock_get_redis:
            mock_get_redis.side_effect = Exception("Redis connection failed")
            
            response = self.client.get('/api/v3/travel-planner/health')
            
            assert response.status_code == 500
            data = json.loads(response.data)
            assert data['status'] == 'unhealthy'
            assert 'error' in data
    
    def test_request_data_validation(self):
        """测试请求数据验证"""
        # 测试有效请求数据
        valid_data = {
            "user_query": "我想去上海玩两天",
            "user_id": "test_user_001",
            "execution_mode": "automatic",
            "user_profile": {
                "age": 30,
                "interests": ["文化", "美食"]
            }
        }
        
        with patch('src.api.v3.travel_planner.get_redis_client') as mock_get_redis:
            mock_get_redis.return_value = self.mock_redis_client
            
            with patch('src.agents.travel_planner_lg.graph_v3.TravelPlannerGraphV3') as mock_graph_class:
                mock_graph = AsyncMock()
                mock_graph_class.return_value = mock_graph
                
                # 模拟简单的流响应
                async def simple_stream(input_data):
                    yield {"test": "data"}
                
                mock_graph.stream = simple_stream
                
                response = self.client.post(
                    '/api/v3/travel-planner/plan',
                    json=valid_data
                )
                
                # 应该返回SSE流
                assert response.status_code == 200
                assert response.mimetype == 'text/event-stream'
    
    def test_api_response_format(self):
        """测试API响应格式"""
        # 测试健康检查响应格式
        with patch('src.api.v3.travel_planner.get_redis_client') as mock_get_redis:
            mock_get_redis.return_value = self.mock_redis_client
            
            response = self.client.get('/api/v3/travel-planner/health')
            data = json.loads(response.data)
            
            # 验证必需字段
            required_fields = ['status', 'version', 'architecture', 'components', 'timestamp']
            for field in required_fields:
                assert field in data, f"Missing required field: {field}"
            
            # 验证时间戳格式
            assert 'T' in data['timestamp']  # ISO格式包含T
            assert 'Z' in data['timestamp'] or '+' in data['timestamp']  # UTC或时区信息
    
    def test_tools_endpoint_response_format(self):
        """测试工具端点响应格式"""
        response = self.client.get('/api/v3/travel-planner/tools')
        data = json.loads(response.data)
        
        # 验证工具注册表格式
        assert 'tool_registry' in data
        tool_registry = data['tool_registry']
        
        expected_fields = ['action_tools_count', 'planner_tools_count', 'action_tools', 'planner_tools']
        for field in expected_fields:
            assert field in tool_registry, f"Missing tool registry field: {field}"
        
        # 验证计数与列表长度一致
        assert tool_registry['action_tools_count'] == len(tool_registry['action_tools'])
        assert tool_registry['planner_tools_count'] == len(tool_registry['planner_tools'])


class TestSSEEventFormat:
    """测试SSE事件格式"""
    
    def test_event_data_structure(self):
        """测试事件数据结构"""
        # 模拟各种事件类型
        test_events = [
            {
                "event": "start",
                "data": {"task_id": "test_123"},
                "timestamp": "2024-01-01T00:00:00Z"
            },
            {
                "event": "node_complete",
                "data": {
                    "node_name": "framework_analysis",
                    "result": {"framework_analysis": {"core_intent": {}}},
                    "timestamp": "2024-01-01T00:00:01Z"
                }
            },
            {
                "event": "error",
                "data": {
                    "message": "Test error",
                    "error_code": "TEST_ERROR",
                    "timestamp": "2024-01-01T00:00:02Z"
                }
            }
        ]
        
        for event in test_events:
            # 验证基本结构
            assert "event" in event
            assert "data" in event or "timestamp" in event
            
            # 验证事件类型
            assert isinstance(event["event"], str)
            assert len(event["event"]) > 0
            
            # 验证时间戳格式（如果存在）
            if "timestamp" in event:
                timestamp = event["timestamp"]
                assert "T" in timestamp
                assert timestamp.endswith("Z") or "+" in timestamp
    
    def test_sse_format_conversion(self):
        """测试SSE格式转换"""
        event_data = {
            "event": "test_event",
            "data": {"message": "Hello, 世界!"},
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        # 转换为SSE格式
        sse_line = f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"
        
        # 验证格式
        assert sse_line.startswith("data: ")
        assert sse_line.endswith("\n\n")
        
        # 验证可以解析回原始数据
        json_part = sse_line[6:-2]  # 移除 "data: " 和 "\n\n"
        parsed_data = json.loads(json_part)
        assert parsed_data == event_data


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
