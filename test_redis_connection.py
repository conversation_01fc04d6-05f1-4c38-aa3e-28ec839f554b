#!/usr/bin/env python3
"""
测试Redis连接
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.redis_client import get_redis_client

async def test_redis():
    """测试Redis连接"""
    try:
        print("🔍 测试Redis连接...")
        client = await get_redis_client()
        print(f"✅ Redis连接成功: {client is not None}")
        
        # 测试基本操作
        if client:
            # 设置测试值
            await client.set("test_key", "test_value")
            value = await client.get("test_key")
            print(f"✅ Redis读写测试成功: {value}")
            
            # 清理测试数据
            await client.delete("test_key")
            print("✅ Redis测试完成")
        
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    asyncio.run(test_redis())
