"""
日志系统的单元测试

测试 src.core.logger 模块的功能。
"""
import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, mock_open
from io import StringIO


class TestStructuredLogger:
    """测试结构化日志记录器"""
    
    def test_logger_creation_with_default_config(self):
        """测试使用默认配置创建日志记录器"""
        from src.core.logger import StructuredLogger
        
        logger = StructuredLogger("test_module")
        
        assert logger.name == "test_module"
        assert logger.level == "INFO"
        assert logger.format == "json"
    
    def test_logger_creation_with_custom_config(self):
        """测试使用自定义配置创建日志记录器"""
        from src.core.logger import StructuredLogger
        
        logger = StructuredLogger(
            name="custom_module",
            level="DEBUG", 
            format="text",
            file_path="/tmp/test.log"
        )
        
        assert logger.name == "custom_module"
        assert logger.level == "DEBUG"
        assert logger.format == "text"
        assert logger.file_path == "/tmp/test.log"
    
    def test_info_logging_json_format(self):
        """测试JSON格式的信息日志"""
        from src.core.logger import StructuredLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            logger = StructuredLogger("test", format="json")
            logger.info("Test message", extra_field="extra_value")
            
            output = mock_stdout.getvalue().strip()
            log_data = json.loads(output)
            
            assert log_data["level"] == "INFO"
            assert log_data["message"] == "Test message"
            assert log_data["module"] == "test"
            assert log_data["extra_field"] == "extra_value"
            assert "timestamp" in log_data
    
    def test_text_format_logging(self):
        """测试文本格式的日志输出"""
        from src.core.logger import StructuredLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            logger = StructuredLogger("test", format="text")
            logger.info("Test message", user_id="123")
            
            output = mock_stdout.getvalue().strip()
            
            assert "INFO" in output
            assert "Test message" in output
            assert "test" in output
            assert "user_id=123" in output
    
    def test_debug_logging_with_level_filter(self):
        """测试日志级别过滤功能"""
        from src.core.logger import StructuredLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            # INFO 级别的日志器不应该输出 DEBUG 日志
            logger = StructuredLogger("test", level="INFO", format="json")
            logger.debug("Debug message")
            
            output = mock_stdout.getvalue().strip()
            assert output == ""  # 应该为空
            
            # 但应该输出 INFO 日志
            logger.info("Info message")
            output = mock_stdout.getvalue().strip()
            assert output != ""
    
    def test_error_logging_with_exception(self):
        """测试异常信息的错误日志"""
        from src.core.logger import StructuredLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            logger = StructuredLogger("test", format="json")
            
            try:
                raise ValueError("Test error")
            except Exception:
                logger.error("Error occurred", exc_info=True)
            
            output = mock_stdout.getvalue().strip()
            log_data = json.loads(output)
            
            assert log_data["level"] == "ERROR"
            assert log_data["message"] == "Error occurred"
            assert "exception" in log_data
            assert "ValueError" in log_data["exception"]
    
    def test_all_log_levels(self):
        """测试所有日志级别"""
        from src.core.logger import StructuredLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            logger = StructuredLogger("test", level="DEBUG", format="json")
            
            logger.debug("Debug message")
            logger.info("Info message")
            logger.warning("Warning message")
            logger.error("Error message")
            logger.critical("Critical message")
            
            output = mock_stdout.getvalue().strip()
            lines = output.split('\n')
            
            assert len(lines) == 5
            
            levels = []
            for line in lines:
                log_data = json.loads(line)
                levels.append(log_data["level"])
            
            assert levels == ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    
    def test_file_logging_output(self):
        """测试文件日志输出"""
        from src.core.logger import StructuredLogger
        
        with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.log') as temp_file:
            logger = StructuredLogger("test", file_path=temp_file.name)
            logger.info("Test file logging", request_id="req_123")
            
            # 读取文件内容
            with open(temp_file.name, 'r') as f:
                content = f.read()
            
            log_data = json.loads(content.strip())
            assert log_data["message"] == "Test file logging"
            assert log_data["request_id"] == "req_123"


class TestPerformanceLogger:
    """测试性能监控日志记录器"""
    
    def test_performance_context_manager(self):
        """测试性能监控上下文管理器"""
        from src.core.logger import PerformanceLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            perf_logger = PerformanceLogger("test")
            
            with perf_logger.measure("test_operation"):
                # 模拟一些操作
                import time
                time.sleep(0.01)  # 10ms
            
            output = mock_stdout.getvalue().strip()
            log_data = json.loads(output)
            
            assert log_data["level"] == "INFO"
            assert log_data["operation"] == "test_operation"
            assert "duration_ms" in log_data
            assert isinstance(log_data["duration_ms"], (int, float))
            assert log_data["duration_ms"] >= 10  # 至少10ms
    
    def test_performance_decorator(self):
        """测试性能监控装饰器"""
        from src.core.logger import PerformanceLogger
        
        perf_logger = PerformanceLogger("test")
        
        @perf_logger.monitor_performance
        def test_function(x, y):
            return x + y
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            result = test_function(1, 2)
            
            assert result == 3
            
            output = mock_stdout.getvalue().strip()
            log_data = json.loads(output)
            
            assert log_data["level"] == "INFO"
            assert log_data["operation"] == "test_function"
            assert "duration_ms" in log_data


class TestTraceLogger:
    """测试分布式追踪日志记录器"""
    
    def test_trace_context_creation(self):
        """测试追踪上下文创建"""
        from src.core.logger import TraceLogger
        
        with patch('sys.stdout', new_callable=StringIO):
            trace_logger = TraceLogger("test")
            trace_id = trace_logger.start_trace("user_request")
            
            assert isinstance(trace_id, str)
            assert len(trace_id) > 0
    
    def test_trace_logging_with_context(self):
        """测试带追踪上下文的日志记录"""
        from src.core.logger import TraceLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            trace_logger = TraceLogger("test")
            trace_id = trace_logger.start_trace("user_request")
            
            # 清空之前的输出
            mock_stdout.truncate(0)
            mock_stdout.seek(0)
            
            trace_logger.info("Processing request", step="validation")
            
            output = mock_stdout.getvalue().strip()
            log_data = json.loads(output)
            
            assert log_data["trace_id"] == trace_id
            assert log_data["trace_name"] == "user_request"
            assert log_data["step"] == "validation"
    
    def test_trace_end_logging(self):
        """测试追踪结束日志"""
        from src.core.logger import TraceLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            trace_logger = TraceLogger("test")
            trace_id = trace_logger.start_trace("user_request")
            
            import time
            time.sleep(0.01)  # 10ms
            
            # 清空之前的输出
            mock_stdout.truncate(0)
            mock_stdout.seek(0)
            
            trace_logger.end_trace()
            
            output = mock_stdout.getvalue().strip()
            log_data = json.loads(output)
            
            assert log_data["event"] == "trace_end"
            assert log_data["trace_id"] == trace_id
            assert "total_duration_ms" in log_data
            assert log_data["total_duration_ms"] >= 10
    
    def test_trace_error_logging(self):
        """测试追踪错误日志"""
        from src.core.logger import TraceLogger
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            trace_logger = TraceLogger("test")
            trace_id = trace_logger.start_trace("user_request")
            
            # 清空之前的输出
            mock_stdout.truncate(0)
            mock_stdout.seek(0)
            
            trace_logger.error("Request failed", error_code="500")
            
            output = mock_stdout.getvalue().strip()
            log_data = json.loads(output)
            
            assert log_data["level"] == "ERROR"
            assert log_data["trace_id"] == trace_id
            assert log_data["error_code"] == "500"


class TestLoggerFactory:
    """测试日志记录器工厂"""
    
    def test_get_logger_returns_same_instance(self):
        """测试工厂返回相同实例（单例模式）"""
        from src.core.logger import get_logger
        
        logger1 = get_logger("test_module")
        logger2 = get_logger("test_module")
        
        assert logger1 is logger2
    
    def test_get_logger_different_modules(self):
        """测试不同模块返回不同实例"""
        from src.core.logger import get_logger
        
        logger1 = get_logger("module1") 
        logger2 = get_logger("module2")
        
        assert logger1 is not logger2
        assert logger1.name == "module1"
        assert logger2.name == "module2"
    
    def test_get_performance_logger(self):
        """测试获取性能监控日志记录器"""
        from src.core.logger import get_performance_logger
        
        perf_logger = get_performance_logger("test_module")
        
        assert hasattr(perf_logger, 'measure')
        assert hasattr(perf_logger, 'monitor_performance')
    
    def test_get_trace_logger(self):
        """测试获取追踪日志记录器"""
        from src.core.logger import get_trace_logger
        
        trace_logger = get_trace_logger("test_module")
        
        assert hasattr(trace_logger, 'start_trace')
        assert hasattr(trace_logger, 'end_trace')
    
    def test_configure_logging_from_settings(self):
        """测试从配置文件加载日志设置"""
        from src.core.logger import configure_logging_from_settings
        from src.core.config import Settings
        
        # 模拟配置
        mock_settings = Settings(
            REASONING_LLM_MODEL="test_model",
            REASONING_LLM_API_KEY="test_key", 
            BASIC_LLM_MODEL="test_model",
            BASIC_LLM_API_KEY="test_key"
        )
        
        # 应该能成功配置日志系统
        configure_logging_from_settings(mock_settings)
        
        # 验证可以正常创建日志记录器
        from src.core.logger import get_logger
        logger = get_logger("test")
        assert logger is not None 