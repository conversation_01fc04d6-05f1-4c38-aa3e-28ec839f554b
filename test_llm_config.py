"""
测试LLM配置和连接的脚本

验证智谱AI的API配置是否正确，以及agent是否可以正常调用LLM进行规划。
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config import get_settings
from src.core.llm_manager import LLMManager, quick_chat


async def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("测试1: 配置加载")
    print("=" * 60)
    
    try:
        settings = get_settings()
        
        print("✓ 配置加载成功")
        print(f"  - 推理模型: {settings.reasoning_llm.model}")
        print(f"  - 基础模型: {settings.basic_llm.model}")
        print(f"  - 地图模型: {settings.map_llm.model}")
        print(f"  - API基础URL: {settings.reasoning_llm.base_url}")
        
        # 检查API Key是否存在（不显示完整内容）
        api_key = settings.reasoning_llm.api_key
        if api_key:
            print(f"  - API Key: {api_key[:10]}...{api_key[-4:]} (已配置)")
        else:
            print("  - API Key: 未配置")
            
        return True
        
    except Exception as e:
        print(f"✗ 配置加载失败: {str(e)}")
        return False


async def test_llm_connection():
    """测试LLM连接"""
    print("\n" + "=" * 60)
    print("测试2: LLM连接测试")
    print("=" * 60)
    
    try:
        manager = LLMManager()
        
        # 测试基础模型
        print("测试基础模型连接...")
        response = await manager.chat(
            message="你好，请简单回复一句话确认连接正常。",
            role="basic"
        )
        
        print("✓ 基础模型连接成功")
        print(f"  - 响应内容: {response.get('content', '')[:100]}...")
        print(f"  - 使用Token: {response.get('usage', {}).get('total_tokens', 0)}")
        
        # 测试推理模型
        print("\n测试推理模型连接...")
        response = await manager.chat(
            message="请用一句话说明你是什么模型。",
            role="reasoning"
        )
        
        print("✓ 推理模型连接成功")
        print(f"  - 响应内容: {response.get('content', '')[:100]}...")
        print(f"  - 使用Token: {response.get('usage', {}).get('total_tokens', 0)}")
        
        await manager.close_all()
        return True
        
    except Exception as e:
        print(f"✗ LLM连接失败: {str(e)}")
        return False


async def test_travel_planning_capability():
    """测试旅行规划能力"""
    print("\n" + "=" * 60)
    print("测试3: 旅行规划能力测试")
    print("=" * 60)
    
    try:
        # 测试简单的旅行规划推理
        test_query = """
        我想去北京玩3天，主要想看故宫、长城这些历史文化景点。
        请分析我的旅行意图，包括：
        1. 目的地
        2. 天数
        3. 旅行主题
        4. 主要兴趣点
        
        请用JSON格式回复，包含以上信息。
        """
        
        print("发送旅行规划测试查询...")
        response = await quick_chat(
            message=test_query,
            role="reasoning"
        )
        
        content = response.get('content', '')
        print("✓ 旅行规划推理成功")
        print(f"  - 响应长度: {len(content)} 字符")
        print(f"  - 使用Token: {response.get('usage', {}).get('total_tokens', 0)}")
        print(f"  - 响应内容预览:")
        print(f"    {content[:200]}...")
        
        # 尝试解析JSON响应
        try:
            # 查找JSON部分
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_content = json_match.group()
                parsed = json.loads(json_content)
                print("  - JSON解析成功:")
                for key, value in parsed.items():
                    print(f"    {key}: {value}")
            else:
                print("  - 未找到JSON格式响应，但推理正常")
        except:
            print("  - JSON解析失败，但推理正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 旅行规划能力测试失败: {str(e)}")
        return False


async def test_agent_initialization():
    """测试Agent初始化"""
    print("\n" + "=" * 60)
    print("测试4: Agent初始化测试")
    print("=" * 60)
    
    try:
        from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
        
        print("初始化TravelPlannerAgent...")
        agent = TravelPlannerAgentLangGraph()
        
        print("✓ Agent初始化成功")
        print(f"  - 图形对象: {type(agent.graph).__name__}")
        print(f"  - 流适配器: {type(agent.stream_adapter).__name__}")
        print(f"  - 交互钩子: {agent.enable_interaction_hooks}")
        
        # 测试图形可视化
        try:
            mermaid_graph = agent.get_graph_visualization()
            print("✓ 工作流图形生成成功")
            print(f"  - Mermaid图形长度: {len(mermaid_graph)} 字符")
        except Exception as e:
            print(f"  - 图形生成失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent初始化失败: {str(e)}")
        return False


async def test_simple_planning():
    """测试简单规划功能"""
    print("\n" + "=" * 60)
    print("测试5: 简单规划功能测试")
    print("=" * 60)
    
    try:
        from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
        
        agent = TravelPlannerAgentLangGraph()
        
        # 简单的用户画像
        user_profile = {
            "user_id": "test_user_001",
            "age_group": "25-35",
            "travel_style": "深度游",
            "budget_level": "中等",
            "interests": ["文化历史", "美食"],
            "preferred_languages": ["中文"]
        }
        
        query = "我想去北京玩2天，主要想看故宫"
        
        print(f"测试查询: {query}")
        print("开始规划...")
        
        start_time = datetime.now()
        result = await agent.plan_travel(
            user_id="test_user_001",
            query=query,
            user_profile=user_profile
        )
        end_time = datetime.now()
        
        print(f"✓ 规划完成! 耗时: {(end_time - start_time).total_seconds():.2f}秒")
        print(f"  - 状态: {result.get('status')}")
        print(f"  - 会话ID: {result.get('session_id')}")
        print(f"  - 规划模式: {result.get('planning_mode')}")
        
        if result.get('status') == 'completed':
            print("  - 规划成功!")
            
            # 显示核心意图
            if 'core_intent' in result:
                core_intent = result['core_intent']
                print(f"    目的地: {core_intent.get('destinations')}")
                print(f"    天数: {core_intent.get('days')}")
                print(f"    主题: {core_intent.get('travel_theme')}")
            
            # 显示行程数量
            if 'daily_itineraries' in result:
                itineraries = result['daily_itineraries']
                print(f"    生成行程: {len(itineraries)} 天")
        else:
            print(f"  - 规划失败: {result.get('error_message')}")
        
        return result.get('status') == 'completed'
        
    except Exception as e:
        print(f"✗ 简单规划测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def main():
    """主测试函数"""
    print("AutoPilot AI - LLM配置和Agent测试")
    print("=" * 60)
    print("本测试将验证:")
    print("1. 配置文件加载")
    print("2. 智谱AI API连接")
    print("3. LLM推理能力")
    print("4. Agent初始化")
    print("5. 简单规划功能")
    print("=" * 60)
    
    results = []
    
    try:
        # 运行所有测试
        results.append(await test_config_loading())
        results.append(await test_llm_connection())
        results.append(await test_travel_planning_capability())
        results.append(await test_agent_initialization())
        results.append(await test_simple_planning())
        
        # 总结结果
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        test_names = [
            "配置加载",
            "LLM连接",
            "旅行规划能力",
            "Agent初始化",
            "简单规划功能"
        ]
        
        passed = sum(results)
        total = len(results)
        
        for i, (name, result) in enumerate(zip(test_names, results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{i+1}. {name}: {status}")
        
        print(f"\n总体结果: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试通过! Agent可以正常使用真实LLM进行规划。")
        else:
            print("⚠️  部分测试失败，请检查配置和网络连接。")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
