# AutoPilot AI 测试指南

## 📁 测试文件结构

**测试目录已重新设计为分层结构，提供更清晰的测试分类：**

```
autopilotai/
├── tests/                     # 📁 所有测试文件统一目录
│   ├── integration/           # 🔗 集成测试 - 端到端测试
│   │   ├── test_agent_system.py      # 🤖 Agent系统集成测试
│   │   └── test_python_expert_integration.py  # 🐍 Python专家Agent专项测试
│   ├── unit/                  # 🧩 单元测试 - 模块级测试
│   │   ├── agents/            # Agent相关单元测试
│   │   ├── core/              # 核心模块单元测试
│   │   ├── llms/              # LLM相关测试
│   │   ├── tools/             # 工具测试
│   │   ├── memory/            # 记忆系统测试
│   │   ├── api/               # API测试
│   │   └── templates/         # 模板测试
│   ├── scripts/               # 📜 测试脚本和工具
│   │   └── test_all_agents.ps1    # 🔄 自动化测试脚本
│   └── README.md              # 📖 测试结构说明
├── run_tests.ps1              # ⚡ 简化测试运行器
└── doc/测试指南.md            # 📖 本文档
```

## 📋 目录
- [快速开始](#快速开始)
- [虚拟环境管理](#虚拟环境管理)
- [模块测试](#模块测试)
- [测试命令速查](#测试命令速查)
- [测试最佳实践](#测试最佳实践)
  - [什么是测试覆盖率？](#什么是测试覆盖率)
- [AutoGen Agent 测试](#-autogen-agent-测试)
- [常见问题解决](#常见问题解决)
- [性能测试](#性能测试)

---

## 🚀 快速开始

### 推荐测试流程

```powershell
# 1. 激活虚拟环境
.\.venv\Scripts\Activate.ps1

# 2. 快速测试（最简单方式）
.\run_tests.ps1 -Quick

# 3. 完整测试
.\run_tests.ps1

# 4. 或手动运行特定测试
python tests/integration/test_agent_system.py        # Agent集成测试
python tests/integration/test_python_expert_integration.py # 专家集成测试
.\tests\scripts\test_all_agents.ps1       # 完整自动化测试
```

---

## 🐍 虚拟环境管理

### 1. 虚拟环境基础操作

#### Windows PowerShell

```powershell
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
.\.venv\Scripts\Activate.ps1

# 确认激活成功（命令行前应显示 (.venv)）
python --version
pip --version

# 退出虚拟环境
deactivate
```

#### Windows Command Prompt

```cmd
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
.venv\Scripts\activate.bat

# 退出虚拟环境
deactivate
```

#### Linux/macOS

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
source .venv/bin/activate

# 退出虚拟环境
deactivate
```

### 2. 检查当前环境状态

```powershell
# 查看Python版本和路径
python --version
where python

# 查看已安装包
pip list

# 查看项目包信息
pip show autopilot-ai

# 检查虚拟环境是否激活
echo $env:VIRTUAL_ENV  # PowerShell
# 或
echo %VIRTUAL_ENV%     # CMD
```

### 3. 依赖管理

```powershell
# 安装项目依赖
pip install -e .

# 安装开发依赖
pip install -e ".[dev]"

# 更新requirements
pip freeze > requirements.txt

# 从requirements安装
pip install -r requirements.txt
```

---

## 🧪 模块测试

### 1. 项目结构概览

```
autopilotai/
├── src/
│   ├── core/           # 核心模块
│   │   ├── config.py
│   │   ├── logger.py
│   │   └── llm_manager.py
│   └── agents/         # Agent模块
│       ├── simple_assistant.py
│       └── python_expert.py
├── tests/              # 测试文件 - 分层结构
│   ├── integration/    # 集成测试
│   │   ├── test_agent_system.py
│   │   └── test_python_expert_integration.py
│   └── unit/           # 单元测试
│       ├── core/
│       │   ├── test_config.py
│       │   ├── test_logger.py
│       │   └── test_llm_manager.py
│       └── agents/
│           ├── test_simple_assistant.py
│           └── test_python_expert.py
└── run_tests.ps1       # 测试运行器
```

### 2. 单元测试

#### 运行所有测试

```powershell
# 运行所有单元测试（推荐）
pytest tests/unit/ -v

# 运行所有测试（包含集成测试）
pytest tests/ -v

# 显示测试覆盖率
pytest tests/unit/ --cov=src

# 生成HTML覆盖率报告
pytest tests/unit/ --cov=src --cov-report=html

# 运行特定模块测试
pytest tests/unit/core/test_logger.py

# 运行特定测试函数
pytest tests/unit/core/test_logger.py::TestStructuredLogger::test_basic_logging
```

#### 核心模块测试

```powershell
# 测试配置管理
pytest tests/unit/core/test_config.py -v

# 测试日志系统
pytest tests/unit/core/test_logger.py -v

# 测试LLM管理器
pytest tests/unit/core/test_llm_manager.py -v
```

### 3. 集成测试

#### Agent系统测试

AutoPilot AI提供了完整的Agent测试套件，涵盖基础功能、模型对比和专业领域测试：

```powershell
# 🚀 Agent基础功能测试（推荐首先运行）
python tests/integration/test_agent_system.py

# 🐍 Python专家Agent测试
python tests/integration/test_python_expert_integration.py
```

**test_agent_system.py 测试内容：**
- ✅ 基础功能测试：验证Agent创建、对话、历史管理
- ✅ 基础模型测试：测试 glm-4-flash 模型的日常对话能力
- ✅ 思考模型测试：测试 glm-z1-flash 模型的复杂推理能力
- ✅ 模型对比测试：比较两种模型的性能和特点

**test_python_expert_integration.py 测试内容：**
- 🔍 代码分析功能：语法检查、模式识别、复杂度评估
- ⚡ 优化建议：代码改进建议和最佳实践检查
- 📚 库推荐：基于任务的Python库推荐
- 💬 专家对话：带代码分析的专业咨询
- 🐛 错误解释：Python错误信息的详细解释
- 🚀 性能建议：针对不同场景的性能优化建议
- ⚡ 快速帮助：Python问题的快速回答
- 🔄 模型使用：验证专家Agent使用思考模型

#### 模型配置说明

AutoPilot AI支持两种智谱AI模型：

**基础模型 (glm-4-flash)：**
- 用途：日常对话、简单问答、基础任务
- 特点：响应速度快、成本较低
- 适用场景：客服对话、简单咨询、常规交互

**思考模型 (glm-z1-flash)：**
- 用途：复杂推理、深度分析、专业咨询
- 特点：思考过程详细、回答深入、逻辑性强
- 适用场景：代码分析、技术咨询、复杂问题解决

#### 快速测试命令

```powershell
# 🔥 完整Agent测试流程
# 1. 激活环境
.\.venv\Scripts\Activate.ps1

# 2. 运行基础Agent测试
python tests/integration/test_agent_system.py

# 3. 运行专家Agent测试
python tests/integration/test_python_expert_integration.py

# 4. 运行单元测试验证
pytest tests/unit/agents/ -v

# 一键测试脚本（使用 tests/scripts/test_all_agents.ps1）
.\tests\scripts\test_all_agents.ps1
```

#### 自定义测试脚本

```powershell
# 测试基础模型
python -c "
import asyncio
import os
os.environ['BASIC_LLM_MODEL'] = 'glm-4-flash'
os.environ['BASIC_LLM_API_KEY'] = 'your-api-key'
os.environ['BASIC_LLM_BASE_URL'] = 'https://open.bigmodel.cn/api/paas/v4/'
from src.agents.simple_assistant import quick_chat
result = asyncio.run(quick_chat('你好，请介绍一下你自己'))
print(f'基础模型回复: {result}')
"

# 测试思考模型
python -c "
import asyncio
import os
os.environ['REASONING_LLM_MODEL'] = 'glm-z1-flash'
os.environ['REASONING_LLM_API_KEY'] = 'your-api-key'
os.environ['REASONING_LLM_BASE_URL'] = 'https://open.bigmodel.cn/api/paas/v4/'
from src.agents.simple_assistant import create_simple_assistant
async def test():
    agent = await create_simple_assistant('思考测试', 'reasoning')
    result = await agent.chat('请分析一下云计算的发展趋势')
    print(f'思考模型回复: {result[\"assistant_reply\"][:200]}...')
asyncio.run(test())
"

# 测试Python专家
python -c "
import asyncio
import os
os.environ['REASONING_LLM_MODEL'] = 'glm-z1-flash'
os.environ['REASONING_LLM_API_KEY'] = 'your-api-key'
os.environ['REASONING_LLM_BASE_URL'] = 'https://open.bigmodel.cn/api/paas/v4/'
from src.agents.python_expert import quick_python_help
code = 'for i in range(len(items)): print(items[i])'
result = asyncio.run(quick_python_help('如何优化这段代码？', code))
print(f'Python专家建议: {result}')
"
```

### 4. 环境变量配置测试

#### 设置测试环境变量

```powershell
# 设置智谱AI配置（测试用）
# 思考模型配置（用于复杂推理和分析）
$env:REASONING_LLM_MODEL = "glm-z1-flash"
$env:REASONING_LLM_API_KEY = "your-api-key"
$env:REASONING_LLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4/"

# 基础模型配置（用于日常对话）
$env:BASIC_LLM_MODEL = "glm-4-flash"
$env:BASIC_LLM_API_KEY = "your-api-key"
$env:BASIC_LLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4/"

# 日志配置
$env:LOG_LEVEL = "INFO"
$env:LOG_FORMAT = "text"

# 验证环境变量
echo "思考模型: $env:REASONING_LLM_MODEL"
echo "基础模型: $env:BASIC_LLM_MODEL"
```

#### 使用.env文件（推荐）

创建 `.env` 文件：

```bash
# LLM配置 - 双模型支持
# 思考模型：用于复杂推理、代码分析、专业咨询
REASONING_LLM_MODEL=glm-z1-flash
REASONING_LLM_API_KEY=your-api-key-here
REASONING_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

# 基础模型：用于日常对话、简单问答
BASIC_LLM_MODEL=glm-4-flash
BASIC_LLM_API_KEY=your-api-key-here
BASIC_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=text

# 可选：性能监控配置
ENABLE_PERFORMANCE_LOGGING=true
ENABLE_TRACE_LOGGING=true
```

---

## 🤖 Agent系统专项测试

### 1. Agent测试架构

AutoPilot AI采用分层Agent架构，支持多模型、多角色的智能助手系统：

```
Agent系统架构
├── 基础层：SimpleAssistant
│   ├── 基础模型 (glm-4-flash)：日常对话
│   └── 思考模型 (glm-z1-flash)：复杂推理
├── 专业层：PythonExpert
│   ├── 代码分析引擎
│   ├── 优化建议系统
│   └── 专业知识库
└── 测试层：完整测试覆盖
    ├── 单元测试 (pytest tests/unit/)
    ├── 集成测试 (tests/integration/)
    └── 自动化脚本 (tests/scripts/)
```

### 2. 双模型测试策略

#### 基础模型 (glm-4-flash) 测试重点

```powershell
# 特性测试
- 响应速度：< 3秒
- 对话质量：自然流畅
- 上下文理解：5轮对话保持连贯
- 资源消耗：低token使用

# 测试场景
python -c "
import asyncio
from src.agents.simple_assistant import create_simple_assistant
async def test_basic():
    agent = await create_simple_assistant('基础测试', 'basic')
    result = await agent.chat('你好，请介绍一下Python的特点')
    print(f'模型: {result.get(\"model_used\")}')
    print(f'响应: {result[\"assistant_reply\"][:100]}...')
asyncio.run(test_basic())
"
```

#### 思考模型 (glm-z1-flash) 测试重点

```powershell
# 特性测试
- 推理深度：多层次逻辑分析
- 内容质量：专业详细的回答
- 思考过程：可见的推理步骤
- 复杂度处理：技术问题深度解析

# 测试场景
python -c "
import asyncio
from src.agents.simple_assistant import create_simple_assistant
async def test_reasoning():
    agent = await create_simple_assistant('思考测试', 'reasoning')
    result = await agent.chat('请分析微服务架构的优缺点及适用场景')
    print(f'模型: {result.get(\"model_used\")}')
    print(f'Token使用: {result[\"tokens_used\"][\"total_tokens\"]}')
    print(f'响应质量: {\"深度分析\" if len(result[\"assistant_reply\"]) > 1000 else \"简单回答\"}')
asyncio.run(test_reasoning())
"
```

### 3. Agent测试场景库

#### 功能测试场景

```powershell
# 对话连贯性测试
python tests/integration/test_agent_system.py  # 包含多轮对话测试

# 专业能力测试
python tests/integration/test_python_expert_integration.py  # 代码分析专业测试

# 性能对比测试
python -c "
import asyncio
import time
from src.agents.simple_assistant import create_simple_assistant

async def performance_test():
    question = '解释一下什么是RESTful API'
    
    # 基础模型测试
    start = time.time()
    basic = await create_simple_assistant('basic_perf', 'basic')
    basic_result = await basic.chat(question)
    basic_time = time.time() - start
    
    # 思考模型测试
    start = time.time()
    reasoning = await create_simple_assistant('reasoning_perf', 'reasoning')
    reasoning_result = await reasoning.chat(question)
    reasoning_time = time.time() - start
    
    print(f'基础模型 - 时间: {basic_time:.2f}s, 长度: {len(basic_result[\"assistant_reply\"])}')
    print(f'思考模型 - 时间: {reasoning_time:.2f}s, 长度: {len(reasoning_result[\"assistant_reply\"])}')

asyncio.run(performance_test())
"
```

#### 错误处理测试

```powershell
# API错误测试
python -c "
import asyncio
import os
os.environ['BASIC_LLM_API_KEY'] = 'invalid_key'
from src.agents.simple_assistant import quick_chat
try:
    result = asyncio.run(quick_chat('测试'))
    print('❌ 应该失败但成功了')
except Exception as e:
    print(f'✅ 正确处理错误: {type(e).__name__}')
"

# 网络超时测试
python -c "
import asyncio
import os
os.environ['BASIC_LLM_BASE_URL'] = 'http://invalid-url.com'
from src.agents.simple_assistant import quick_chat
try:
    result = asyncio.run(quick_chat('测试'))
    print('❌ 应该失败但成功了')
except Exception as e:
    print(f'✅ 正确处理网络错误: {type(e).__name__}')
"
```

### 4. 自动化测试脚本

完整的自动化测试脚本位于 `tests/scripts/test_all_agents.ps1`，支持多种测试模式：

```powershell
# 完整测试
.\tests\scripts\test_all_agents.ps1

# 快速测试
.\tests\scripts\test_all_agents.ps1 -Quick

# 性能测试
.\tests\scripts\test_all_agents.ps1 -Performance

# 覆盖率测试
.\tests\scripts\test_all_agents.ps1 -Coverage
```

---

## ⚡ 测试命令速查

### 快速测试命令

```powershell
# 🔥 一键测试套件
pytest tests/ -v --cov=src --cov-report=term-missing

# 🧪 单元测试专项
pytest tests/unit/ -v --cov=src --cov-report=term-missing

# 🔗 集成测试专项
pytest tests/integration/ -v

# 🚀 快速模块验证
python -c "from src.core.config import get_settings; print('配置模块正常:', get_settings().project_name)"

# 🧠 Agent快速测试
python -c "
import asyncio
import os
os.environ['BASIC_LLM_MODEL'] = 'gpt-3.5-turbo'
os.environ['BASIC_LLM_API_KEY'] = 'test-key'
from src.agents.simple_assistant import quick_chat
try:
    result = asyncio.run(quick_chat('Hello'))
    print('Agent测试成功')
except Exception as e:
    print(f'Agent测试失败: {e}')
"

# 📊 日志系统测试
python -c "
from src.core.logger import get_logger
logger = get_logger('test')
logger.info('日志系统测试', test_data='success')
print('日志系统正常')
"
```

### 开发测试流程

```powershell
# 1. 激活环境
.\.venv\Scripts\Activate.ps1

# 2. 运行单元测试
pytest tests/unit/ -v

# 3. 运行集成测试
pytest tests/integration/ -v

# 4. 运行Agent专项测试
python tests/integration/test_agent_system.py

# 5. 运行专家Agent测试
python tests/integration/test_python_expert_integration.py

# 6. 检查代码质量
flake8 src/
black src/ --check

# 7. 类型检查（如果使用mypy）
mypy src/
```

---

## ✅ 测试最佳实践

### 1. 什么是测试覆盖率？

**测试覆盖率 (Test Coverage)** 是一种度量标准，用于衡量在运行测试套件时，有多少比例的源代码被执行。它能精确地告诉我们，哪些代码行、逻辑分支或函数被测试过，哪些没有。

#### 为什么测试覆盖率很重要？

1.  **🎯 发现未经测试的代码**：
    覆盖率报告可以清晰地指出代码库中未经测试的"盲点"，帮助我们补充测试用例，防止潜在的bug。

2.  **提升测试质量**：
    追求更高的覆盖率会促使我们思考更多的边界条件和异常情况，从而编写出更健的测试。

3.  **降低回归风险**：
    在重构或修改代码时，覆盖率报告可以确保所有逻辑路径在修改后仍然被测试，防止引入新的bug。

4.  **提供质量信心**：
    一个高的测试覆盖率（例如，本项目要求核心模块达到90%以上）是代码质量的重要保证，让我们能更有信心地发布新版本。

#### 如何运行覆盖率测试？

我们使用 `pytest-cov` 插件来生成覆盖率报告。

```powershell
# 1. 运行单元测试并生成终端报告
# --cov=src 指定要计算覆盖率的源码目录
# --cov-report=term-missing 显示覆盖率摘要和未覆盖的代码行号
pytest tests/unit/ -v --cov=src --cov-report=term-missing

# 2. 生成详细的HTML报告
# 这会创建一个 `htmlcov/` 目录，里面有可交互的网页报告
pytest tests/unit/ -v --cov=src --cov-report=html
```

#### 如何解读覆盖率报告？

终端报告通常包含以下几列：
- **`Name`**: 模块文件名
- **`Stmts`**: 总语句数 (Statements)
- **`Miss`**: 未被执行的语句数
- **`Cover`**: 覆盖率 (100% - Miss / Stmts)
- **`Missing`**: 未被覆盖的具体行号

**HTML报告** (`htmlcov/index.html`) 提供了更直观的视图，您可以点击文件查看每一行代码的覆盖情况（绿色表示已覆盖，红色表示未覆盖）。

### 2. 测试环境隔离

```powershell
# 为不同环境创建不同的.env文件
# .env.test - 测试环境
# .env.dev - 开发环境
# .env.prod - 生产环境

# 使用特定环境配置
$env:ENV_FILE = ".env.test"
pytest
```

### 3. 测试数据管理

```python
# 使用pytest fixtures管理测试数据
import pytest

@pytest.fixture
def test_config():
    return {
        "llm_model": "test-model",
        "api_key": "test-key"
    }

def test_agent_creation(test_config):
    # 使用测试配置
    pass
```

### 4. Mock外部依赖

```python
from unittest.mock import patch, AsyncMock

@patch('src.core.llm_manager.LLMClient')
async def test_agent_without_api_call(mock_client):
    mock_client.return_value.chat = AsyncMock(return_value={"content": "test"})
    # 测试逻辑
```

### 5. 测试覆盖率目标

- **最低覆盖率**: 80%
- **核心模块**: 95%以上
- **Agent模块**: 90%以上

### 6. 持续集成测试

```yaml
# .github/workflows/test.yml 示例
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.13
      - name: Install dependencies
        run: pip install -e ".[dev]"
      - name: Run tests
        run: pytest tests/unit/ --cov=src --cov-report=xml
```

---

## 🚨 常见问题解决

### 1. 虚拟环境问题

**问题**: PowerShell执行策略限制
```powershell
# 解决方案：设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者使用绝对路径
& "D:\code\pythonWork\autopilotai\.venv\Scripts\Activate.ps1"
```

**问题**: 虚拟环境路径包含空格
```powershell
# 使用引号包围路径
& ".\.venv\Scripts\Activate.ps1"
```

### 2. 模块导入问题

**问题**: `ModuleNotFoundError: No module named 'src'`
```powershell
# 解决方案1：安装包为开发模式
pip install -e .

# 解决方案2：添加到PYTHONPATH
$env:PYTHONPATH = "$pwd;$env:PYTHONPATH"

# 解决方案3：在测试中添加路径
python -c "import sys; sys.path.append('.'); import src.core.config"
```

### 3. 环境变量问题

**问题**: 环境变量未生效
```powershell
# 检查环境变量
Get-ChildItem Env: | Where-Object Name -like "*LLM*"

# 重新设置
$env:REASONING_LLM_MODEL = "glm-z1-flash"

# 确认设置
echo $env:REASONING_LLM_MODEL
```

### 4. 依赖版本冲突

```powershell
# 检查依赖冲突
pip check

# 重新安装依赖
pip install --force-reinstall -e .

# 清理缓存
pip cache purge
```

### 5. 测试超时问题

```powershell
# 设置pytest超时
pytest --timeout=300 tests/

# 或在pytest.ini中设置
[tool:pytest]
timeout = 300
```

---

## 🔌 AutoGen Agent 测试

### 🌟 AutoGen 测试概述

AutoPilot AI 已完成与 Microsoft AutoGen 框架的深度集成，提供了多种测试AutoGen agent的方式。AutoGen集成允许您使用现有的智谱AI配置创建多Agent协作团队。

### 📝 使用自定义测试文件 `test_my_autogen.py`

我们提供了一个专门的AutoGen测试文件，支持多种测试模式：

#### 1. 🚀 基础功能测试

```powershell
# 运行基础功能测试（不需要API密钥）
python test_my_autogen.py

# 输出示例：
# 🎯 AutoGen Agent 自定义测试
# ============================================================
# 
# 🔍 环境检查
# ==================================================
# AutoGen 可用性: ✅ 可用
# ✅ AutoGen集成实例创建成功
# 
# 🧪 基础功能测试
# ==================================================
# ✅ 基础客户端创建成功
# ✅ 推理客户端创建成功
# ✅ 基础助手创建成功: 测试助手
# ✅ 推理助手创建成功: 推理助手
# 
# 🎯 总体结果: 1/1 项测试通过
# 🎉 所有测试通过！AutoGen集成工作正常
```

这个测试会验证：
- ✅ AutoGen 库是否正确安装
- ✅ AutoGen 集成适配器是否工作正常
- ✅ 基础模型客户端创建（glm-4-flash）
- ✅ 推理模型客户端创建（glm-z1-flash）
- ✅ AutoGen Assistant Agent 创建

#### 2. 💬 真实API对话测试

```powershell
# 配置真实API密钥后，运行完整测试
python test_my_autogen.py --with-api

# 这会额外测试：
# - 基础模型对话能力
# - 推理模型对话能力
# - 性能对比分析
```

**前提条件**: 需要在 `.env` 文件中配置真实的智谱AI API密钥：

```env
# .env 文件配置
BASIC_LLM_MODEL=glm-4-flash
BASIC_LLM_API_KEY=your-actual-api-key-here
BASIC_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4

REASONING_LLM_MODEL=glm-z1-flash
REASONING_LLM_API_KEY=your-actual-api-key-here
REASONING_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4
```

#### 3. 🎮 交互式聊天测试

```powershell
# 启动交互式聊天模式
python test_my_autogen.py --interactive

# 交互命令：
# - 'quit'   - 退出聊天
# - 'switch' - 切换模型 (basic ↔ reasoning)
# - 'status' - 查看当前状态
# - 'help'   - 显示帮助
```

交互式模式让您可以：
- 🔄 实时切换基础模型（glm-4-flash）和推理模型（glm-z1-flash）
- 💬 体验两种模型的不同响应风格
- 📊 对比模型的响应速度和内容质量

#### 4. 📊 综合测试模式

```powershell
# 运行所有测试并启动交互模式
python test_my_autogen.py --with-api --interactive

# 这会依次执行：
# 1. 环境检查
# 2. 基础功能测试
# 3. 对话功能测试
# 4. 性能对比测试
# 5. 交互式聊天模式
```

### 🧪 使用现有测试套件

#### 1. 官方集成测试

```powershell
# 运行官方AutoGen集成测试
.\tests\scripts\test_all_agents.ps1 -AutoGen

# 或直接运行
python tests/integration/test_autogen_agent_integration.py
```

#### 2. AutoGen适配器单元测试

```powershell
# 运行AutoGen适配器单元测试（使用Mock，不需要API）
pytest tests/unit/integration/test_autogen_adapter.py -v

# 输出示例：
# tests/unit/integration/test_autogen_adapter.py::TestAutoGenIntegrationAdapter::test_adapter_imports_with_autogen_available PASSED
# tests/unit/integration/test_autogen_adapter.py::TestAutoGenIntegrationAdapter::test_create_model_client_basic_config PASSED
# ... (17个测试全部通过)
```

### 🛠️ 故障排除

#### 问题1: AutoGen库未安装

```
❌ AutoGen 可用性: ❌ 不可用
💡 安装命令: pip install autogen-agentchat[all]
```

**解决方案**：
```powershell
pip install autogen-agentchat[all]
```

#### 问题2: API密钥配置错误

```
⚠️ 警告: 使用测试API密钥，实际对话功能不可用
```

**解决方案**：
1. 创建或编辑 `.env` 文件
2. 配置真实的智谱AI API密钥
3. 重新运行测试

#### 问题3: 模型配置错误

```
❌ 基础功能测试失败: validation errors
```

**解决方案**：
1. 检查环境变量设置
2. 确认模型名称正确（glm-4-flash/glm-z1-flash）
3. 验证API端点URL

### 📋 AutoGen测试最佳实践

#### 1. 测试顺序建议

```powershell
# 1. 首先运行基础测试（无需API密钥）
python test_my_autogen.py

# 2. 配置API密钥后运行完整测试
python test_my_autogen.py --with-api

# 3. 通过交互模式体验效果
python test_my_autogen.py --interactive

# 4. 运行单元测试验证逻辑
pytest tests/unit/integration/test_autogen_adapter.py -v
```

#### 2. 模型选择指南

**基础模型 (glm-4-flash)**：
- ✅ 适用场景：日常对话、简单问答、快速响应
- ⚡ 特点：响应速度快、token消耗少
- 🎯 测试重点：响应速度、对话连贯性

**推理模型 (glm-z1-flash)**：
- ✅ 适用场景：复杂分析、技术咨询、深度思考
- 🧠 特点：推理详细、回答深入、逻辑性强
- 🎯 测试重点：分析深度、推理质量

#### 3. 性能测试技巧

```powershell
# 性能对比测试示例
python -c "
import asyncio
import time
from test_my_autogen import AutoGenTester

async def perf_test():
    tester = AutoGenTester()
    await tester.check_environment()
    
    question = '请分析云计算的发展趋势'
    
    # 测试基础模型
    start = time.time()
    basic_resp = await tester.integration.simple_chat_test(question, 'basic')
    basic_time = time.time() - start
    
    # 测试推理模型
    start = time.time()
    reasoning_resp = await tester.integration.simple_chat_test(question, 'reasoning')
    reasoning_time = time.time() - start
    
    print(f'基础模型: {basic_time:.2f}s, {len(basic_resp)}字符')
    print(f'推理模型: {reasoning_time:.2f}s, {len(reasoning_resp)}字符')

asyncio.run(perf_test())
"
```

### 🔗 AutoGen集成架构

AutoGen集成采用适配器模式，完全复用现有配置系统：

```
现有系统               AutoGen集成
┌─────────────┐       ┌─────────────────┐
│ LLMConfig   │  ──>  │ AutoGenClient   │
│ (basic)     │       │ (glm-4-flash)   │
└─────────────┘       └─────────────────┘

┌─────────────┐       ┌─────────────────┐
│ LLMConfig   │  ──>  │ AutoGenClient   │
│ (reasoning) │       │ (glm-z1-flash)  │
└─────────────┘       └─────────────────┘
                              │
                              ▼
                      ┌─────────────────┐
                      │ AssistantAgent  │
                      │ (AutoGen)       │
                      └─────────────────┘
```

### 🎯 测试检查清单

#### AutoGen测试前检查

- [ ] AutoGen库已安装 `pip list | grep autogen`
- [ ] 虚拟环境已激活 `(.venv)` 显示在命令行
- [ ] 基础测试通过 `python test_my_autogen.py`
- [ ] API密钥已配置（可选，仅实际对话需要）

#### AutoGen功能验证

- [ ] 基础模型客户端创建成功
- [ ] 推理模型客户端创建成功
- [ ] AutoGen Assistant创建成功
- [ ] 与现有Agent系统共存
- [ ] 配置系统正确转换

#### AutoGen对话测试（需要API密钥）

- [ ] 基础模型对话正常
- [ ] 推理模型对话正常
- [ ] 模型切换功能正常
- [ ] 性能指标合理

---

## 📈 性能测试

### 1. Agent响应时间测试

```python
import time
import asyncio
from src.agents.simple_assistant import quick_chat

async def benchmark_agent():
    start = time.time()
    result = await quick_chat("简单测试")
    end = time.time()
    print(f"响应时间: {end - start:.2f}秒")
    print(f"回复长度: {len(result)}字符")

# 运行测试
asyncio.run(benchmark_agent())
```

### 2. 并发性能测试

```python
import asyncio
from src.agents.simple_assistant import create_simple_assistant

async def concurrent_test():
    assistant = await create_simple_assistant("perf_test")
    
    tasks = []
    for i in range(10):
        task = assistant.chat(f"测试消息 {i}")
        tasks.append(task)
    
    start = time.time()
    results = await asyncio.gather(*tasks)
    end = time.time()
    
    print(f"10个并发请求完成时间: {end - start:.2f}秒")
    print(f"平均响应时间: {(end - start) / 10:.2f}秒")

asyncio.run(concurrent_test())
```

### 3. 内存使用监控

```python
import psutil
import os

def monitor_memory():
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    print(f"内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")

# 在测试前后调用
monitor_memory()
# ... 运行测试 ...
monitor_memory()
```

---

## 📝 测试报告生成

### 1. 生成详细测试报告

```powershell
# 生成HTML测试报告
pytest --html=reports/test_report.html --self-contained-html

# 生成覆盖率报告
pytest tests/unit/ --cov=src --cov-report=html:reports/coverage/

# 生成JUnit格式报告（CI/CD用）
pytest --junitxml=reports/junit.xml
```

### 2. 自动化测试脚本

创建 `run_tests.ps1`:

```powershell
#!/usr/bin/env powershell
# AutoPilot AI 自动化测试脚本

Write-Host "🚀 启动AutoPilot AI测试套件" -ForegroundColor Green

# 检查虚拟环境
if (-not $env:VIRTUAL_ENV) {
    Write-Host "❌ 请先激活虚拟环境" -ForegroundColor Red
    Write-Host "运行: .\.venv\Scripts\Activate.ps1" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ 虚拟环境已激活: $env:VIRTUAL_ENV" -ForegroundColor Green

# 创建报告目录
New-Item -ItemType Directory -Force -Path "reports"

Write-Host "🧪 运行单元测试..." -ForegroundColor Blue
pytest tests/unit/ -v --cov=src --cov-report=html:reports/coverage/ --html=reports/test_report.html --self-contained-html

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 单元测试通过" -ForegroundColor Green
} else {
    Write-Host "❌ 单元测试失败" -ForegroundColor Red
    exit 1
}

Write-Host "🤖 运行Agent集成测试..." -ForegroundColor Blue
python tests/integration/test_agent_system.py

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Agent测试通过" -ForegroundColor Green
} else {
    Write-Host "❌ Agent测试失败" -ForegroundColor Red
    exit 1
}

Write-Host "🐍 运行Python专家测试..." -ForegroundColor Blue
python tests/integration/test_python_expert_integration.py

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Python专家测试通过" -ForegroundColor Green
} else {
    Write-Host "❌ Python专家测试失败" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 所有测试完成！" -ForegroundColor Green
Write-Host "📊 查看测试报告: reports/test_report.html" -ForegroundColor Cyan
Write-Host "📈 查看覆盖率报告: reports/coverage/index.html" -ForegroundColor Cyan
```

运行自动化测试：

```powershell
# 给脚本执行权限并运行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\run_tests.ps1
```

---

## 🎯 测试检查清单

### 开发前检查

- [ ] 虚拟环境已激活 `(.venv)` 显示在命令行
- [ ] 依赖已安装 `pip list` 包含项目依赖
- [ ] 环境变量已设置 `echo $env:REASONING_LLM_MODEL`
- [ ] 基础模块可导入 `python -c "import src.core.config"`

### 代码提交前检查

- [ ] 单元测试通过 `pytest tests/unit/ -v`
- [ ] 测试覆盖率达标 `pytest tests/unit/ --cov=src --cov-fail-under=80`
- [ ] 集成测试通过 `pytest tests/integration/ -v`
- [ ] Agent测试通过 `python tests/integration/test_agent_system.py`
- [ ] 代码格式规范 `black src/ --check`
- [ ] 类型检查通过 `mypy src/` (如果使用)

### 发布前检查

- [ ] 所有测试套件通过 `.\run_tests.ps1`
- [ ] 性能测试达标
- [ ] 文档更新完整
- [ ] 变更日志记录

---

**🔗 相关文档链接:**
- [开发环境设置指南.md](开发环境设置指南.md)
- [Agent技术实现规范.md](Agent%20技术实现规范.md)
- [进度文档.md](进度文档.md)

**📞 需要帮助?**
如遇到测试问题，请检查本文档的常见问题部分，或查看项目logs目录下的详细日志。特别注意新的分层测试结构：单元测试在 `tests/unit/`，集成测试在 `tests/integration/`，测试脚本在 `tests/scripts/`。 