"""
Web application for testing tools without AutoGen integration
"""
from flask import Flask, render_template, request, jsonify, Response
import os
import requests
import argparse
import asyncio
from tools.Amap.map_tool import MapTool, Location
from tools.NetEaseCloudMusic.music_tool import MusicTool

app = Flask(__name__)

# Initialize tools with API keys from environment variables
map_tool = MapTool("")

music_tool = MusicTool()

@app.route('/')
def index():
    return render_template('index.html')

# Map Tool Routes
@app.route('/api/map/search_pois', methods=['POST'])
def search_pois():
    try:
        data = request.json
        keywords = data.get('keywords')
        city = data.get('city')
        type_ = data.get('type')
        
        # Handle location if provided
        location = None
        if data.get('latitude') and data.get('longitude'):
            location = Location(
                latitude=float(data.get('latitude')),
                longitude=float(data.get('longitude'))
            )
            
        results = map_tool.search_pois(
            keywords=keywords,
            city=city,
            type_=type_,
            location=location,
            radius=int(data.get('radius', 3000))
        )
        
        # Convert POIResult objects to dict for JSON response
        return jsonify([{
            'id': poi.id,
            'name': poi.name,
            'type': poi.type,
            'address': poi.address,
            'location': {
                'latitude': poi.location.latitude,
                'longitude': poi.location.longitude
            },
            'distance': poi.distance,
            'rating': poi.rating,
            'price': poi.price
        } for poi in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/map/get_route', methods=['POST'])
def get_route():
    try:
        data = request.json
        origin = Location(
            latitude=float(data['origin']['latitude']),
            longitude=float(data['origin']['longitude'])
        )
        destination = Location(
            latitude=float(data['destination']['latitude']),
            longitude=float(data['destination']['longitude'])
        )
        
        # Handle waypoints if provided
        waypoints = None
        if data.get('waypoints'):
            waypoints = [
                Location(
                    latitude=float(wp['latitude']),
                    longitude=float(wp['longitude'])
                )
                for wp in data['waypoints']
            ]
            
        route = map_tool.get_route(
            origin=origin,
            destination=destination,
            waypoints=waypoints,
            transport_mode=data.get('transport_mode', 'driving')
        )
        
        return jsonify(route)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

# LLM and Music Tool Routes
@app.route('/api/music/get_keyword', methods=['POST'])
def get_keyword():
    try:
        data = request.json
        prompt = data['prompt']
        
        # Prepare the prompt for LLM
        messages = [
            {"role": "system", "content": "你是一个音乐推荐助手。用户会描述他想听的音乐类型，你需要根据描述提取一个最相关的关键词用于搜索音乐。只返回关键词，不要其他解释。"},
            {"role": "user", "content": prompt}
        ]
        
        # Call LLM API
        llm_response = requests.post(
            "http://27.159.93.61:8007/v1/chat/completions",
            json={
                "model": "Qwen2.5-32B-Instruct-AWQ",
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 50,
            }
        )
        
        llm_data = llm_response.json()
        if "error" in llm_data:
            raise Exception(f"LLM API Error: {llm_data['error']}")
            
        # Extract keyword from LLM response
        keyword = llm_data["choices"][0]["message"]["content"].strip()
        return jsonify({"keyword": keyword})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/search_songs', methods=['POST'])
def search_songs():
    try:
        data = request.json        
        results = music_tool.search_songs(
            keyword=data['keyword']
        )
        
        # Convert Song objects to dict for JSON response
        return jsonify([{
            'id': song.id,
            'name': song.name,
            'artist': song.artist,
            'album': song.album,
            'duration': song.duration,
            'url': song.url
        } for song in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/search_songs_by_artist', methods=['POST'])
def search_songs_by_artist():
    try:
        data = request.json        
        results = music_tool.search_songs_by_artist(
            keyword=data['keyword']
        )
        
        return jsonify([{
            'id': song.id,
            'name': song.name,
            'artist': song.artist,
            'album': song.album,
            'duration': song.duration,
            'url': song.url
        } for song in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/search_songs_by_artist_and_name', methods=['POST'])
def search_songs_by_artist_and_name():
    try:
        data = request.json        
        results = music_tool.search_songs_by_artist_and_name(
            artistName=data['artistName'],
            songName=data['songName']
        )
        
        return jsonify([{
            'id': song.id,
            'name': song.name,
            'artist': song.artist,
            'album': song.album,
            'duration': song.duration,
            'url': song.url
        } for song in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/search_playlists', methods=['POST'])
def search_playlists():
    try:
        data = request.json        
        results = music_tool.search_playlists(
            keyword=data['keyword']
        )
        
        return jsonify([{
            'id': playlist.id,
            'name': playlist.name,
            'description': playlist.description,
            'creator': playlist.creator,
            'song_count': playlist.song_count,
            'play_count': playlist.play_count,
            'songs': [{
                'id': song.id,
                'name': song.name,
                'artist': song.artist,
                'album': song.album,
                'duration': song.duration,
                'url': song.url
            } for song in (playlist.songs or [])]
        } for playlist in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/search_albums', methods=['POST'])
def search_albums():
    try:
        data = request.json        
        results = music_tool.search_albums(
            keyword=data['keyword']
        )
        
        return jsonify([{
            'id': album.id,
            'name': album.name,
            'artist': album.artist,
            'description': album.description
        } for album in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/search_albums_by_artist', methods=['POST'])
def search_albums_by_artist():
    try:
        data = request.json        
        results = music_tool.search_albums_by_artist(
            artistName=data['artistName'],
            albumName=data['albumName']
        )
        
        return jsonify([{
            'id': album.id,
            'name': album.name,
            'artist': album.artist,
            'description': album.description
        } for album in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/search_artists', methods=['POST'])
def search_artists():
    try:
        data = request.json        
        results = music_tool.search_artists(
            keyword=data['keyword']
        )
        
        return jsonify([{
            'id': artist.id,
            'name': artist.name,
            'alias': artist.alias
        } for artist in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/hot_keywords', methods=['GET'])
def hot_keywords():
    try:
        results = music_tool.get_hot_keywords()
        return jsonify(results)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/suggest_keywords', methods=['POST'])
def suggest_keywords():
    try:
        data = request.json        
        results = music_tool.get_suggest_keywords(
            keyword=data['keyword']
        )
        return jsonify(results)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/hi_res', methods=['POST'])
def get_hi_res():
    try:
        data = request.json
        results = music_tool.get_hi_res(
            resource_types=data['resourceTypes'],
            with_url=data.get('withUrl', True),
            bitrate=data.get('bitrate', 320)
        )
        
        return jsonify([{
            'category': category.category,
            'songDetailVoList': [{
                'id': song.id,
                'name': song.name,
                'duration': song.duration,
                'albumName': song.album_name,
                'albumId': song.album_id,
                'albumArtistId': song.album_artist_id,
                'albumArtistName': song.album_artist_name,
                'artistId': song.artist_id,
                'artistName': song.artist_name,
                'coverImgUrl': song.cover_img_url,
                'mvId': song.mv_id,
                'playUrl': song.play_url,
                'playFlag': song.play_flag,
                'downloadFlag': song.download_flag,
                'payPlayFlag': song.pay_play_flag,
                'payDownloadFlag': song.pay_download_flag,
                'vipFlag': song.vip_flag,
                'vipPlayFlag': song.vip_play_flag,
                'liked': song.liked,
                'maxBrLevel': song.max_br_level,
                'plLevel': song.pl_level,
                'dlLevel': song.dl_level,
                'songSize': song.song_size,
                'songMd5': song.song_md5,
                'songTag': song.song_tag,
                'songFee': song.song_fee,
                'br': song.br,
                'audioFlag': song.audio_flag,
                'effects': song.effects,
                'visible': song.visible
            } for song in (category.song_detail_vo_list or [])],
            'playlistDetailVoList': [{
                'id': pl.id,
                'name': pl.name,
                'describe': pl.describe,
                'coverImgUrl': pl.cover_img_url,
                'creatorNickName': pl.creator_nick_name,
                'playCount': pl.play_count,
                'subscribedCount': pl.subscribed_count,
                'tags': pl.tags,
                'creatorId': pl.creator_id,
                'createTime': pl.create_time,
                'subed': pl.subed,
                'trackCount': pl.track_count,
                'specialType': pl.special_type
            } for pl in (category.playlist_detail_vo_list or [])],
            'albumDetailVoList': [{
                'id': album.id,
                'name': album.name,
                'language': album.language,
                'coverImgUrl': album.cover_img_url,
                'company': album.company,
                'transName': album.trans_name,
                'aliaName': album.alia_name,
                'genre': album.genre,
                'artists': [{
                    'id': artist.id,
                    'name': artist.name
                } for artist in album.artists],
                'briefDesc': album.brief_desc,
                'description': album.description,
                'publishTime': album.publish_time
            } for album in (category.album_detail_vo_list or [])]
        } for category in results])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/music/song_detail', methods=['POST'])
def get_song_detail():
    """Get detailed information about a song"""
    try:
        song_id = request.json.get('songId')
        with_url = request.json.get('withUrl', True)
        bitrate = request.json.get('bitrate')
        effects = request.json.get('effects')
        quality_flag = request.json.get('qualityFlag', False)
        language_flag = request.json.get('languageFlag', False)

        if not song_id:
            return jsonify({"error": "songId is required"}), 400

        song = music_tool.get_song_detail(
            song_id=song_id,
            with_url=with_url,
            bitrate=bitrate,
            effects=effects,
            quality_flag=quality_flag,
            language_flag=language_flag
        )
        return jsonify({"song": song.__dict__})
    except Exception as e:
        print(f"Error getting song detail: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/music/playlist/songs', methods=['POST'])
def get_playlist_songs():
    """Get list of songs in a playlist"""
    try:
        data = request.json
        playlist_id = data.get('playlistId')
        limit = data.get('limit', 5)
        offset = data.get('offset', 0)

        if not playlist_id:
            return jsonify({"error": "playlistId is required"}), 400

        songs = music_tool.get_playlist_songs(
            playlist_id=playlist_id,
            limit=limit,
            offset=offset
        )
        
        return jsonify([{
            'id': song.id,
            'name': song.name,
            'duration': song.duration,
            'albumName': song.album_name,
            'albumId': song.album_id,
            'albumArtistId': song.album_artist_id,
            'albumArtistName': song.album_artist_name,
            'artistId': song.artist_id,
            'artistName': song.artist_name,
            'coverImgUrl': song.cover_img_url,
            'mvId': song.mv_id,
            'playUrl': song.play_url,
            'playFlag': song.play_flag,
            'downloadFlag': song.download_flag,
            'payPlayFlag': song.pay_play_flag,
            'payDownloadFlag': song.pay_download_flag,
            'vipFlag': song.vip_flag,
            'vipPlayFlag': song.vip_play_flag,
            'liked': song.liked,
            'maxBrLevel': song.max_br_level,
            'plLevel': song.pl_level,
            'dlLevel': song.dl_level,
            'songSize': song.song_size,
            'songMd5': song.song_md5,
            'songTag': song.song_tag,
            'songFee': song.song_fee,
            'br': song.br,
            'audioFlag': song.audio_flag,
            'effects': song.effects,
            'visible': song.visible
        } for song in songs])
    except Exception as e:
        print(f"Error getting playlist songs: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/music/album/free_songs', methods=['POST'])
def get_album_free_songs():
    """Get list of free songs in an album"""
    try:
        data = request.json
        album_id = data.get('albumId')
        limit = data.get('limit', 5)
        offset = data.get('offset', 0)

        if not album_id:
            return jsonify({"error": "albumId is required"}), 400

        songs = music_tool.get_album_free_songs(
            album_id=album_id,
            limit=limit,
            offset=offset
        )
        
        return jsonify([{
            'id': song.id,
            'name': song.name,
            'duration': song.duration,
            'albumName': song.album_name,
            'albumId': song.album_id,
            'albumArtistId': song.album_artist_id,
            'albumArtistName': song.album_artist_name,
            'artistId': song.artist_id,
            'artistName': song.artist_name,
            'coverImgUrl': song.cover_img_url,
            'mvId': song.mv_id,
            'playUrl': song.play_url,
            'playFlag': song.play_flag,
            'downloadFlag': song.download_flag,
            'payPlayFlag': song.pay_play_flag,
            'payDownloadFlag': song.pay_download_flag,
            'vipFlag': song.vip_flag,
            'vipPlayFlag': song.vip_play_flag,
            'liked': song.liked,
            'maxBrLevel': song.max_br_level,
            'plLevel': song.pl_level,
            'dlLevel': song.dl_level,
            'songSize': song.song_size,
            'songMd5': song.song_md5,
            'songTag': song.song_tag,
            'songFee': song.song_fee,
            'br': song.br,
            'audioFlag': song.audio_flag,
            'effects': song.effects,
            'visible': song.visible
        } for song in songs])
    except Exception as e:
        print(f"Error getting album free songs: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/music/album/vip_songs', methods=['POST'])
def get_album_vip_songs():
    """Get list of VIP songs in an album"""
    try:
        data = request.json
        album_id = data.get('albumId')
        limit = data.get('limit', 5)
        offset = data.get('offset', 0)

        if not album_id:
            return jsonify({"error": "albumId is required"}), 400

        songs = music_tool.get_album_vip_songs(
            album_id=album_id,
            limit=limit,
            offset=offset
        )
        
        return jsonify([{
            'id': song.id,
            'name': song.name,
            'duration': song.duration,
            'albumName': song.album_name,
            'albumId': song.album_id,
            'albumArtistId': song.album_artist_id,
            'albumArtistName': song.album_artist_name,
            'artistId': song.artist_id,
            'artistName': song.artist_name,
            'coverImgUrl': song.cover_img_url,
            'mvId': song.mv_id,
            'playUrl': song.play_url,
            'playFlag': song.play_flag,
            'downloadFlag': song.download_flag,
            'payPlayFlag': song.pay_play_flag,
            'payDownloadFlag': song.pay_download_flag,
            'vipFlag': song.vip_flag,
            'vipPlayFlag': song.vip_play_flag,
            'liked': song.liked,
            'maxBrLevel': song.max_br_level,
            'plLevel': song.pl_level,
            'dlLevel': song.dl_level,
            'songSize': song.song_size,
            'songMd5': song.song_md5,
            'songTag': song.song_tag,
            'songFee': song.song_fee,
            'br': song.br,
            'audioFlag': song.audio_flag,
            'effects': song.effects,
            'visible': song.visible
        } for song in songs])
    except Exception as e:
        print(f"Error getting album VIP songs: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/music/batch/song/urls', methods=['POST'])
def get_batch_song_urls():
    """批量获取歌曲播放URL"""
    try:
        data = request.json
        song_ids = data.get('songIds', [])
        bitrate = data.get('bitrate')
        effects = data.get('effects')

        if not song_ids:
            return jsonify({"error": "songIds is required"}), 400

        urls = music_tool.get_batch_song_urls(
            song_ids=song_ids,
            bitrate=bitrate,
            effects=effects
        )
        
        return jsonify(urls)
    except Exception as e:
        print(f"Error getting batch song URLs: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/music/song/like', methods=['POST'])
def like_song():
    """添加或删除红心歌曲"""
    try:
        data = request.json
        song_id = data.get('songId')
        is_like = data.get('isLike', True)

        if not song_id:
            return jsonify({"error": "songId is required"}), 400

        result = music_tool.like_song(
            song_id=song_id,
            is_like=is_like
        )
        
        return jsonify({
            "success": True,
            "message": "Successfully " + ("liked" if is_like else "unliked") + " the song",
            "data": result
        })
    except Exception as e:
        print(f"Error liking/unliking song: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/music/test_agent', methods=['POST'])
def test_music_agent_route():
    """Test music agent with a given prompt"""
    try:
        data = request.json
        prompt = data.get('prompt')

        if not prompt:
            return jsonify({"error": "prompt is required"}), 400

        from tools.NetEaseCloudMusic.music_autogen_agent import test_music_agent
        # 在同步环境中运行异步函数
        result = asyncio.run(test_music_agent(prompt))
        
        return result
    except Exception as e:
        print(f"Error testing music agent: {e}")
        return jsonify({"error": str(e)}), 500


if __name__ == '__main__':
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='启动 Web API 服务')
    parser.add_argument('--port', type=int, default=5000, help='服务运行的端口号 (默认: 5000)')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务监听的主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--debug', action='store_true', help='是否开启调试模式 (默认: False)')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 运行服务
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug
    )
