<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI短视频生成 Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-film"></i> AI短视频生成
                    </h5>
                </div>
                <div class="card-body">
                    <form id="aiVlogForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="vlogUserId" class="form-label">用户ID</label>
                            <input type="number" class="form-control" id="vlogUserId" name="user_id" value="1" required>
                        </div>
                        <div class="mb-3">
                            <label for="vlogTopic" class="form-label">视频主题</label>
                            <input type="text" class="form-control" id="vlogTopic" name="topic" placeholder="如：我的上海之旅" required>
                        </div>
                        <div class="mb-3">
                            <label for="vlogScript" class="form-label">视频文案</label>
                            <textarea class="form-control" id="vlogScript" name="script" rows="3" placeholder="请输入视频文案" required></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="magicCamera" name="magic_camera">
                                <label class="form-check-label" for="magicCamera">
                                    <i class="bi bi-magic"></i> 启用魔法相机（图生图）
                                </label>
                            </div>
                            <small class="form-text text-muted">启用后将对上传的图片进行AI增强处理</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">图片文件</label>
                            <input type="file" class="form-control" id="vlogImages" name="images" accept="image/*" multiple>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">视频文件</label>
                            <input type="file" class="form-control" id="vlogVideos" name="videos" accept="video/*" multiple>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">自定义背景音乐</label>
                            <input type="file" class="form-control" id="vlogAudios" name="audios" accept="audio/*">
                        </div>
                        <button type="submit" class="btn btn-success w-100">
                            <i class="bi bi-play-btn"></i> 生成AI短视频
                        </button>
                    </form>
                    <div id="aiVlogResult" class="mt-3" style="display:none;">
                        <div class="alert alert-info" id="aiVlogStatus">正在生成视频，请稍候...</div>
                        <video id="aiVlogVideo" controls style="width:100%;display:none;"></video>
                        <a id="aiVlogDownload" class="btn btn-primary mt-2" style="display:none;" download target="_blank">下载视频</a>
                    </div>
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-search"></i> 查询视频任务状态
                    </h5>
                </div>
                <div class="card-body">
                    <form id="aiVlogStatusForm" class="mb-2">
                        <div class="input-group">
                            <input type="text" class="form-control" id="aiVlogTaskId" placeholder="请输入任务ID" required>
                            <button class="btn btn-info" type="submit">查询状态</button>
                        </div>
                    </form>
                    <div id="aiVlogStatusResult" style="display:none;">
                        <div class="alert alert-secondary" id="aiVlogStatusText">状态信息</div>
                        <div id="aiVlogStatusVideos"></div>
                        <div id="aiVlogStatusCombinedVideos"></div>
                        <button class="btn btn-outline-primary btn-sm mt-2" id="aiVlogStatusPollBtn">轮询刷新</button>
                    </div>
                </div>
            </div>
            <!-- 新增：用户Vlog列表查询卡片 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-collection-play"></i> 查询用户Vlog列表
                    </h5>
                </div>
                <div class="card-body">
                    <form id="userVlogsForm" class="mb-2">
                        <div class="input-group">
                            <input type="number" class="form-control" id="userVlogsUserId" placeholder="请输入用户ID" value="1" min="1" required>
                            <button class="btn btn-primary" type="submit">查询</button>
                        </div>
                    </form>
                    <div id="userVlogsResult" style="display:none;">
                        <div id="userVlogsList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const aiVlogForm = document.getElementById('aiVlogForm');
  if (aiVlogForm) {
    aiVlogForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      const resultDiv = document.getElementById('aiVlogResult');
      const statusDiv = document.getElementById('aiVlogStatus');
      const videoEl = document.getElementById('aiVlogVideo');
      const downloadBtn = document.getElementById('aiVlogDownload');
      resultDiv.style.display = 'block';
      statusDiv.textContent = '正在生成视频，请稍候...';
      videoEl.style.display = 'none';
      downloadBtn.style.display = 'none';

      const formData = new FormData(aiVlogForm);

      try {
        const resp = await fetch('/api/ai_vlog/generate', {
          method: 'POST',
          body: formData
        });
        const data = await resp.json();
        if (data.code === 200 && data.task_id) {
          statusDiv.textContent = '视频生成任务已提交，任务ID：' + data.task_id;
          // 自动填入任务ID到查询框，方便后续查询
          const taskIdInput = document.getElementById('aiVlogTaskId');
          if (taskIdInput) taskIdInput.value = data.task_id;
        } else {
          statusDiv.textContent = '视频生成失败: ' + (data.detail || '未知错误');
        }
      } catch (err) {
        statusDiv.textContent = '请求失败: ' + err.message;
      }
    });
  }

  // 任务状态查询表单
  const statusForm = document.getElementById('aiVlogStatusForm');
  const pollBtn = document.getElementById('aiVlogStatusPollBtn');
  let lastTaskId = '';
  if (statusForm) {
    statusForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const taskId = document.getElementById('aiVlogTaskId').value.trim();
      if (taskId) {
        lastTaskId = taskId;
        queryVlogTaskStatus(taskId);
      }
    });
  }
  if (pollBtn) {
    pollBtn.addEventListener('click', function() {
      if (lastTaskId) {
        queryVlogTaskStatus(lastTaskId);
      }
    });
  }

  // 用户Vlog列表查询逻辑
  const userVlogsForm = document.getElementById('userVlogsForm');
  if (userVlogsForm) {
    userVlogsForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      const userId = document.getElementById('userVlogsUserId').value.trim();
      if (!userId) return;
      const resultDiv = document.getElementById('userVlogsResult');
      const listDiv = document.getElementById('userVlogsList');
      resultDiv.style.display = 'block';
      listDiv.innerHTML = '查询中...';
      try {
        const resp = await fetch(`/api/ai_vlog/user_vlogs?user_id=${userId}`);
        const data = await resp.json();
        if (data.code === 200 && data.data && Array.isArray(data.data.vlogs)) {
          if (data.data.vlogs.length === 0) {
            listDiv.innerHTML = '<div class="alert alert-warning">该用户暂无Vlog记录</div>';
          } else {
            listDiv.innerHTML = `<table class="table table-bordered table-sm"><thead><tr><th>ID</th><th>标题</th><th>状态</th><th>视频</th><th>创建时间</th><th>完成时间</th></tr></thead><tbody>` +
              data.data.vlogs.map(vlog => `
                <tr>
                  <td>${vlog.id}</td>
                  <td>${vlog.title}</td>
                  <td>${vlog.status_id}</td>
                  <td>
                    ${vlog.final_video_url ? `
                      <button class='btn btn-sm btn-outline-primary' onclick='previewVlogVideo("${vlog.final_video_url}")'>播放</button>
                      <a class='btn btn-sm btn-outline-success ms-1' href='${vlog.final_video_url}' download target='_blank'>下载</a>
                    ` : ''}
                    <button class='btn btn-sm btn-outline-danger ms-1' onclick='deleteVlog(${vlog.id}, this)'>删除</button>
                    ${vlog.status_id == 8 ? `<button class='btn btn-sm btn-outline-warning ms-1' onclick='regenerateVlog(${vlog.id}, this)'>重新生成</button>` : ''}
                  </td>
                  <td>${vlog.created_at || ''}</td>
                  <td>${vlog.completed_at || ''}</td>
                </tr>
              `).join('') + '</tbody></table>';
          }
        } else {
          listDiv.innerHTML = `<div class="alert alert-danger">查询失败: ${data.detail || '未知错误'}</div>`;
        }
      } catch (err) {
        listDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${err.message}</div>`;
      }
    });
  }
});

// 任务状态查询逻辑
function getStateText(state) {
  const stateMap = {
    '8': '任务失败',
    '7': '任务完成',
    '6': '处理中'
  };
  return stateMap[state] || `未知状态(${state})`;
}

function renderTaskStatus(data) {
  const statusDiv = document.getElementById('aiVlogStatusResult');
  const textDiv = document.getElementById('aiVlogStatusText');
  const videosDiv = document.getElementById('aiVlogStatusVideos');
  const combinedDiv = document.getElementById('aiVlogStatusCombinedVideos');
  if (!data || !data.data) {
    textDiv.textContent = '未查询到任务信息';
    videosDiv.innerHTML = '';
    combinedDiv.innerHTML = '';
    statusDiv.style.display = 'block';
    return;
  }
  textDiv.textContent = `状态: ${getStateText(data.data.status_id)}  进度: ${data.data.progress ?? '-'}%`;
  // 视频列表
  if (data.data.final_video_url) {
    videosDiv.innerHTML = '<b>单独视频：</b>' +
      `<video controls style="width:100%;max-width:400px;margin:5px 0;"><source src="${data.data.final_video_url}" type="video/mp4">您的浏览器不支持视频播放</video>`;
  } else if (Array.isArray(data.data.videos) && data.data.videos.length > 0) {
    videosDiv.innerHTML = '<b>单独视频：</b>' + data.data.videos.map(url => 
      `<video controls style="width:100%;max-width:400px;margin:5px 0;"><source src="${url}" type="video/mp4">您的浏览器不支持视频播放</video>`
    ).join('<br>');
  } else {
    videosDiv.innerHTML = '';
  }
  // 合成视频
  //if (Array.isArray(data.data.combined_videos) && data.data.combined_videos.length > 0) {
  //  combinedDiv.innerHTML = '<b>合成视频：</b>' + data.data.combined_videos.map(url => 
  //    `<video controls style="width:100%;max-width:400px;margin:5px 0;"><source src="${url}" type="video/mp4">您的浏览器不支持视频播放</video>`
  //  ).join('<br>');
  //} else {
  //  combinedDiv.innerHTML = '';
  //}
  statusDiv.style.display = 'block';
}

async function queryVlogTaskStatus(taskId) {
  const resp = await fetch(`/api/ai_vlog/task_status/${taskId}`);
  const data = await resp.json();
  renderTaskStatus(data);
}

function previewVlogVideo(url) {
  const videoWin = window.open('', '_blank', 'width=600,height=400');
  videoWin.document.write(`
    <html><head><title>视频预览</title></head><body style='margin:0;padding:0;'>
    <video src='${url}' controls autoplay style='width:100vw;height:100vh;max-width:100%;max-height:100%;background:#000;'></video>
    </body></html>
  `);
}

function deleteVlog(vlogId, btn) {
  if (!confirm('确定要删除该Vlog吗？')) return;
  btn.disabled = true;
  fetch(`/api/ai_vlog/delete/${vlogId}`, {
    method: 'POST',
  })
    .then(resp => resp.json())
    .then(data => {
      if (data.code === 200) {
        // 删除成功，刷新列表
        const userId = document.getElementById('userVlogsUserId').value.trim();
        if (userId) {
          document.getElementById('userVlogsForm').dispatchEvent(new Event('submit'));
        }
      } else {
        alert('删除失败: ' + (data.detail || data.msg || '未知错误'));
        btn.disabled = false;
      }
    })
    .catch(err => {
      alert('请求失败: ' + err.message);
      btn.disabled = false;
    });
}

function regenerateVlog(vlogId, btn) {
  btn.disabled = true;
  fetch(`/api/ai_vlog/regenerate/${vlogId}`, {
    method: 'POST',
  })
    .then(resp => resp.json())
    .then(data => {
      if (data.code === 200) {
        // 重新生成成功，刷新列表
        const userId = document.getElementById('userVlogsUserId').value.trim();
        if (userId) {
          document.getElementById('userVlogsForm').dispatchEvent(new Event('submit'));
        }
      } else {
        alert('重新生成失败: ' + (data.detail || data.msg || '未知错误'));
        btn.disabled = false;
      }
    })
    .catch(err => {
      alert('请求失败: ' + err.message);
      btn.disabled = false;
    });
}
</script>
</body>
</html> 