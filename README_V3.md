# AutoPilot AI V3.0 - 统一架构版

## 🚀 项目概述

AutoPilot AI V3.0 是基于统一架构重构的智能旅行规划系统，采用事件驱动架构和两阶段工作流，提供更高效、更智能的旅行规划体验。

### 核心特性

- **🏗️ 统一架构**: 基于三大核心支柱的统一事件驱动架构
- **🧠 两阶段工作流**: 意图分析 + ICP迭代规划
- **⚡ 实时响应**: SSE流式API，实时事件推送
- **🔧 工具生态**: 统一的工具注册与管理系统
- **📊 状态管理**: 标准化的Agent状态管理
- **🎯 智能规划**: AI驱动的迭代式上下文规划

## 🏛️ 架构设计

### 三大核心支柱

#### 1. StandardAgentState - 统一状态管理
```python
class StandardAgentState(TypedDict):
    # 基础消息流
    messages: Annotated[List[Dict[str, Any]], add_messages]
    
    # 任务标识与控制
    task_id: str
    user_id: str
    current_phase: str
    execution_mode: Literal["automatic", "interactive"]
    
    # 意图分析结果
    framework_analysis: Optional[Dict[str, Any]]
    preference_analysis: Optional[Dict[str, Any]]
    consolidated_intent: Optional[Dict[str, Any]]
    
    # ICP迭代规划状态
    planning_log: List[str]
    current_action: Optional[Dict[str, Any]]
    daily_plans: Dict[int, List[Dict[str, Any]]]
    
    # 最终结果
    final_itinerary: Optional[Dict[str, Any]]
    is_completed: bool
```

#### 2. UnifiedToolRegistry - 统一工具注册表
```python
# Action Tools: 与外部世界交互
@unified_registry.register_action_tool
async def search_poi(keywords: str, city: str) -> List[Dict]:
    """搜索POI信息"""
    pass

# Planner Tools: 辅助AI思考和推理
@unified_registry.register_planner_tool
def generate_planning_thought(current_state: Dict, context: Dict) -> Dict:
    """生成规划思考"""
    pass
```

#### 3. UnifiedEventBus - 统一事件总线
```python
# 事件发布
await event_bus.notify_phase_start(task_id, "framework_analysis", "核心框架分析", "正在分析...")
await event_bus.notify_planning_log(task_id, "开始第1步规划", 1)
await event_bus.notify_final_result(task_id, final_itinerary)

# 状态同步
await event_bus.sync_from_agent_state(task_id, agent_state)
```

### 两阶段工作流

#### 阶段一：意图分析
1. **核心框架分析**: 确定目的地、天数、主题、预算等核心要素
2. **个性化偏好分析**: 深入分析景点、美食、住宿偏好
3. **意图整合**: 将两步分析结果整合为统一的规划上下文

#### 阶段二：ICP迭代规划
1. **思考**: AI分析当前状态，生成下一步规划思路
2. **行动**: 选择并执行具体的工具调用
3. **观察**: 分析执行结果，评估规划质量
4. **迭代**: 重复上述循环直到规划完成

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Redis 6.0+
- LangGraph 0.2+

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置环境

1. 复制配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置必要的API密钥和服务地址：
```env
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password

# LLM配置
REASONING_LLM_API_KEY=your_openai_api_key
REASONING_LLM_BASE_URL=https://api.openai.com/v1

# 高德地图API
AMAP_API_KEY=your_amap_api_key
```

### 运行演示

```bash
python demo_v3.py
```

## 📖 使用指南

### 基本用法

```python
from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.database.redis_client import get_redis_client

# 创建事件总线
redis_client = await get_redis_client()
event_bus = UnifiedEventBus(redis_client)

# 创建规划图
graph = TravelPlannerGraphV3(event_bus=event_bus)

# 准备输入数据
input_data = {
    "user_query": "我想去北京玩三天，喜欢历史文化景点",
    "user_id": "user_123",
    "execution_mode": "automatic",
    "user_profile": {
        "age": 30,
        "interests": ["文化", "历史"],
        "budget_level": "中等"
    }
}

# 执行规划
result = await graph.invoke(input_data)
print(f"规划完成: {result['final_itinerary']}")
```

### 流式执行

```python
# 流式执行，实时获取进度
async for step in graph.stream(input_data):
    for node_name, node_result in step.items():
        print(f"节点 {node_name} 完成: {node_result}")
```

### API调用

```bash
# 启动规划
curl -X POST http://localhost:8000/api/v3/travel-planner/plan \
  -H "Content-Type: application/json" \
  -d '{
    "user_query": "我想去上海玩两天",
    "user_id": "user_123",
    "execution_mode": "automatic"
  }'

# 查询任务状态
curl http://localhost:8000/api/v3/travel-planner/status/task_123

# 健康检查
curl http://localhost:8000/api/v3/travel-planner/health
```

## 🧪 测试

### 运行单元测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_unified_architecture.py -v
pytest tests/test_intent_analysis_flow.py -v
pytest tests/test_icp_planning.py -v
pytest tests/test_api_v3_integration.py -v
```

### 端到端测试

```bash
# 运行端到端测试
pytest tests/test_unified_architecture_e2e.py -v
```

## 📊 性能指标

### 基准测试结果

- **意图分析阶段**: 平均 2-3 秒
- **ICP规划阶段**: 平均 5-10 秒（取决于复杂度）
- **内存使用**: 峰值 < 500MB
- **并发处理**: 支持 50+ 并发任务

### 优化建议

1. **Redis连接池**: 配置合适的连接池大小
2. **工具缓存**: 启用POI搜索结果缓存
3. **异步处理**: 使用异步I/O提升性能
4. **负载均衡**: 部署多个实例进行负载分担

## 🔧 开发指南

### 添加新工具

1. **Action Tool** (与外部API交互):
```python
@unified_registry.register_action_tool
async def my_action_tool(param1: str, param2: int = 10) -> Dict:
    """我的Action Tool
    
    Args:
        param1: 参数1描述
        param2: 参数2描述，默认为10
    """
    # 实现工具逻辑
    return {"result": "success"}
```

2. **Planner Tool** (辅助AI思考):
```python
@unified_registry.register_planner_tool
def my_planner_tool(data: Dict[str, Any]) -> Dict:
    """我的Planner Tool"""
    # 实现规划逻辑
    return {"analysis": "completed"}
```

### 扩展工作流

1. 在 `src/agents/travel_planner_lg/nodes.py` 中添加新节点
2. 在 `src/agents/travel_planner_lg/graph_v3.py` 中更新图结构
3. 编写相应的单元测试

### 自定义事件

```python
# 发布自定义事件
await event_bus.notify_custom_event(
    task_id, 
    "my_custom_event", 
    {"data": "custom_data"}
)
```

## 🚀 部署

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "app.py"]
```

### 生产环境配置

1. **Redis集群**: 配置Redis主从或集群模式
2. **负载均衡**: 使用Nginx或HAProxy
3. **监控**: 集成Prometheus + Grafana
4. **日志**: 配置结构化日志输出

## 📝 更新日志

### V3.0.0 (2024-01-XX)

#### 新增功能
- ✨ 统一架构重构
- ✨ 两阶段工作流
- ✨ SSE流式API
- ✨ 事件驱动架构
- ✨ 统一工具注册表

#### 改进
- 🚀 性能提升 50%+
- 🔧 更好的错误处理
- 📊 实时进度跟踪
- 🎯 更智能的规划算法

#### 破坏性变更
- 🔄 API端点从 `/api/v2/` 迁移到 `/api/v3/`
- 🔄 状态结构从 `TravelPlanState` 迁移到 `StandardAgentState`

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [LangGraph](https://github.com/langchain-ai/langgraph) - 工作流编排框架
- [Redis](https://redis.io/) - 内存数据库
- [Flask](https://flask.palletsprojects.com/) - Web框架

---

**AutoPilot AI V3.0** - 让旅行规划更智能 🌟
