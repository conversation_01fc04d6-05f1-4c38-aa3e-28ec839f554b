"""
Music data models for NetEase Cloud Music API
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Union

@dataclass
class FreeTrail:
    """Free trial period data structure"""
    start: int
    end: int

@dataclass
class Song:
    """Song data structure"""
    id: str
    name: str
    artist: str
    album: str
    duration: int  # in seconds
    url: Optional[str] = None

@dataclass
class Album:
    """Album data structure"""
    id: str
    name: str
    artist: str
    description: Optional[str] = None

@dataclass
class Artist:
    """Artist data structure"""
    id: str
    name: str
    alias: Optional[List[str]] = None
    cover_img_url: Optional[str] = None
    type: Optional[str] = None

@dataclass
class Playlist:
    """Playlist data structure"""
    id: str
    name: str
    description: Optional[str]
    creator: str
    song_count: int
    play_count: int
    songs: Optional[List[Song]] = None

@dataclass
class Qualities:
    """Audio qualities data structure"""
    vivid_music: Optional[bool] = None  # 超景气(前称杜比)
    sk_music: Optional[bool] = None     # Hi-Res
    jy_master_music: Optional[bool] = None  # 母带
    jy_effect_music: Optional[bool] = None  # 增效
    sq_music: Optional[bool] = None     # 超品
    h_music: Optional[bool] = None      # 高品
    m_music: Optional[bool] = None      # 标准
    l_music: Optional[bool] = None      # 流畅

@dataclass
class SongDetail:
    """Song detail data structure"""
    id: str
    name: str
    duration: int
    album_name: str
    album_id: str
    album_artist_id: str
    album_artist_name: str
    artist_id: str
    artist_name: str
    cover_img_url: Optional[str] = None
    mv_id: Optional[str] = None
    play_url: Optional[str] = None
    play_flag: bool = True
    download_flag: bool = True
    pay_play_flag: bool = False
    pay_download_flag: bool = False
    vip_flag: bool = False
    vip_play_flag: bool = False
    free_trail_flag: bool = False
    free_trail: Optional[FreeTrail] = None
    liked: bool = False
    song_max_br: Optional[int] = None
    user_max_br: Optional[int] = None
    max_br_level: Optional[str] = None
    pl_level: Optional[str] = None
    dl_level: Optional[str] = None
    gain: Optional[float] = None
    peak: Optional[float] = None
    level: Optional[str] = None
    song_size: Optional[int] = None
    song_md5: Optional[str] = None
    song_tag: Optional[List[str]] = None
    artists: Optional[List[Artist]] = None
    full_artists: Optional[List[Artist]] = None
    song_fee: int = 0
    br: Optional[int] = None
    audio_flag: Optional[int] = None
    effects: Optional[str] = None
    private_cloud_song: bool = False
    qualities: Optional[Qualities] = None
    language: Optional[str] = None
    visible: bool = True

@dataclass
class PlaylistDetail:
    """Playlist detail data structure"""
    id: str
    name: str
    describe: Optional[str]
    cover_img_url: Optional[str]
    creator_nick_name: str
    play_count: str
    subscribed_count: str
    tags: Optional[List[str]]
    creator_id: str
    create_time: str
    subed: bool = False
    track_count: int = 0
    special_type: Optional[int] = None

@dataclass
class AlbumArtist:
    """Album Artist data structure"""
    id: str
    name: str

@dataclass
class AlbumDetail:
    """Album detail data structure"""
    id: str
    name: str
    language: Optional[str]
    cover_img_url: Optional[str]
    company: Optional[str]
    trans_name: Optional[str]
    alia_name: Optional[str]
    genre: Optional[str]
    artists: List[AlbumArtist]
    brief_desc: Optional[str]
    description: Optional[str]
    publish_time: Optional[str]

@dataclass
class HiResCategory:
    """Hi-Res category data structure"""
    category: str
    song_detail_vo_list: Optional[List[SongDetail]] = None
    playlist_detail_vo_list: Optional[List[PlaylistDetail]] = None 
    album_detail_vo_list: Optional[List[AlbumDetail]] = None
