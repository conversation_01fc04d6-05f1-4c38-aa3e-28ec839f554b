#!/usr/bin/env python3
"""
AutoGen 适配器单元测试

这些测试使用 Mock 对象验证 AutoGen 适配器的逻辑，
不依赖外部服务和真实的配置文件。
"""

import os
import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置测试环境变量
def setup_test_env():
    """设置测试环境变量"""
    test_env = {
        "REASONING_LLM_MODEL": "test-reasoning-model",
        "REASONING_LLM_API_KEY": "test-reasoning-key",
        "REASONING_LLM_BASE_URL": "https://test.api.com/v1",
        "BASIC_LLM_MODEL": "test-basic-model",
        "BASIC_LLM_API_KEY": "test-basic-key",
        "BASIC_LLM_BASE_URL": "https://test.api.com/v1",
        "LOG_LEVEL": "INFO"
    }
    for key, value in test_env.items():
        if not os.getenv(key):
            os.environ[key] = value

setup_test_env()

from src.core.config import LLMConfig


class TestAutoGenIntegrationAdapter:
    """AutoGen 集成适配器测试类"""

    def test_adapter_imports_with_autogen_available(self):
        """测试当 AutoGen 可用时的适配器导入"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            # 模拟 AutoGen 模块
            mock_autogen = Mock()
            with patch.dict('sys.modules', {
                'autogen_agentchat.agents': mock_autogen,
                'autogen_agentchat.teams': mock_autogen,
                'autogen_agentchat.conditions': mock_autogen,
                'autogen_ext.models.openai': mock_autogen
            }):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                # 创建适配器应该成功
                adapter = AutoGenIntegration()
                assert adapter is not None

    def test_adapter_imports_with_autogen_unavailable(self):
        """测试当 AutoGen 不可用时的适配器行为"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', False):
            from tests.integration.test_autogen_agent_integration import AutoGenIntegration
            
            # 应该能创建适配器但使用 Mock 配置
            adapter = AutoGenIntegration()
            assert adapter is not None

    @patch('src.core.config.get_settings')
    def test_create_model_client_basic_config(self, mock_get_settings):
        """测试基础配置的模型客户端创建"""
        # Mock 配置
        mock_llm_config = LLMConfig(
            model="glm-4-flash",
            api_key="test-api-key",
            base_url="https://api.openai.com/v1"
        )
        
        mock_settings = Mock()
        mock_settings.get_llm_config_by_role.return_value = mock_llm_config
        mock_get_settings.return_value = mock_settings
        
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            # Mock AutoGen 模块
            mock_client_class = Mock()
            mock_client = Mock()
            mock_client_class.return_value = mock_client
            
            # 在模块导入之前 Mock OpenAIChatCompletionClient
            with patch('tests.integration.test_autogen_agent_integration.OpenAIChatCompletionClient', mock_client_class):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                adapter = AutoGenIntegration()
                # 直接设置 settings 避免初始化问题
                adapter.settings = mock_settings
                client = adapter.create_model_client("basic")
                
                # 验证客户端创建参数，GLM模型需要 model_info
                mock_client_class.assert_called_once()
                call_args = mock_client_class.call_args
                assert call_args[1]['model'] == "glm-4-flash"
                assert call_args[1]['api_key'] == "test-api-key"
                assert call_args[1]['base_url'] == "https://api.openai.com/v1"
                assert 'model_info' in call_args[1]  # GLM模型需要model_info
                assert client == mock_client

    @patch('src.core.config.get_settings')
    def test_create_model_client_reasoning_config(self, mock_get_settings):
        """测试思考配置的模型客户端创建"""
        # Mock 配置
        mock_llm_config = LLMConfig(
            model="glm-z1-flash",
            api_key="test-reasoning-key",
            base_url="https://api.anthropic.com/v1"
        )
        
        mock_settings = Mock()
        mock_settings.get_llm_config_by_role.return_value = mock_llm_config
        mock_get_settings.return_value = mock_settings
        
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            # Mock AutoGen 模块
            mock_client_class = Mock()
            mock_client = Mock()
            mock_client_class.return_value = mock_client
            
            # 在模块导入之前 Mock OpenAIChatCompletionClient
            with patch('tests.integration.test_autogen_agent_integration.OpenAIChatCompletionClient', mock_client_class):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                adapter = AutoGenIntegration()
                # 直接设置 settings 避免初始化问题
                adapter.settings = mock_settings
                client = adapter.create_model_client("reasoning")
                
                # 验证客户端创建参数，GLM模型需要 model_info
                mock_client_class.assert_called_once()
                call_args = mock_client_class.call_args
                assert call_args[1]['model'] == "glm-z1-flash"
                assert call_args[1]['api_key'] == "test-reasoning-key"
                assert call_args[1]['base_url'] == "https://api.anthropic.com/v1"
                assert 'model_info' in call_args[1]  # GLM模型需要model_info
                assert client == mock_client

    def test_create_model_client_autogen_unavailable(self):
        """测试 AutoGen 不可用时的错误处理"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', False):
            from tests.integration.test_autogen_agent_integration import AutoGenIntegration
            
            adapter = AutoGenIntegration()
            
            with pytest.raises(ImportError, match="AutoGen 库未可用"):
                adapter.create_model_client("basic")

    @patch('src.core.config.get_settings')
    def test_create_model_client_with_none_base_url(self, mock_get_settings):
        """测试 base_url 为 None 的情况"""
        # Mock 配置（不包含 base_url）
        mock_llm_config = LLMConfig(
            model="glm-4-flash",
            api_key="test-api-key"
        )
        
        mock_settings = Mock()
        mock_settings.get_llm_config_by_role.return_value = mock_llm_config
        mock_get_settings.return_value = mock_settings
        
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            # Mock AutoGen 模块
            mock_client_class = Mock()
            mock_client = Mock()
            mock_client_class.return_value = mock_client
            
            # 在模块导入之前 Mock OpenAIChatCompletionClient
            with patch('tests.integration.test_autogen_agent_integration.OpenAIChatCompletionClient', mock_client_class):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                adapter = AutoGenIntegration()
                # 直接设置 settings 避免初始化问题
                adapter.settings = mock_settings
                client = adapter.create_model_client("basic")
                
                # 验证 base_url 为 None，GLM模型需要 model_info
                mock_client_class.assert_called_once()
                call_args = mock_client_class.call_args
                assert call_args[1]['model'] == "glm-4-flash"
                assert call_args[1]['api_key'] == "test-api-key"
                assert call_args[1]['base_url'] is None
                assert 'model_info' in call_args[1]  # GLM模型需要model_info

    def test_create_simple_assistant_autogen_unavailable(self):
        """测试 AutoGen 不可用时创建助手的错误处理"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', False):
            from tests.integration.test_autogen_agent_integration import AutoGenIntegration
            
            adapter = AutoGenIntegration()
            
            with pytest.raises(ImportError):
                adapter.create_simple_assistant("测试助手")

    @patch('src.core.config.get_settings')
    def test_create_simple_assistant_with_custom_name(self, mock_get_settings):
        """测试创建自定义名称的助手"""
        # Mock 配置
        mock_llm_config = LLMConfig(
            model="glm-4-flash",
            api_key="test-api-key"
        )
        
        mock_settings = Mock()
        mock_settings.get_llm_config_by_role.return_value = mock_llm_config
        mock_get_settings.return_value = mock_settings
        
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            # Mock AutoGen 模块
            mock_client_class = Mock()
            mock_client = Mock()
            mock_client_class.return_value = mock_client
            
            mock_assistant_class = Mock()
            mock_assistant = Mock()
            mock_assistant.name = "自定义助手"
            mock_assistant_class.return_value = mock_assistant
            
            # 在模块导入之前 Mock 相关类
            with patch('tests.integration.test_autogen_agent_integration.OpenAIChatCompletionClient', mock_client_class), \
                 patch('tests.integration.test_autogen_agent_integration.AssistantAgent', mock_assistant_class):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                adapter = AutoGenIntegration()
                # 直接设置 settings 避免初始化问题
                adapter.settings = mock_settings
                assistant = adapter.create_simple_assistant("自定义助手", "basic")
                
                # 验证助手创建参数
                mock_assistant_class.assert_called_once()
                call_args = mock_assistant_class.call_args
                assert call_args[1]['name'] == "自定义助手"
                assert "自定义助手" in call_args[1]['system_message']
                assert assistant == mock_assistant

    def test_simple_chat_test_autogen_unavailable(self):
        """测试 AutoGen 不可用时对话测试的错误处理"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', False):
            from tests.integration.test_autogen_agent_integration import AutoGenIntegration
            
            adapter = AutoGenIntegration()
            
            # 应该在 create_simple_assistant 时抛出错误
            with pytest.raises(ImportError):
                import asyncio
                asyncio.run(adapter.simple_chat_test("测试消息"))

    def test_mock_settings_fallback(self):
        """测试配置获取失败时的 Mock 设置后备机制"""
        # 直接 Mock 集成模块内的 get_settings 函数
        with patch('tests.integration.test_autogen_agent_integration.get_settings') as mock_get_settings:
            # 让 get_settings 抛出异常
            mock_get_settings.side_effect = Exception("配置加载失败")
            
            with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                adapter = AutoGenIntegration()
                
                # 当get_settings失败时，应该使用Mock设置
                basic_config = adapter.settings.get_llm_config_by_role("basic")
                reasoning_config = adapter.settings.get_llm_config_by_role("reasoning")
                
                # Mock配置应该使用智谱AI模型
                assert basic_config.model == "glm-4-flash"
                assert reasoning_config.model == "glm-z1-flash"

    def test_get_llm_config_exception_handling(self):
        """测试获取 LLM 配置时的异常处理"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            # Mock AutoGen 模块
            mock_client_class = Mock()
            mock_client = Mock()
            mock_client_class.return_value = mock_client
            
            # 在模块导入之前 Mock OpenAIChatCompletionClient
            with patch('tests.integration.test_autogen_agent_integration.OpenAIChatCompletionClient', mock_client_class):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                adapter = AutoGenIntegration()
                
                # Mock 设置对象让 get_llm_config_by_role 抛出异常
                adapter.settings = Mock()
                adapter.settings.get_llm_config_by_role.side_effect = Exception("配置获取失败")
                
                # 应该使用默认配置
                client = adapter.create_model_client("basic")
                
                # 验证使用了默认配置
                mock_client_class.assert_called_once()
                call_args = mock_client_class.call_args
                assert call_args[1]['model'] == "glm-4-flash"
                assert call_args[1]['api_key'] == "default-test-key"

    def test_create_model_client_reasoning_default_config(self):
        """测试思考模型使用默认配置的情况"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            # Mock AutoGen 模块
            mock_client_class = Mock()
            mock_client = Mock()
            mock_client_class.return_value = mock_client
            
            # 在模块导入之前 Mock OpenAIChatCompletionClient
            with patch('tests.integration.test_autogen_agent_integration.OpenAIChatCompletionClient', mock_client_class):
                from tests.integration.test_autogen_agent_integration import AutoGenIntegration
                
                adapter = AutoGenIntegration()
                
                # Mock 设置对象让 get_llm_config_by_role 抛出异常
                adapter.settings = Mock()
                adapter.settings.get_llm_config_by_role.side_effect = Exception("配置获取失败")
                
                # 应该使用默认配置
                client = adapter.create_model_client("reasoning")
                
                # 验证使用了默认配置
                mock_client_class.assert_called_once()
                call_args = mock_client_class.call_args
                assert call_args[1]['model'] == "glm-z1-flash"
                assert call_args[1]['api_key'] == "default-test-key"

    def test_logger_usage(self):
        """测试日志记录功能"""
        with patch('tests.integration.test_autogen_agent_integration.get_logger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            from tests.integration.test_autogen_agent_integration import AutoGenIntegration
            
            adapter = AutoGenIntegration()
            
            # 验证日志记录器被获取
            mock_get_logger.assert_called()
            assert adapter.logger == mock_logger


class TestAutoGenTestFunctions:
    """AutoGen 测试函数的单元测试"""
    
    @pytest.mark.asyncio
    async def test_autogen_availability_true(self):
        """测试 AutoGen 可用性检查 - 可用情况"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            from tests.integration.test_autogen_agent_integration import test_autogen_availability
            
            result = await test_autogen_availability()
            assert result is True
    
    @pytest.mark.asyncio
    async def test_autogen_availability_false(self):
        """测试 AutoGen 可用性检查 - 不可用情况"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', False):
            from tests.integration.test_autogen_agent_integration import test_autogen_availability
            
            result = await test_autogen_availability()
            assert result is False
    
    @pytest.mark.asyncio
    async def test_config_integration_unavailable(self):
        """测试配置集成 - AutoGen 不可用时的处理"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', False):
            from tests.integration.test_autogen_agent_integration import test_autogen_config_integration
            
            # 应该静默跳过，不抛出异常
            await test_autogen_config_integration()
    
    @pytest.mark.asyncio  
    async def test_coexistence_test_success(self):
        """测试共存性测试的成功情况"""
        with patch('tests.integration.test_autogen_agent_integration.AUTOGEN_AVAILABLE', True):
            from tests.integration.test_autogen_agent_integration import test_coexistence_with_existing_agents
            
            # 模拟 AutoGen 集成
            with patch('tests.integration.test_autogen_agent_integration.AutoGenIntegration') as mock_integration_class:
                mock_integration = Mock()
                mock_assistant = Mock()
                mock_assistant.name = "共存测试助手"
                mock_integration.create_simple_assistant.return_value = mock_assistant
                mock_integration_class.return_value = mock_integration
                
                # 执行测试，应该成功完成
                await test_coexistence_with_existing_agents()
                
                # 验证 AutoGen Agent 被创建
                mock_integration.create_simple_assistant.assert_called_once_with("共存测试助手")


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 