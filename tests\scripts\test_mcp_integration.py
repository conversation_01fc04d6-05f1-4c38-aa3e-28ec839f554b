"""
高德地图MCP工具集成测试

验证：
1. MCP客户端连接和工具调用
2. 地理编码和路线规划功能
3. POI搜索和天气查询功能  
4. 与TravelPlannerAgent的集成效果
5. 真实旅行场景的端到端测试
"""

import asyncio
import sys
import os
import json
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.tools.amap_mcp_client import get_amap_client


class MCPTestSuite:
    """MCP工具测试套件"""
    
    def __init__(self):
        self.test_results = {}
        self.client = None
    
    async def setup(self):
        """测试环境初始化"""
        print("[SETUP] 初始化MCP测试环境")
        try:
            self.client = await get_amap_client()
            await self.client.connect()
            print(f"[OK] MCP客户端连接成功，可用工具: {len(self.client.tools)}")
            return True
        except Exception as e:
            print(f"[ERROR] MCP客户端连接失败: {e}")
            return False
    
    async def teardown(self):
        """清理测试环境"""
        if self.client:
            await self.client.disconnect()
            print("[CLEANUP] MCP客户端已断开")

    async def test_basic_connection(self):
        """测试基础连接和工具列表"""
        print("\n[测试1] MCP基础连接和工具发现")
        
        test_name = "basic_connection"
        try:
            if not self.client.is_connected:
                raise Exception("MCP客户端未连接")
            
            # 验证工具列表
            tools = list(self.client.tools.keys())
            required_tools = [
                "maps_geo", "maps_direction_driving", 
                "maps_text_search", "maps_weather"
            ]
            
            missing_tools = [tool for tool in required_tools if tool not in tools]
            if missing_tools:
                raise Exception(f"缺少必需工具: {missing_tools}")
            
            print(f"[OK] 连接验证通过，可用工具: {len(tools)}")
            print(f"[INFO] 核心工具列表:")
            for i, tool in enumerate(required_tools, 1):
                print(f"  {i}. {tool}")
            
            self.test_results[test_name] = {"status": "PASS", "tools_count": len(tools)}
            return True
            
        except Exception as e:
            print(f"[ERROR] 基础连接测试失败: {e}")
            self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
            return False

    async def test_geocoding(self):
        """测试地理编码功能"""
        print("\n[测试2] 地理编码功能")
        
        test_name = "geocoding"
        test_cases = [
            {"address": "福州", "city": None},
            {"address": "厦门", "city": None},
            {"address": "北京天安门", "city": "北京"},
            {"address": "上海人民广场", "city": "上海"}
        ]
        
        results = []
        try:
            for case in test_cases:
                print(f"[INFO] 测试地理编码: {case['address']}")
                
                result = await self.client.maps_geo(
                    address=case["address"], 
                    city=case["city"]
                )
                
                # 验证响应格式
                if "results" not in result or not result["results"]:
                    raise Exception(f"地理编码失败: {case['address']}")
                
                location = result["results"][0]["location"]
                city_name = result["results"][0]["city"]
                
                print(f"  ✅ {case['address']} -> {location} ({city_name})")
                results.append({
                    "address": case["address"],
                    "location": location,
                    "city": city_name
                })
            
            print(f"[OK] 地理编码测试通过，成功编码 {len(results)} 个地址")
            self.test_results[test_name] = {"status": "PASS", "results": results}
            return True
            
        except Exception as e:
            print(f"[ERROR] 地理编码测试失败: {e}")
            self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
            return False

    async def test_route_planning(self):
        """测试路线规划功能"""
        print("\n[测试3] 路线规划功能")
        
        test_name = "route_planning"
        try:
            # 先获取起点和终点坐标
            print("[INFO] 获取起点坐标: 福州")
            origin_result = await self.client.maps_geo("福州")
            origin_location = origin_result["results"][0]["location"]
            
            print("[INFO] 获取终点坐标: 厦门")
            dest_result = await self.client.maps_geo("厦门")
            dest_location = dest_result["results"][0]["location"]
            
            print(f"[INFO] 规划路线: {origin_location} -> {dest_location}")
            
            # 执行路线规划
            route_result = await self.client.maps_direction_driving(
                origin=origin_location,
                destination=dest_location
            )
            
            # 验证路线规划结果
            if "paths" not in route_result or not route_result["paths"]:
                raise Exception("路线规划返回空结果")
            
            path = route_result["paths"][0]
            distance = int(path["distance"]) / 1000  # 转换为公里
            duration = int(path["duration"]) / 60    # 转换为分钟
            steps_count = len(path.get("steps", []))
            
            print(f"[OK] 路线规划成功:")
            print(f"  📍 起点: 福州 ({origin_location})")
            print(f"  📍 终点: 厦门 ({dest_location})")
            print(f"  🛣️ 距离: {distance:.1f}公里")
            print(f"  ⏱️ 时间: {duration:.0f}分钟")
            print(f"  📋 步骤: {steps_count}个导航指令")
            
            self.test_results[test_name] = {
                "status": "PASS",
                "distance_km": round(distance, 1),
                "duration_min": round(duration, 0),
                "steps_count": steps_count
            }
            return True
            
        except Exception as e:
            print(f"[ERROR] 路线规划测试失败: {e}")
            self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
            return False

    async def test_poi_search(self):
        """测试POI搜索功能"""
        print("\n[测试4] POI搜索功能")
        
        test_name = "poi_search"
        search_cases = [
            {"keywords": "景点", "city": "厦门", "expected_min": 5},
            {"keywords": "美食", "city": "福州", "expected_min": 3},
            {"keywords": "酒店", "city": "杭州", "expected_min": 3}
        ]
        
        search_results = []
        try:
            for case in search_cases:
                print(f"[INFO] 搜索: {case['keywords']} (城市: {case['city']})")
                
                result = await self.client.maps_text_search(
                    keywords=case["keywords"],
                    city=case["city"]
                )
                
                if "pois" not in result:
                    raise Exception(f"POI搜索失败: {case}")
                
                pois = result["pois"]
                if len(pois) < case["expected_min"]:
                    raise Exception(f"POI数量不足: 期望>={case['expected_min']}, 实际{len(pois)}")
                
                print(f"  ✅ 找到 {len(pois)} 个{case['keywords']}:")
                for i, poi in enumerate(pois[:3], 1):  # 显示前3个
                    print(f"    {i}. {poi['name']} - {poi.get('address', '无地址')}")
                
                search_results.append({
                    "keywords": case["keywords"],
                    "city": case["city"],
                    "count": len(pois),
                    "samples": [{"name": poi["name"], "address": poi.get("address", "")} 
                              for poi in pois[:3]]
                })
            
            print(f"[OK] POI搜索测试通过，完成 {len(search_cases)} 个搜索场景")
            self.test_results[test_name] = {"status": "PASS", "results": search_results}
            return True
            
        except Exception as e:
            print(f"[ERROR] POI搜索测试失败: {e}")
            self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
            return False

    async def test_weather_query(self):
        """测试天气查询功能"""
        print("\n[测试5] 天气查询功能")
        
        test_name = "weather_query"
        # 分为预期成功和已知可能不稳定的城市
        success_cities = ["厦门", "福州", "北京", "上海"]
        flaky_cities = ["杭州"]  # 杭州的查询已知可能不稳定
        cities = success_cities + flaky_cities
        
        weather_results = []
        errors = []
        try:
            for city in cities:
                print(f"[INFO] 查询天气: {city}")
                
                result = await self.client.maps_weather(city)
                
                # 检查响应基本格式
                if "forecasts" not in result:
                    error_msg = f"天气查询失败，响应格式错误: {city}"
                    print(f"  ❌ {error_msg}")
                    errors.append(error_msg)
                    continue

                # 对于已知不稳定的城市，如果返回空数据，是可接受的
                if city in flaky_cities and not result["forecasts"]:
                    print(f"  🟡 {city}: 查询成功，但无天气数据 (符合预期)")
                    weather_results.append({"city": city, "status": "NO_DATA"})
                    continue

                # 对于预期成功的城市，必须返回数据
                if city in success_cities and not result["forecasts"]:
                    error_msg = f"天气查询失败，未返回预期数据: {city}"
                    print(f"  ❌ {error_msg}")
                    errors.append(error_msg)
                    continue
                
                forecasts = result["forecasts"]
                today_weather = forecasts[0]
                
                print(f"  🌤️ {city}: {today_weather['dayweather']} "
                      f"{today_weather['daytemp']}°/{today_weather['nighttemp']}°")
                
                weather_results.append({
                    "city": city,
                    "weather": today_weather["dayweather"],
                    "temp_high": today_weather["daytemp"],
                    "temp_low": today_weather["nighttemp"],
                    "forecast_days": len(forecasts)
                })
            
            if errors:
                raise Exception(f"天气查询中发生错误: {'; '.join(errors)}")

            print(f"[OK] 天气查询测试通过，已处理 {len(cities)} 个城市")
            self.test_results[test_name] = {"status": "PASS", "results": weather_results}
            return True
            
        except Exception as e:
            print(f"[ERROR] 天气查询测试失败: {e}")
            self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
            return False

    async def test_travel_scenario(self):
        """测试真实旅行场景"""
        print("\n[测试6] 真实旅行场景测试")
        
        test_name = "travel_scenario"
        scenario = {
            "query": "我周末要从福州去厦门玩两天",
            "origin": "福州",
            "destination": "厦门",
            "days": 2
        }
        
        try:
            print(f"[SCENARIO] {scenario['query']}")
            
            # 1. 地理编码
            print("  步骤1: 获取城市坐标")
            origin_geo = await self.client.maps_geo(scenario["origin"])
            dest_geo = await self.client.maps_geo(scenario["destination"])
            
            origin_location = origin_geo["results"][0]["location"]
            dest_location = dest_geo["results"][0]["location"]
            
            # 2. 路线规划
            print("  步骤2: 规划交通路线")
            route = await self.client.maps_direction_driving(
                origin=origin_location,
                destination=dest_location
            )
            
            distance = int(route["paths"][0]["distance"]) / 1000
            duration = int(route["paths"][0]["duration"]) / 60
            
            # 3. 目的地POI搜索
            print("  步骤3: 搜索目的地景点和服务")
            attractions = await self.client.maps_text_search(
                keywords="景点", city=scenario["destination"]
            )
            restaurants = await self.client.maps_text_search(
                keywords="美食", city=scenario["destination"]
            )
            hotels = await self.client.maps_text_search(
                keywords="酒店", city=scenario["destination"]
            )
            
            # 4. 天气查询
            print("  步骤4: 查询目的地天气")
            weather = await self.client.maps_weather(scenario["destination"])
            
            # 5. 生成场景总结
            weather_summary = {
                "today": "N/A",
                "temp": "N/A"
            }
            if weather.get("forecasts"):
                today_forecast = weather["forecasts"][0]
                weather_summary["today"] = today_forecast.get("dayweather", "N/A")
                weather_summary["temp"] = f"{today_forecast.get('daytemp', '?')}°/{today_forecast.get('nighttemp', '?')}°"

            scenario_result = {
                "origin": {"city": scenario["origin"], "location": origin_location},
                "destination": {"city": scenario["destination"], "location": dest_location},
                "route": {"distance_km": round(distance, 1), "duration_min": round(duration, 0)},
                "pois": {
                    "attractions": len(attractions.get("pois", [])),
                    "restaurants": len(restaurants.get("pois", [])),
                    "hotels": len(hotels.get("pois", []))
                },
                "weather": weather_summary
            }
            
            print(f"[OK] 旅行场景测试完成:")
            print(f"  🚗 交通: {distance:.1f}公里, {duration:.0f}分钟")
            print(f"  🏛️ 景点: {scenario_result['pois']['attractions']}个")
            print(f"  🍽️ 美食: {scenario_result['pois']['restaurants']}个")
            print(f"  🏨 住宿: {scenario_result['pois']['hotels']}个")
            print(f"  🌤️ 天气: {scenario_result['weather']['today']} {scenario_result['weather']['temp']}")
            
            self.test_results[test_name] = {"status": "PASS", "scenario": scenario_result}
            return True
            
        except Exception as e:
            print(f"[ERROR] 旅行场景测试失败: {e}")
            self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
            return False

    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("🎯 MCP工具集成测试总结")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result["status"] == "PASS")
        failed_tests = total_tests - passed_tests
        
        print(f"📊 测试统计:")
        print(f"  总计: {total_tests}")
        print(f"  通过: {passed_tests}")
        print(f"  失败: {failed_tests}")
        print(f"  成功率: {(passed_tests/total_tests*100):.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"  {status_icon} {test_name}: {result['status']}")
            if result["status"] == "FAIL":
                print(f"     错误: {result.get('error', '未知错误')}")
        
        return passed_tests == total_tests


async def run_mcp_tests():
    """运行MCP集成测试"""
    print("🚀 开始MCP工具集成测试")
    print("="*60)
    
    test_suite = MCPTestSuite()
    
    try:
        # 初始化测试环境
        if not await test_suite.setup():
            print("❌ 测试环境初始化失败")
            return False
        
        # 执行测试用例
        test_methods = [
            test_suite.test_basic_connection,
            test_suite.test_geocoding,
            test_suite.test_route_planning,
            test_suite.test_poi_search,
            test_suite.test_weather_query,
            test_suite.test_travel_scenario
        ]
        
        for test_method in test_methods:
            try:
                await test_method()
            except Exception as e:
                print(f"[CRITICAL] 测试方法 {test_method.__name__} 发生异常: {e}")
        
        # 打印测试总结
        success = test_suite.print_test_summary()
        
        return success
        
    finally:
        await test_suite.teardown()


if __name__ == "__main__":
    try:
        success = asyncio.run(run_mcp_tests())
        if success:
            print("\n🎉 所有MCP测试通过!")
            exit(0)
        else:
            print("\n💥 部分MCP测试失败!")
            exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行异常: {e}")
        import traceback
        traceback.print_exc()
        exit(1) 