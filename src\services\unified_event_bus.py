"""
统一事件总线 (V3.0 - 统一架构版)

基于Redis Pub/Sub实现的事件驱动架构核心组件，负责：
1. 事件发布与订阅（Redis Pub/Sub）
2. 任务状态持久化（Redis HASH）
3. 与StandardAgentState的双向同步
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class UnifiedEventBus:
    """统一事件总线
    
    核心功能：
    1. 消息总线：基于Redis Pub/Sub的实时事件发布
    2. 实时作战室：基于Redis HASH的任务状态持久化
    3. 状态同步：与StandardAgentState的双向同步
    4. 事件标准化：统一的SSE事件格式
    """
    
    def __init__(self, redis_client, task_ttl: int = 3600):
        """初始化事件总线

        Args:
            redis_client: Redis客户端实例
            task_ttl: 任务状态TTL（秒），默认1小时
        """
        # 处理不同类型的Redis客户端
        if hasattr(redis_client, '_redis') and redis_client._redis is not None:
            # 自定义RedisClient包装类，提取内部的redis实例
            self.redis = redis_client._redis
            logger.info(f"使用RedisClient包装类的内部redis实例: {type(self.redis)}")
        elif hasattr(redis_client, 'client') and not callable(redis_client.client):
            # 其他包装对象，提取真实的Redis客户端
            self.redis = redis_client.client
            logger.info(f"使用包装对象的client属性: {type(self.redis)}")
        elif hasattr(redis_client, 'publish') and hasattr(redis_client, 'hset') and callable(redis_client.publish):
            # 直接是Redis客户端实例
            self.redis = redis_client
            logger.info(f"使用直接的Redis客户端实例: {type(self.redis)}")
        else:
            # 可能是模拟对象或其他类型，记录警告但继续
            logger.warning(f"Redis客户端类型未知或不完整: {type(redis_client)}")
            self.redis = redis_client

        self.task_ttl = task_ttl
        logger.info(f"UnifiedEventBus initialized with TTL: {task_ttl}s, Final Redis type: {type(self.redis)}")
    
    async def _publish(self, task_id: str, event_data: Dict[str, Any]):
        """发布事件到Redis频道

        Args:
            task_id: 任务ID
            event_data: 事件数据
        """
        try:
            if not hasattr(self.redis, 'publish') or not callable(self.redis.publish):
                logger.warning(f"Redis客户端没有有效的publish方法，跳过事件发布: {type(self.redis)}")
                return

            channel = f"task_channel:{task_id}"
            message = json.dumps(event_data, ensure_ascii=False)

            # 调用publish方法
            result = await self.redis.publish(channel, message)
            logger.debug(f"Event published to {channel}: {event_data.get('event', 'unknown')}, subscribers: {result}")
        except Exception as e:
            logger.error(f"Failed to publish event to {task_id}: {str(e)}")
    
    async def _update_task_status(self, task_id: str, updates: Dict[str, Any]):
        """更新Redis中的任务状态
        
        Args:
            task_id: 任务ID
            updates: 状态更新字典
        """
        try:
            if not hasattr(self.redis, 'hset'):
                logger.warning(f"Redis客户端没有hset方法，跳过状态更新: {type(self.redis)}")
                return

            key = f"task_status:{task_id}"
            updates["last_updated"] = datetime.utcnow().isoformat()

            # 将复杂对象序列化为JSON字符串
            serialized_updates = {}
            for k, v in updates.items():
                if isinstance(v, (dict, list)):
                    serialized_updates[k] = json.dumps(v, ensure_ascii=False)
                else:
                    serialized_updates[k] = str(v)

            await self.redis.hset(key, mapping=serialized_updates)
            await self.redis.expire(key, self.task_ttl)
            logger.debug(f"Task status updated: {task_id}")
        except Exception as e:
            logger.error(f"Failed to update task status: {str(e)}")
    
    async def sync_from_agent_state(self, task_id: str, agent_state: Dict[str, Any]):
        """从StandardAgentState同步状态到Redis
        
        Args:
            task_id: 任务ID
            agent_state: StandardAgentState字典
        """
        try:
            state_mapping = {
                "current_phase": agent_state.get("current_phase", "unknown"),
                "current_action": agent_state.get("current_action", {}),
                "tool_results": agent_state.get("tool_results", {}),
                "daily_plans": agent_state.get("daily_plans", {}),
                "planning_log": agent_state.get("planning_log", []),
                "is_completed": agent_state.get("is_completed", False),
                "has_error": agent_state.get("has_error", False),
                "error_message": agent_state.get("error_message", ""),
                "framework_analysis": agent_state.get("framework_analysis", {}),
                "preference_analysis": agent_state.get("preference_analysis", {}),
                "consolidated_intent": agent_state.get("consolidated_intent", {})
            }
            
            await self._update_task_status(task_id, state_mapping)
            logger.debug(f"Agent state synced to Redis: {task_id}")
        except Exception as e:
            logger.error(f"Failed to sync agent state: {str(e)}")
    
    async def initialize_task(self, task_id: str, initial_state: Dict[str, Any]):
        """初始化任务状态
        
        Args:
            task_id: 任务ID
            initial_state: 初始状态
        """
        try:
            await self.sync_from_agent_state(task_id, initial_state)
            await self._update_task_status(task_id, {"overall_status": "pending"})
            logger.info(f"Task {task_id} initialized")
        except Exception as e:
            logger.error(f"Failed to initialize task: {str(e)}")
    
    async def notify_phase_start(self, task_id: str, phase_name: str, title: str, message: str):
        """通知阶段开始
        
        Args:
            task_id: 任务ID
            phase_name: 阶段名称
            title: 阶段标题
            message: 阶段描述
        """
        event = {
            "event": "phase_start",
            "data": {
                "phase_name": phase_name,
                "title": title,
                "message": message,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)
        await self._update_task_status(task_id, {
            "overall_status": "running",
            "current_phase": phase_name
        })
    
    async def notify_phase_end(self, task_id: str, phase_name: str, status: str, result: Dict[str, Any] = None):
        """通知阶段结束
        
        Args:
            task_id: 任务ID
            phase_name: 阶段名称
            status: 执行状态（success/error）
            result: 阶段结果
        """
        event = {
            "event": "phase_end",
            "data": {
                "phase_name": phase_name,
                "status": status,
                "result": result or {},
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)
    
    async def notify_tool_execution(self, task_id: str, tool_name: str, event_type: str, **kwargs):
        """通知工具执行事件
        
        Args:
            task_id: 任务ID
            tool_name: 工具名称
            event_type: 事件类型（tool_start/tool_end）
            **kwargs: 额外的事件数据
        """
        event = {
            "event": event_type,
            "data": {
                "tool_name": tool_name,
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            }
        }
        await self._publish(task_id, event)
    
    async def notify_planning_log(self, task_id: str, message: str, reasoning_step: int):
        """通知规划思考日志
        
        Args:
            task_id: 任务ID
            message: 思考内容
            reasoning_step: 推理步骤
        """
        event = {
            "event": "PLANNING_LOG",
            "data": {
                "message": message,
                "reasoning_step": reasoning_step,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)
    
    async def notify_itinerary_update(self, task_id: str, day: int, activity: Dict[str, Any]):
        """通知行程更新
        
        Args:
            task_id: 任务ID
            day: 天数
            activity: 活动信息
        """
        event = {
            "event": "ITINERARY_UPDATE",
            "data": {
                "day": day,
                "activity": activity,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)
    
    async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        """通知最终结果
        
        Args:
            task_id: 任务ID
            final_data: 最终结果数据
        """
        event = {
            "event": "complete",
            "data": {
                **final_data,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)
        await self._publish(task_id, {"event": "eos", "timestamp": datetime.utcnow().isoformat()})
        
        await self._update_task_status(task_id, {
            "overall_status": "completed",
            "final_result": final_data
        })
    
    async def notify_error(self, task_id: str, error_message: str, phase_name: str = "unknown", tool_name: str = None):
        """通知错误
        
        Args:
            task_id: 任务ID
            error_message: 错误信息
            phase_name: 出错阶段
            tool_name: 出错工具（可选）
        """
        event = {
            "event": "error",
            "data": {
                "phase_name": phase_name,
                "tool_name": tool_name,
                "message": error_message,
                "error_code": "EXECUTION_ERROR",
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)
        await self._publish(task_id, {"event": "eos", "timestamp": datetime.utcnow().isoformat()})
        
        await self._update_task_status(task_id, {
            "overall_status": "failed",
            "error_info": error_message
        })
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态字典或None
        """
        try:
            key = f"task_status:{task_id}"
            status = await self.redis.hgetall(key)
            
            if not status:
                return None
            
            # 反序列化JSON字段
            result = {}
            for k, v in status.items():
                if isinstance(v, bytes):
                    v = v.decode('utf-8')
                
                # 尝试解析JSON字段
                if k in ['current_action', 'tool_results', 'daily_plans', 'planning_log', 
                        'framework_analysis', 'preference_analysis', 'consolidated_intent', 'final_result']:
                    try:
                        result[k] = json.loads(v) if v else {}
                    except json.JSONDecodeError:
                        result[k] = v
                else:
                    result[k] = v
            
            return result
        except Exception as e:
            logger.error(f"Failed to get task status: {str(e)}")
            return None
