# Agent实时推送架构重构方案 (V3.0 - 统一架构版)

本文档旨在提出一套基于**事件驱动**和**责任分离**原则的全新实时推送架构，与统一的UnifiedToolRegistry和StandardAgentState深度集成，从根本上解决现有推送机制的耦合问题，并大幅提升系统的健壮性、可维护性和可扩展性。

## 一、问题分析：从表面到根源

### 1. 表面问题
- **SSE推送延迟**：在IO密集型操作（如数据库查询、多工具调用）中，开始和结束事件被一同缓冲，导致前端长时间"假死"。
- **前端体验不佳**：用户无法获得实时的进度反馈，可能因等待时间过长而重复提交请求。

### 2. 架构根源
现有方案（`SSEStreamAdapter`）的核心问题在于**责任不清**和**高度耦合**：

- **业务逻辑泄露 (`Business Logic Leakage`)**: `SSEStreamAdapter`被迫承担了业务逻辑判断的责任。它需要通过检查LangGraph `state`中的字段来"猜测"业务走到了哪一步。
- **高度耦合 (`High Coupling`)**: 推送逻辑与LangGraph的节点名称、状态结构强绑定。一旦业务流程发生变化，`SSEStreamAdapter`必须同步修改，维护成本极高。
- **可复用性差 (`Poor Reusability`)**: 如果要开发新的Agent，当前的`SSEStreamAdapter`几乎无法复用。

## 二、新架构蓝图：解耦、实时、健壮

### 1. 核心组件
- **`UnifiedEventBus` (统一事件总线)**: 基于Redis Pub/Sub的标准化事件发布与状态管理服务，与StandardAgentState深度集成。
- **LangGraph Nodes (业务逻辑单元)**: 在执行关键业务逻辑前后，调用`UnifiedEventBus`来宣告"我开始了"或"我结束了"。
- **Redis (消息总线 & 实时作战室)**: 同时作为解耦的**Pub/Sub消息总线**和**HASH状态存储**。
- **API Endpoint (事件订阅与推送者)**: API流式接口订阅Redis频道，接收事件，并将其格式化为SSE推送给前端。
- **UnifiedToolRegistry集成**: 工具执行事件自动发布到事件总线。

### 2. 数据流转图

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant API as API Endpoint
    participant BG as LangGraph (Background Task)
    participant UTR as UnifiedToolRegistry
    participant UEB as UnifiedEventBus
    participant Redis as Redis Pub/Sub & HASH

    FE->>+API: 发起流式规划请求 (/v3/plan/stream)
    API->>UEB: 调用 initialize_task(task_id)
    UEB->>Redis: 创建 HASH 'task_status:{task_id}' 并设置TTL
    API->>BG: 启动后台规划任务 (task_id)
    API->>Redis: 订阅频道 (SUBSCRIBE channel:{task_id})
    API-->>-FE: 建立SSE长连接

    loop 规划流程
        BG->>UEB: 调用 notify_phase_start("framework_analysis", ...)
        UEB->>Redis: 更新 HASH 'task_status:{task_id}'
        UEB->>Redis: PUBLISH 'task:{task_id}', event_data
        Redis-->>API: 收到 'phase_start' 事件
        API-->>FE: 推送SSE phase_start 事件

        BG->>UTR: 执行Action Tool (search_poi)
        UTR->>UEB: 自动发布 tool_start 事件
        UEB->>Redis: PUBLISH 'task:{task_id}', tool_event
        Redis-->>API: 收到 'tool_start' 事件
        API-->>FE: 推送SSE tool_start 事件

        UTR->>UTR: 工具执行完成
        UTR->>UEB: 自动发布 tool_end 事件
        UEB->>Redis: PUBLISH 'task:{task_id}', tool_event
        Redis-->>API: 收到 'tool_end' 事件
        API-->>FE: 推送SSE tool_end 事件

        BG->>UEB: 调用 notify_phase_end("framework_analysis", ...)
        UEB->>Redis: 更新 HASH 'task_status:{task_id}'
        UEB->>Redis: PUBLISH 'task:{task_id}', event_data
        Redis-->>API: 收到 'phase_end' 事件
        API-->>FE: 推送SSE phase_end 事件

        BG->>UEB: 调用 notify_itinerary_update(day, activity)
        UEB->>Redis: 更新 HASH 'task_status:{task_id}'
        UEB->>Redis: PUBLISH 'task:{task_id}', event_data
        Redis-->>API: 收到 'ITINERARY_UPDATE' 事件
        API-->>FE: 推送SSE ITINERARY_UPDATE 事件
    end
```

## 三、任务状态持久化：Redis实时作战室 (V3.0)

为了实现系统的可观测性、可恢复性和健壮性，除了Pub/Sub消息外，我们必须在Redis中为每个任务维护一个持久化的状态记录，与StandardAgentState保持同步。

### 1. 数据结构：Redis HASH (增强版)

-   **Key**: `task_status:{task_id}` (例如: `task_status:task_guest_1688888888`)
-   **Type**: `HASH`

| Field | Type | Description | Example |
| :--- | :--- | :--- | :--- |
| `overall_status` | string | 整个任务的宏观状态。枚举值: `pending`, `running`, `completed`, `failed` | `running` |
| `current_phase` | string | 当前正在执行的阶段 | `framework_analysis` |
| `phases_status` | JSON string | 所有阶段的详细状态记录 | `{"framework_analysis": {"status": "running", ...}}` |
| `current_action` | JSON string | 当前正在执行的Action Tool | `{"tool_name": "search_poi", "parameters": {...}}` |
| `tool_results` | JSON string | 工具执行结果缓存 | `{"search_poi": [...], "get_weather": {...}}` |
| `daily_plans` | JSON string | 按天存储的POI规划 | `{"1": [...], "2": [...]}` |
| `planning_log` | JSON string | 规划思考日志 | `["开始分析用户意图", "搜索北京景点", ...]` |
| `last_updated` | string | 状态最后更新的ISO 8601时间戳 | `2023-07-09T12:30:05.123Z` |
| `final_result` | JSON string | 任务成功完成后，存储最终的结构化结果 | `{"itinerary": {"title": "..."}}` |
| `error_info` | string | 任务失败时，存储详细的错误信息 | `LLM service timeout` |

### 2. 状态同步机制

`UnifiedEventBus`确保Redis HASH与LangGraph的`StandardAgentState`保持双向同步：

- **写入同步**: 每次LangGraph状态更新时，自动同步到Redis HASH
- **读取同步**: 任务恢复时，从Redis HASH重建StandardAgentState
- **增量更新**: 只同步变化的字段，提高性能

## 四、统一的SSE事件格式定义 (V3.0)

所有通过SSE推送的事件都必须严格遵守以下JSON结构，与StandardAgentState字段保持一致。

### 1. 阶段事件 (Phase Events)

*   **阶段开始 (`phase_start`)**:
    ```json
    { 
      "event": "phase_start", 
      "data": { 
        "phase_name": "framework_analysis", 
        "title": "核心框架分析", 
        "message": "正在分析您的核心需求和旅行框架...",
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

*   **阶段结束 (`phase_end`)**:
    ```json
    { 
      "event": "phase_end", 
      "data": { 
        "phase_name": "framework_analysis", 
        "status": "success", 
        "result": { 
          "destinations": ["北京"], 
          "days": 3, 
          "travel_theme": ["亲子", "文化"] 
        },
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

### 2. 工具事件 (Tool Events)

*   **工具开始 (`tool_start`)**:
    ```json
    { 
      "event": "tool_start", 
      "data": { 
        "tool_name": "search_around", 
        "parameters": {"location": "116.397,39.918", "keywords": "餐厅", "radius": 1000}, 
        "description": "在 故宫博物院 附近搜索 餐厅",
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

*   **工具结束 (`tool_end`)**:
    ```json
    { 
      "event": "tool_end", 
      "data": { 
        "tool_name": "search_poi", 
        "status": "success", 
        "result": {"pois": [...]}, 
        "execution_time": 1.2,
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

### 3. 规划事件 (Planning Events)

*   **行程更新 (`ITINERARY_UPDATE`)**:
    ```json
    { 
      "event": "ITINERARY_UPDATE", 
      "data": { 
        "day": 1, 
        "activity": { 
          "poi_id": "B000A8UIN8",
          "name": "故宫博物院", 
          "poi_type": "attraction",
          "location": "116.397029,39.917839",
          "introduction": "明清两代的皇家宫殿，中国古代宫廷建筑之精华。",
          "suggested_time": "上午 09:00 - 12:00",
          "rating": 4.9,
          "phone_number": "4009501925",
          "image_urls": [
              "http://store.is.autonavi.com/showpic/2f968490d105bb2741e17f90b85c6b79"
          ]
        },
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

*   **思考日志 (`PLANNING_LOG`)**:
    ```json
    { 
      "event": "PLANNING_LOG", 
      "data": { 
        "message": "上午的故宫游览已规划完毕（09:00-12:00），时间已至中午，我将在故宫附近为您寻找午餐选择。", 
        "reasoning_step": 3,
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

### 4. 系统事件 (System Events)

*   **规划完成 (`complete`)**:
    ```json
    { 
      "event": "complete", 
      "data": { 
        "final_itinerary": { "title": "北京3日亲子游", "days": [...] },
        "total_execution_time": 45.6,
        "tools_used": ["search_poi", "get_weather", "get_driving_route"],
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

*   **发生错误 (`error`)**:
    ```json
    { 
      "event": "error", 
      "data": { 
        "phase_name": "preference_analysis", 
        "tool_name": "search_poi", 
        "message": "API调用超时，请稍后重试", 
        "error_code": "TIMEOUT",
        "timestamp": "2023-07-09T12:30:05.123Z"
      } 
    }
    ```

*   **流结束信号 (`eos`)**:
    ```json
    { "event": "eos", "timestamp": "2023-07-09T12:30:05.123Z" }
    ```

## 五、实施路径图与核心代码 (V3.0)

### 1. **创建 `UnifiedEventBus`**
-   **位置**: `src/services/unified_event_bus.py`
-   **任务**: 实现与StandardAgentState深度集成的事件总线服务。

    ```python
# src/services/unified_event_bus.py
    import json
    import logging
    from datetime import datetime
from typing import Dict, Any, Optional, List
    from src.database.redis_client import RedisClient
from src.tools.unified_registry import unified_registry

    logger = logging.getLogger(__name__)

class UnifiedEventBus:
        def __init__(self, redis_client: RedisClient, task_ttl: int = 3600):
            self.redis = redis_client.client
            self.task_ttl = task_ttl

        async def _publish(self, task_id: str, event_data: Dict[str, Any]):
        """发布事件到Redis频道"""
            channel = f"task_channel:{task_id}"
            message = json.dumps(event_data, ensure_ascii=False)
            await self.redis.publish(channel, message)

        async def _update_task_status(self, task_id: str, updates: Dict[str, Any]):
        """更新Redis中的任务状态"""
            key = f"task_status:{task_id}"
            updates["last_updated"] = datetime.utcnow().isoformat()
            await self.redis.hset(key, mapping=updates)
            await self.redis.expire(key, self.task_ttl)

    async def sync_from_agent_state(self, task_id: str, agent_state: Dict[str, Any]):
        """从StandardAgentState同步状态到Redis"""
        state_mapping = {
            "current_phase": agent_state.get("current_phase", "unknown"),
            "current_action": json.dumps(agent_state.get("current_action", {})),
            "tool_results": json.dumps(agent_state.get("tool_results", {})),
            "daily_plans": json.dumps(agent_state.get("daily_plans", {})),
            "planning_log": json.dumps(agent_state.get("planning_log", [])),
            "is_completed": str(agent_state.get("is_completed", False)),
            "has_error": str(agent_state.get("has_error", False)),
            "error_message": agent_state.get("error_message", "")
        }
        
        await self._update_task_status(task_id, state_mapping)

    async def initialize_task(self, task_id: str, initial_state: Dict[str, Any]):
        """初始化任务状态"""
        await self.sync_from_agent_state(task_id, initial_state)
        await self._update_task_status(task_id, {"overall_status": "pending"})
        logger.info(f"Task {task_id} initialized with TTL: {self.task_ttl}s")

    async def notify_phase_start(self, task_id: str, phase_name: str, title: str, message: str):
        """通知阶段开始"""
        event = {
            "event": "phase_start",
            "data": {
                "phase_name": phase_name,
                "title": title,
                "message": message,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)
        await self._update_task_status(task_id, {
            "overall_status": "running",
            "current_phase": phase_name
        })

    async def notify_phase_end(self, task_id: str, phase_name: str, status: str, result: Dict[str, Any] = None):
        """通知阶段结束"""
        event = {
            "event": "phase_end",
            "data": {
                "phase_name": phase_name,
                "status": status,
                "result": result or {},
                "timestamp": datetime.utcnow().isoformat()
            }
        }
            await self._publish(task_id, event)
            
    async def notify_tool_execution(self, task_id: str, tool_name: str, event_type: str, **kwargs):
        """通知工具执行事件"""
        event = {
            "event": event_type,  # tool_start 或 tool_end
            "data": {
                "tool_name": tool_name,
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            }
        }
        await self._publish(task_id, event)

    async def notify_planning_log(self, task_id: str, message: str, reasoning_step: int):
        """通知规划思考日志"""
        event = {
            "event": "PLANNING_LOG",
            "data": {
                "message": message,
                "reasoning_step": reasoning_step,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
            await self._publish(task_id, event)
            
    async def notify_itinerary_update(self, task_id: str, day: int, activity: Dict[str, Any]):
        """通知行程更新"""
        event = {
            "event": "ITINERARY_UPDATE",
            "data": {
                "day": day,
                "activity": activity,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await self._publish(task_id, event)

        async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        """通知最终结果"""
        event = {
            "event": "complete",
            "data": {
                **final_data,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
            await self._publish(task_id, event)
        await self._publish(task_id, {"event": "eos", "timestamp": datetime.utcnow().isoformat()})
            
        await self._update_task_status(task_id, {
                "overall_status": "completed",
                "final_result": json.dumps(final_data)
        })

    async def notify_error(self, task_id: str, error_message: str, phase_name: str = "unknown", tool_name: str = None):
        """通知错误"""
        event = {
            "event": "error",
            "data": {
                "phase_name": phase_name,
                "tool_name": tool_name,
                "message": error_message,
                "error_code": "EXECUTION_ERROR",
                "timestamp": datetime.utcnow().isoformat()
            }
        }
            await self._publish(task_id, event)
        await self._publish(task_id, {"event": "eos", "timestamp": datetime.utcnow().isoformat()})
            
        await self._update_task_status(task_id, {
                "overall_status": "failed",
                "error_info": error_message
        })
```

### 2. **增强 `UnifiedToolRegistry` 以支持事件发布**

```python
# 在 src/tools/unified_registry.py 中添加事件支持
class UnifiedToolRegistry:
    def __init__(self):
        self._action_tools: Dict[str, Callable] = {}
        self._action_schemas: Dict[str, Dict] = {}
        self._planner_tools: Dict[str, Callable] = {}
        self._event_bus: Optional[Any] = None

    def set_event_bus(self, event_bus):
        """设置事件总线"""
        self._event_bus = event_bus

    async def execute_action_tool(self, name: str, task_id: str = None, **kwargs) -> Any:
        """执行Action Tool并发布事件"""
        if name not in self._action_tools:
            raise ValueError(f"Action tool '{name}' not found.")
        
        tool_func = self._action_tools[name]
        
        # 发布工具开始事件
        if self._event_bus and task_id:
            await self._event_bus.notify_tool_execution(
                task_id, name, "tool_start", 
                parameters=kwargs,
                description=tool_func.__doc__ or f"Execute {name}"
            )
        
        try:
            start_time = time.time()
            
            if inspect.iscoroutinefunction(tool_func):
                result = await tool_func(**kwargs)
            else:
                result = tool_func(**kwargs)
            
            execution_time = time.time() - start_time
            
            # 发布工具结束事件
            if self._event_bus and task_id:
                await self._event_bus.notify_tool_execution(
                    task_id, name, "tool_end",
                    status="success",
                    result=result,
                    execution_time=execution_time
                )
            
            return result
            
        except Exception as e:
            # 发布工具错误事件
            if self._event_bus and task_id:
                await self._event_bus.notify_tool_execution(
                    task_id, name, "tool_end",
                    status="error",
                    error_message=str(e)
                )
            raise
```

### 3. **实现新API端点 (V3.0)**

    ```python
    # src/api/travel_planner.py
    import asyncio
    import json
    import time
    from fastapi import APIRouter, Depends, Request, BackgroundTasks
    from sse_starlette.sse import EventSourceResponse
from src.core.config import settings
    from src.database.redis_client import RedisClient, get_redis_client
from src.services.unified_event_bus import UnifiedEventBus
from src.agents.travel_planner_lg import TravelPlannerAgent  # 新的统一Agent
from src.tools.unified_registry import unified_registry

    router = APIRouter()

    async def redis_event_generator(task_id: str, redis_client: RedisClient):
        """监听Redis频道并生成SSE事件"""
        channel = f"task_channel:{task_id}"
        pubsub = redis_client.client.pubsub()
        await pubsub.subscribe(channel)
        try:
            while True:
                message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=60)
                if message and message['data']:
                    data = message['data'].decode('utf-8')
                    yield f"data: {data}\n\n"
                    if '"event": "eos"' in data:
                        break
                await asyncio.sleep(0.01)
        finally:
            await pubsub.unsubscribe(channel)

@router.post("/v3/plan/stream", name="流式旅行规划 V3.0 - 统一架构版")
async def plan_travel_stream_v3(
        request_data: dict,
        background_tasks: BackgroundTasks,
        redis_client: RedisClient = Depends(get_redis_client)
    ):
    """V3版流式规划接口，基于统一架构"""
        user_id = request_data.get("user_id", "guest")
        query = request_data.get("query")
        task_id = f"task_{user_id}_{int(time.time())}"

    # 创建统一事件总线
        task_ttl = settings.REDIS_TASK_TTL or 3600
    event_bus = UnifiedEventBus(redis_client, task_ttl=task_ttl)
        
    # 设置工具注册表的事件总线
    unified_registry.set_event_bus(event_bus)

    # 初始化任务状态
    initial_state = {
        "task_id": task_id,
        "current_phase": "initialization",
        "is_completed": False,
        "has_error": False,
        "planning_log": [],
        "daily_plans": {},
        "tool_results": {}
    }
    await event_bus.initialize_task(task_id, initial_state)

    # 启动后台任务
    agent = TravelPlannerAgent()
        background_tasks.add_task(
        agent.run_unified,
        query=query, 
        user_id=user_id, 
        task_id=task_id,
        event_bus=event_bus
        )
        
        return EventSourceResponse(redis_event_generator(task_id, redis_client))
    ```

## 六、与其他重构文档的集成 (V3.0)

### 1. 与规划距离逻辑.md的集成

**统一状态管理**: `UnifiedEventBus`直接操作`StandardAgentState`，确保LangGraph状态与Redis状态的完全同步。

**工具事件自动化**: 通过增强的`UnifiedToolRegistry`，所有Action Tool的执行都会自动发布相应的事件，无需手动调用。

### 2. 与意图整合.md的集成

**阶段事件映射**: 两步意图分析的每个阶段都对应特定的`phase_start`和`phase_end`事件：
- `framework_analysis` → 核心框架分析阶段
- `preference_analysis` → 偏好分析阶段

### 3. 与旅游搭子UI.md的集成

**事件格式标准化**: 所有SSE事件都包含`timestamp`字段，支持前端的时序显示和动画效果。

**组件状态映射**: 前端组件可以直接根据事件中的`phase_name`、`tool_name`等字段更新对应的UI状态。

这份重构计划将AutoPilot AI的实时推送能力提升到企业级水准，实现了真正的事件驱动架构，为后续的多Agent协作和复杂业务场景奠定了坚实的基础。

## 七、代码实现现状分析 (Current Implementation Analysis)

本章节旨在将文档中定义的`UnifiedEventBus`设计与项目中的实际代码进行映射和验证。

*   **文件路径**: `src/services/unified_event_bus.py`
*   **对应组件**: `class UnifiedEventBus`
*   **现状分析**:
    *   **完全实现**: 项目中存在一个功能完整且健壮的`UnifiedEventBus`类，其路径与本文档的设想一致。
    *   **高度一致**: 代码中的类和方法签名（如 `notify_phase_start`, `notify_itinerary_update`, `sync_from_agent_state` 等）与本文档第五章“实施路径图与核心代码”中定义的设计高度吻合。
    *   **功能超越**: 实际代码比文档中的伪代码更为健壮，增加了对不同Redis客户端类型的兼容性处理和详细的错误日志记录。
*   **结论**:
    *   **无需修改**: `UnifiedEventBus` 的代码实现已达到或超过了本文档的设计要求。
    *   **可以直接复用**: 后端开发人员可以完全信赖并直接使用 `src/services/unified_event_bus.py` 中的 `UnifiedEventBus` 来实现本文档描述的事件推送功能。
