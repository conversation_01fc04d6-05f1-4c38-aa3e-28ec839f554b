"""
旅行规划系统V3重构版核心组件单元测试

测试重构后的关键组件：
1. 两阶段工作流程节点
2. 统一工具注册表
3. V3 API端点
4. ICP迭代规划循环
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# 导入被测试的组件
from src.tools.unified_registry import unified_registry
from src.agents.travel_planner_lg.nodes import (
    run_framework_analysis,
    run_preference_analysis,
    prepare_planning_context,
    planner_agent_node,
    tool_executor_node,
    route_icp_action
)
from src.agents.travel_planner_lg.state import StandardAgentState


class TestUnifiedToolRegistry:
    """统一工具注册表测试"""

    def test_tool_registry_initialization(self):
        """测试工具注册表初始化"""
        assert unified_registry is not None
        assert hasattr(unified_registry, '_action_tools')
        assert hasattr(unified_registry, '_planner_tools')
        assert hasattr(unified_registry, '_action_schemas')

    def test_planner_tools_registration(self):
        """测试Planner Tools注册"""
        # 导入工具模块以触发注册
        import src.tools.travel_planner.consolidated_tools
        import src.tools.travel_planner.icp_tools
        from src.agents.services.amap_service import AmapService

        planner_tools = unified_registry.get_all_planner_tools()
        
        # 验证关键Planner Tools已注册
        expected_tools = [
            "format_framework_analysis_prompt",
            "format_preference_analysis_prompt",
            "create_consolidated_intent",
            "prepare_icp_context",
            "extract_key_insights",
            "generate_planning_thought",
            "select_next_action",
            "observe_action_result",
            "update_planning_state",
            "check_planning_completion"
        ]
        
        for tool_name in expected_tools:
            assert tool_name in planner_tools, f"Planner tool {tool_name} not registered"
            assert callable(planner_tools[tool_name]), f"Tool {tool_name} is not callable"
    
    def test_action_tools_registration(self):
        """测试Action Tools注册"""
        # 确保工具已注册
        import src.tools.travel_planner.consolidated_tools
        import src.tools.travel_planner.icp_tools
        from src.agents.services.amap_service import AmapService

        action_tool_names = unified_registry.get_action_tool_names()
        
        # 验证关键Action Tools已注册
        expected_tools = ["search_poi", "geocode", "get_driving_route"]
        
        for tool_name in expected_tools:
            assert tool_name in action_tool_names, f"Action tool {tool_name} not registered"
    
    def test_tool_info_retrieval(self):
        """测试工具信息获取"""
        tool_info = unified_registry.get_tool_info()
        
        assert "action_tools_count" in tool_info
        assert "planner_tools_count" in tool_info
        assert "action_tools" in tool_info
        assert "planner_tools" in tool_info
        assert "event_bus_connected" in tool_info
        
        assert tool_info["action_tools_count"] > 0
        assert tool_info["planner_tools_count"] > 0


class TestTwoStageWorkflowNodes:
    """两阶段工作流程节点测试"""
    
    def setup_method(self):
        """设置测试环境"""
        mock_event_bus = AsyncMock()
        mock_event_bus.notify_phase_start = AsyncMock()
        mock_event_bus.notify_phase_end = AsyncMock()
        mock_event_bus.notify_error = AsyncMock()

        self.mock_state = {
            "task_id": "test_task_123",
            "messages": [{"content": "我想去北京玩3天，喜欢历史文化景点"}],
            "user_profile": {"preferences": {"travel_style": "cultural"}},
            "notification_service": mock_event_bus
        }
    
    @pytest.mark.asyncio
    async def test_framework_analysis_node(self):
        """测试核心框架分析节点"""
        # Mock工具注册表
        with patch.object(unified_registry, 'get_planner_tool') as mock_get_tool:
            mock_format_tool = Mock(return_value="formatted prompt")
            mock_get_tool.return_value = mock_format_tool
            
            # Mock LLM调用
            with patch('src.core.llm_manager.LLMManager') as mock_llm_manager:
                mock_llm = AsyncMock()
                mock_llm.call_llm.return_value = {
                    "content": json.dumps({
                        "core_intent": {
                            "destinations": ["北京"],
                            "travel_days": 3,
                            "travel_theme": ["文化", "历史"]
                        }
                    })
                }
                mock_llm_manager.return_value = mock_llm
                
                # 执行测试
                result = await run_framework_analysis(self.mock_state)
                
                # 验证结果
                assert "framework_analysis" in result
                assert "current_phase" in result
                assert result["current_phase"] == "framework_analysis_completed"
                assert "voice_text" in result
    
    @pytest.mark.asyncio
    async def test_preference_analysis_node(self):
        """测试个性化偏好分析节点"""
        # 添加framework_analysis结果到状态
        self.mock_state["framework_analysis"] = {
            "core_intent": {
                "destinations": ["北京"],
                "travel_days": 3
            }
        }
        
        # Mock工具注册表
        with patch.object(unified_registry, 'get_planner_tool') as mock_get_tool:
            mock_format_tool = Mock(return_value="formatted prompt")
            mock_get_tool.return_value = mock_format_tool
            
            # Mock LLM调用
            with patch('src.core.llm_manager.LLMManager') as mock_llm_manager:
                mock_llm = AsyncMock()
                mock_llm.call_llm.return_value = {
                    "content": json.dumps({
                        "attraction_preferences": {
                            "preferred_types": ["历史文化", "古建筑"],
                            "must_visit": ["故宫", "天坛"]
                        }
                    })
                }
                mock_llm_manager.return_value = mock_llm
                
                # 执行测试
                result = await run_preference_analysis(self.mock_state)
                
                # 验证结果
                assert "preference_analysis" in result
                assert "current_phase" in result
                assert result["current_phase"] == "preference_analysis_completed"
                assert "voice_text" in result
    
    @pytest.mark.asyncio
    async def test_prepare_planning_context_node(self):
        """测试规划上下文准备节点"""
        # 添加前置分析结果
        self.mock_state.update({
            "framework_analysis": {"core_intent": {"destinations": ["北京"]}},
            "preference_analysis": {"attraction_preferences": {"preferred_types": ["历史文化"]}}
        })
        
        # Mock工具注册表
        with patch.object(unified_registry, 'get_planner_tool') as mock_get_tool:
            def mock_tool_side_effect(tool_name):
                if tool_name == "create_consolidated_intent":
                    return Mock(return_value={"consolidated": "intent"})
                elif tool_name == "extract_key_insights":
                    return Mock(return_value={"insights": "key insights"})
                elif tool_name == "prepare_icp_context":
                    return Mock(return_value={"context": "prepared"})
                return Mock()
            
            mock_get_tool.side_effect = mock_tool_side_effect
            
            # 执行测试
            result = await prepare_planning_context(self.mock_state)
            
            # 验证结果
            assert "consolidated_intent" in result
            assert "key_insights" in result
            assert "icp_context" in result
            assert "current_phase" in result
            assert result["current_phase"] == "planning_ready"
            assert "analysis_completed" in result


class TestICPIterativePlanning:
    """ICP迭代规划测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_state = {
            "task_id": "test_task_123",
            "icp_context": {"available_tools": ["search_poi"]},
            "consolidated_intent": {"destinations": ["北京"]},
            "daily_plans": {},
            "daily_time_tracker": {},
            "total_budget_tracker": 0.0,
            "tool_results": {},
            "planning_log": [],
            "accommodation_planned": False,
            "notification_service": Mock()
        }
    
    @pytest.mark.asyncio
    async def test_planner_agent_node(self):
        """测试规划器Agent节点"""
        # Mock工具注册表
        with patch.object(unified_registry, 'get_planner_tool') as mock_get_tool:
            def mock_tool_side_effect(tool_name):
                if tool_name == "generate_planning_thought":
                    return Mock(return_value={"thought_content": "thinking..."})
                elif tool_name == "select_next_action":
                    return Mock(return_value={
                        "selected_action": {
                            "tool_name": "search_poi",
                            "parameters": {"keywords": "景点", "city": "北京"}
                        }
                    })
                elif tool_name == "check_planning_completion":
                    return Mock(return_value={"is_complete": False})
                return Mock()
            
            mock_get_tool.side_effect = mock_tool_side_effect
            
            # 执行测试
            result = await planner_agent_node(self.mock_state)
            
            # 验证结果
            assert "current_action" in result
            assert "thought_result" in result
            assert "action_result" in result
            assert "completion_check" in result
            assert "planning_finished" in result
    
    @pytest.mark.asyncio
    async def test_tool_executor_node(self):
        """测试工具执行器节点"""
        # 添加当前行动到状态
        self.mock_state["current_action"] = {
            "tool_name": "search_poi",
            "parameters": {"keywords": "景点", "city": "北京"}
        }
        
        # Mock工具注册表
        with patch.object(unified_registry, 'get_planner_tool') as mock_get_tool:
            def mock_tool_side_effect(tool_name):
                if tool_name == "observe_action_result":
                    return Mock(return_value={"success": True, "observation": "found POIs"})
                elif tool_name == "update_planning_state":
                    return Mock(return_value={
                        "daily_plans": {"1": [{"name": "故宫"}]},
                        "daily_time_tracker": {"1": 8},
                        "total_budget_tracker": 100.0,
                        "tool_results": {"search_poi": "results"},
                        "planning_log": ["executed search_poi"],
                        "accommodation_planned": False
                    })
                return Mock()
            
            mock_get_tool.side_effect = mock_tool_side_effect
            
            # Mock Action Tool执行
            with patch.object(unified_registry, 'execute_action_tool') as mock_execute:
                mock_execute.return_value = [{"name": "故宫", "location": "116.397,39.918"}]
                
                # 执行测试
                result = await tool_executor_node(self.mock_state)
                
                # 验证结果
                assert "daily_plans" in result
                assert "last_observation" in result
                assert "last_action_result" in result
    
    def test_route_icp_action(self):
        """测试ICP行动路由"""
        # 测试继续规划
        state_continue = {
            "current_action": {"tool_name": "search_poi"},
            "planning_finished": False
        }
        result = route_icp_action(state_continue)
        assert result == "tool_executor"
        
        # 测试完成规划
        state_finish = {
            "current_action": {"tool_name": "finish_planning"},
            "planning_finished": True
        }
        result = route_icp_action(state_finish)
        assert result == "END"


class TestV3APIIntegration:
    """V3 API集成测试"""
    
    @pytest.mark.asyncio
    async def test_plan_request_model(self):
        """测试规划请求模型"""
        from src.api.v3.travel_planner import PlanRequest
        
        # 测试有效请求
        valid_request = PlanRequest(
            user_query="我想去北京玩3天",
            user_id="test_user",
            execution_mode="automatic",
            user_profile={"preferences": {"style": "cultural"}}
        )
        
        assert valid_request.user_query == "我想去北京玩3天"
        assert valid_request.user_id == "test_user"
        assert valid_request.execution_mode == "automatic"
        assert valid_request.user_profile["preferences"]["style"] == "cultural"
    
    def test_api_router_configuration(self):
        """测试API路由配置"""
        from src.api.v3.travel_planner import router
        
        # 验证路由前缀和标签
        assert router.prefix == "/api/v3/travel-planner"
        assert "travel-planner-v3" in router.tags


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
