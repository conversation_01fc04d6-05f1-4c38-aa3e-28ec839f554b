# Travel Planner Agent 重构版产品需求文档 (PRD)

## 1. 文档概述

### 1.1. 背景与目标

当前版本的旅行规划Agent功能强大，但与用户的交互是"黑盒"的，用户无法理解AI的决策过程，也无法在规划中途进行干预，导致信任感和可控性不足。

本次重构的核心产品目标是，将旅行规划Agent从一个"一步到位"的后台工具，升级为一个**透明、可交互、高度个性化**的智能规划伙伴。它将通过**分步展示思考过程**和**流式生成行程结果**两大核心体验，显著提升用户的信任感、参与度和最终满意度。

### 1.2. 核心场景：固定汽车自驾 (Core Scenario: Fixed Vehicle Self-Driving)
本次重构所有功能设计和交互流程，均围绕一个核心场景展开：**用户将使用自己的固定车辆进行自驾游**。这使得Agent的角色从通用旅行规划师，精准定位为一名专业的**"智能自驾领航员"**。因此，所有规划都必须将**路线合理性、驾驶便捷性、续航安全性**作为核心考量因素。

### 1.3. 核心用户故事

*   **As a** 注重出行体验的电动车主,
*   **I want** AI在规划长途旅行时，能像老司机一样帮我算好续航，并提前规划好沿途的充电站,
*   **So that** 我可以彻底摆脱续航焦虑，享受旅途本身，而不是一路都在找充电桩。

*   **As a** 每次到达景点或餐厅都要为找车位而烦恼的自驾用户,
*   **I want** AI在推荐任何地点时，都能同步告诉我那里的停车是否方便，甚至推荐最佳停车场,
*   **So that** 我可以节省大量时间，避免因停车问题破坏游玩心情。

*   **As a** 计划进行多城联游的用户,
*   **I want** AI能在我给出总天数后，智能地帮我建议在每个城市待几天，并规划好城市之间的交通衔接,
*   **So that** 我不用在几个城市的攻略之间反复横跳，能得到一个连贯、完整的旅行方案。

*   **As a** 希望掌控规划过程的用户,
*   **I want** 看到AI是如何分析我的驾驶需求，并如何一步步构建出完整的驾驶路线和行程的,
*   **So that** 我能即时了解规划进展，并对路线的合理性有稳定的预期。

## 2. 核心架构理念 (Core Architectural Concepts)

为了支撑更智能、更灵活的规划能力，本次重构引入两大核心架构理念。

### 2.1. 双模运行机制：交互式 vs. 全自动 (Dual-Mode Operation: Interactive vs. Automatic)

Agent必须支持两种截然不同的运行模式，以适应不同的业务场景：

*   **交互模式 (Interactive Mode)**: 这是面向真实用户的默认模式。在此模式下，Agent的思考和规划过程将通过UI被**透明地、分步地展示**出来。例如，用户会先看到意图分析的结果，然后实时看到行程被动态构建。整个过程是**自动化流转**的，旨在建立用户的信任感，同时无需用户手动干预来推进流程。

*   **全自动模式 (Fully Automatic Mode)**: 此模式主要为内部系统调用或无前端界面的场景设计。当请求中包含`"automatic": true`之类的标志时，Agent在遇到需要决策的节点时**不会暂停**。相反，它会依赖其内部的`策略规划师LLM`做出其认为最优的判断，然后无缝地继续执行后续步骤。这确保了Agent可以作为一项可预测的、自动化的服务被集成。

### 2.2. LLM角色矩阵：专业分工的"数字大脑" (LLM Role Matrix: A "Digital Brain" with Specialized Roles)

摒弃单一LLM处理所有任务的模糊模式，我们设计一个由三位"专家"组成的"LLM角色矩阵"，每个角色使用最适合其任务的特定模型，以实现效果、成本和速度的最佳平衡。

*   **策略规划师 (Strategic Planner)**:
    *   **使用模型**: 旗舰级大模型 (如 GLM-4, GPT-4)。
    *   **核心职责**: 负责最高层、最复杂的认知与推理任务，包括：多目的地宏观策略制定、复杂约束下的最终行程与路线编排（TSP问题）、充电桩智能插入等。**它的决策质量直接决定了规划方案的上限。**

*   **数据分析师 (Data Analyst)**:
    *   **使用模型**: 均衡型模型 (如 GLM-3-Turbo)。
    *   **核心职责**: 负责所有结构化的、有明确输入输出的分析任务，包括：从用户自然语言中提取意图（目的地、天数、预算等）、分析用户偏好、为POI列表进行打分和排序等。**它的准确性是保证规划方向正确的基础。**

*   **对话交互师 (Conversationalist)**:
    *   **使用模型**: 轻量级、高响应速度模型 (如 GLM-3-Turbo)。
    *   **核心职责**: 负责生成所有面向用户的、有"人味"的对话旁白。它将分析师输出的结构化数据，实时转化为自然、流畅、符合"智能自驾领航员"人设的语音文本。**它的存在是为了实现"陪伴式"的对话体验。**

## 3. 功能特性与交互流程

Agent的交互流程将演变为一个清晰的两阶段模式，完全对应新版UI的设计。

*   **阶段一：两步意图分析 (UI左侧)**：Agent不再直接规划，而是首先通过流式输出，分两步展示它对用户需求、画像和偏好的分析结果（核心框架分析、个性化偏好分析）。
*   **阶段二：迭代式行程构建 (UI右侧)**：在分析阶段完成后，Agent**自动无缝进入**迭代式规划阶段，在UI右侧逐步、动态地构建最终的行程卡片，并同步更新一个可视化的规划日志。

---

## 4. 详细功能分解 (Functional Breakdown)

### 4.1. 数据流转核心理念: "作战室"与"档案室"

为了实现系统的实时性、鲁棒性和可追溯性，我们将采用多层数据存储策略：

*   **Redis (实时作战室)**: Redis将作为任务执行过程中的**高速缓存和状态管理器**。每个规划任务都会在Redis中有一个唯一的条目（如`task:{task_id}`），用于实时记录和更新Agent的**中间思考状态(State)**。**这种内存级的读写速度是保障前端能快速收到流式响应、避免卡顿的核心。** 这样做的好处是：1. **可观测性**：运维人员可以随时查看任何一个正在运行任务的当前状态。2. **鲁棒性**：如果Agent因故崩溃，可以从Redis中恢复任务的上下文，实现断点续传。

*   **MongoDB (完整档案室)**: MongoDB负责**永久归档完整的、未经删改的交互全过程**。一次任务结束后，后台会将本次交互的所有细节——包括用户的原始问题、每个节点的State快照、所有工具的调用与返回、LLM的输入输出原文——作为一个大的文档，完整存入MongoDB。**这一操作在后台异步完成，不会阻塞最终行程的返回。** 这为未来的模型精调、问题追溯、用户行为分析提供了最宝贵的原始数据。

*   **MySQL (结构化成果库)**: MySQL存储的是**最终的、结构化的、高价值的结果数据**。这包括用户可查看的**最终行程单**，以及通过LLM提炼出的、可用于提升未来服务质量的**用户长期记忆**和**更新后的用户画像**。**同理，所有向MySQL的写入都由后台任务异步执行，确保用户的主流程体验流畅。**

### 阶段A: 两步意图分析 (Two-Step Intent Analysis)

此阶段的核心是"将思考过程外化"，并将原有的六步分析流程高效整合为两步，为后续的迭代式规划（ICP）提供高质量的初始上下文。

| 步骤 | 分析阶段 | 整合内容 | 产出/状态字段 | 🎤 语音反馈点 (示例) |
| :--- | :--- | :--- | :--- | :--- |
| **A.1** | **核心框架分析** | 1. 核心意图 (目的地/天数等)<br>2. 多城市策略<br>3. 自驾情境 | `framework_analysis` | "收到！一个去往[目的地]的[天数]日自驾游，听起来真不错。让我先为您分析一下行程和车辆情况。" |
| **A.2** | **个性化偏好分析** | 1. 景点偏好<br>2. 美食偏好<br>3. 住宿偏好 | `preference_analysis` | "根据您的喜好，我发现您对[景点偏好1]和[景点偏好2]特别感兴趣。我会重点为您留意这类地方，并确保它们停车方便。" |
| **A.3** | **上下文整合** | 将上述两步结果，适配成下游规划节点所需的统一上下文。 | `consolidated_intent` | "好的，您的核心需求和个性化偏好我都清楚了，马上开始为您量身定制行程！" |

**流程衔接**: 所有分析步骤完成后，系统将**自动、无缝地进入下一阶段**，开始迭代式行程构建。

---

### 阶段B: 迭代式上下文规划 (Iterative Contextual Planning, ICP)

此阶段的核心是"将构建过程可视化"，并从简单的POI组合，升级为由**AI主导的、真正智能的路线优化与驾驶服务规划**。我们摒弃了僵化的线性流程，采用更灵活、更强大的**“思考 -> 行动 -> 观察”**循环。

| 步骤 | 循环环节 | 角色/组件 | 功能描述 |
| :--- | :--- | :--- | :--- |
| **B.1** | **思考 (Think)** | `Planner Agent Node` | **AI的大脑**。基于当前完整的上下文（包括已规划的行程、剩余时间、预算等），**决定下一步最合理的操作**。例如：“第一天上午，应该先找个核心景点。”或“午餐时间到了，应在当前景点附近找个餐厅。” |
| **B.2** | **行动 (Act)** | `Tool Executor Node` | **AI的手脚**。根据“思考”环节生成的指令，精确地调用一个**行动工具 (Action Tool)**，如`search_poi`、`get_driving_route`等，与外部世界交互。 |
| **B.3** | **观察 (Observe)** | `State Update` | **AI的记忆**。将“行动”的结果更新回系统状态（`AgentState`）中，形成更丰富的上下文，然后将控制权交还给“思考”环节，开始下一次循环。 |

**循环终止**: 当`Planner Agent`在“思考”环节判断整个行程已经完整且合理时，它会生成一个`finish_planning`指令，从而优雅地结束迭代循环，并进入最终的格式化与归档阶段。

---

### 阶段C: 归档与记忆沉淀 (异步解耦架构)

**设计依据**: 本阶段的设计严格遵循 **《融合系统架构.md》** 中定义的记忆体系，并采用工业级的**异步解耦**方案，以确保主流程的性能和系统的可靠性。

**核心理念**: 将"快速归档"和"耗时评估"分离。主流程（旅行规划）在完成对用户的响应后，应立即结束，其职责仅限于将原始日志存入MongoDB并触发一个后台任务。所有耗费计算资源的LLM分析和评估工作，全部由独立的后台任务异步完成。

#### 4.5.1. 异步工作流拆解

| 阶段 | 步骤 | 角色/组件 | 功能描述 | 触发方式 |
| :--- | :--- | :--- | :--- | :--- |
| **实时归档**<br/>(主流程) | **C.1: 快速归档** | `TravelPlannerAgent` | 在向用户返回最终行程后，立即从Redis中读取本次任务的完整上下文（对话、State变化、工具调用日志等），并将其作为一个**完整的JSON文档**存入**MongoDB**的`task_execution_logs`集合中。 | 用户任务成功结束时。 |
| | **C.2: 触发后台任务** | `TravelPlannerAgent` | 向一个任务队列（如Redis Pub/Sub）发布一条包含`task_id`的轻量级消息，通知后台可以开始进行记忆沉淀。 | C.1步骤成功后。 |
| **异步处理**<br/>(后台任务) | **C.3: 任务消费** | `记忆处理后台服务`<br/>(如Celery Worker) | 订阅任务队列，接收到`task_id`消息后，启动记忆沉淀流程。 | 由C.2步骤发布的消息触发。 |
| | **C.4: 记忆提取与评估** | `记忆生成/评估Agent` | 1. 根据`task_id`从**MongoDB**中读取完整的原始日志。<br/>2. 调用LLM进行分析，提取潜在的记忆点。<br/>3. 对每个记忆点进行**价值评分(1-5分)**。 | 由C.3步骤启动。 |
| | **C.5: 记忆持久化** | `记忆处理后台服务` | 将评分达到**4-5分**的高价值、结构化记忆，写入**MySQL**的`user_memories`表，并可能更新`user_profiles`表中的用户画像。 | C.4步骤成功完成后。 |

#### 4.5.2. 记忆沉淀标准与类别定义

为了确保记忆的质量和有效性，后台的`记忆生成Agent`和`记忆评估Agent`将遵循以下详细标准：

| 记忆类别 | 描述 | ✅ **应该记忆的示例 (高价值)** | ❌ **不应记忆的示例 (低价值)** | 🎯 **目标数据表/字段** |
| :--- | :--- | :--- | :--- | :--- |
| **驾驶习惯与车辆特性** | 用户的驾驶偏好或车辆在真实使用中的特性。 | "用户反馈冬季实际续航只有标称的70%。" (极高价值) <br/> "用户倾向于只使用官方品牌的超充站。" | "这次路上有点堵车。" (一次性事实) | `user_memories` (category: 'driving_habit', content: 'winter_range_factor_is_0.7') <br/> `user_profiles` (vehicle_preferences.charging_brand: 'official') |
| **出行方式偏好** | 用户对于长短途交通工具的选择习惯。 | "去稍远的地方我**不喜欢坐飞机**，觉得很麻烦。" <br/> "我周末出门**习惯自驾**。" | "这次我们是坐高铁去的。" (一次性事实) <br/> "去机场的路有点堵。" | `user_memories` (category: 'travel_style', content: 'avoids_flying_for_long_trips') <br/> `user_profiles` (travel_preferences.transport_mode: 'self_driving') |
| **住宿偏好** | 对住宿地点的类型、预算、品牌、设施的要求。 | "我出差**只住连锁品牌的经济型酒店**，比如汉庭、如家。" <br/> "我们家旅游，住宿预算可以到**1500元一晚**，要找五星级的。" | "这家酒店的早餐不错。" (具体评价) <br/> "希尔顿酒店在市中心。" (事实陈述) | `user_memories` (category: 'accommodation', content: 'prefers_chain_economy_hotels') <br/> `user_profiles` (accommodation_preferences.budget_level: 'luxury') |
| **餐饮偏好** | 对食物口味、餐厅类型、消费水平的偏好。 | "**不喜欢吃辣**，川菜湘菜都不要推荐。" <br/> "我们家聚餐**人均预算200元左右**。" | "今天中午吃的麦当劳。" (一次性事实) <br/> "这家日料店排队人好多。" | `user_memories` (category: 'food', content: 'dislikes_spicy_food') <br/> `user_profiles` (food_preferences.cuisine_exclusions: ['Sichuan', 'Hunan']) |
| **活动与兴趣** | 用户在旅行或日常中表现出的兴趣点。 | "我对**逛博物馆和科技馆**很感兴趣。" <br/> "孩子喜欢去有**大型滑梯的游乐园**。" | "今天下午去了公园。" (普通活动) <br/> "环球影城门票挺贵的。" | `user_memories` (category: 'interest', content: 'enjoys_museums') <br/> `user_profiles` (interest_tags: ['museum', 'tech', 'theme_park']) |
| **家庭与成员** | 用户的家庭构成或同行人信息。 | "**我有一个6岁的儿子**，规划时要考虑儿童活动。" <br/> "**这次是和父母一起出游**，行程不能太紧张。" | "我朋友也想去那里。" (非固定关系) <br/> "我们一家三口。" (不够具体) | `user_memories` (category: 'family', content: 'has_6_year_old_son') <br/> `user_profiles` (family_composition: {'children': [{'age': 6}]}) |
| **否定与纠正** | 用户明确表示的不满或纠正，是极高价值的负反馈。 | "**不要再给我推荐购物中心了**，我非常不喜欢逛街。" (评分5) <br/>"这个充电站是慢充，以后别推荐了。" | "这个推荐不太好。" (过于模糊) | `user_memories` (category: 'constraint', content: 'explicitly_dislikes_shopping_malls') <br/> `user_profiles` (activity_exclusions: ['shopping']) |

## 5. 非功能性需求

*   **性能要求**：所有流式事件（分析步骤、进度、行程卡片）的推送延迟必须在毫秒级，确保用户体验的流畅性。
*   **接口兼容性**：对现有前端的SSE通信协议（事件类型、核心数据结构）必须保持100%向后兼容。
*   **可靠性**：必须有明确的错误处理机制。当任意步骤失败时，应立即向前端发送错误事件，并终止流程，而不是无响应或崩溃。 
*   **对话质量要求 (新增)**: 为实现"陪伴式"体验，Agent生成的语音旁白必须由专门的LLM根据上下文动态生成，避免使用僵硬、重复的模板。文本需自然、友好，符合旅行规划师的专业人设。
*   **语音同步要求 (新增)**: 从后端生成分析结果到前端接收到对应的旁白文本，其延迟应足够低，以确保UI内容的更新与语音播报能够几乎同步发生，提供音画合一的流畅体验。 