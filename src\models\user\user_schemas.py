from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, Text, DateTime, JSON, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class UserMemory(Base):
    """User memory table ORM model."""
    __tablename__ = 'user_memories'
    __table_args__ = {'schema': 'dh_user_profile'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='记忆ID')
    user_id = Column(Integer, nullable=False, comment='用户ID')
    memory_content = Column(Text, nullable=False, comment='记忆内容')
    source_session_id = Column(String(64), comment='来源会话ID')
    confidence = Column(Float, default=1.0, comment='置信度')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    last_accessed = Column(DateTime)

class User(Base):
    """用户表 ORM 模型"""
    __tablename__ = 'users'
    __table_args__ = {'schema': 'dh_user_profile'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='用户ID')
    nickname = Column(String(100), nullable=False, comment='昵称')
    avatar_url = Column(String(255), comment='头像URL')
    status = Column(String(32), nullable=False, default='ACTIVE', comment='状态')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

class UserSummary(Base):
    """用户画像摘要表 ORM 模型"""
    __tablename__ = 'user_summaries'
    __table_args__ = {'schema': 'dh_user_profile'}

    user_id = Column(Integer, primary_key=True, nullable=False, comment='主键, 逻辑外键关联到 users.id，一个用户只对应一条摘要记录')
    summary = Column(Text, nullable=False, comment='用户画像摘要 (由AI生成的自然语言描述)')
    keywords = Column(JSON, nullable=False, comment='从摘要中提取的关键词数组 (JSON数组)')
    model_version = Column(String(50), comment='生成此摘要的AI模型版本号')
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment='摘要的最后更新时间')
    # 索引在ORM中通常通过Index对象定义，但复杂表达式如cast(json as char array)不直接支持，需在数据库层手动建。 