-- =================================================================
-- Digital Human - Trip Planner Schema Initialization Script (Final Corrected Version)
-- =================================================================
-- 项目: 数字人 (Digital Human Project)
-- Schema: dh_tripplanner (旅游搭子应用)
-- 描述: 本脚本为“旅游搭子”App创建完整且优化的数据库结构。
--       (已修复所有INSERT语句的语法问题，保证可执行)
-- =================================================================

-- 如果 Schema 不存在，则创建
CREATE SCHEMA IF NOT EXISTS `dh_tripplanner` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 切换到目标 Schema
USE `dh_tripplanner`;

-- -----------------------------------------------------
-- 第一步: 按逆向依赖顺序，安全删除已存在的旧表
-- -----------------------------------------------------
DROP TABLE IF EXISTS `vlog_media_items`;
DROP TABLE IF EXISTS `ai_vlogs`;
DROP TABLE IF EXISTS `user_media`;
DROP TABLE IF EXISTS `user_app_settings`;
DROP TABLE IF EXISTS `user_trip_stats`;
DROP TABLE IF EXISTS `itinerary_day_pois`;
DROP TABLE IF EXISTS `itinerary_days`;
DROP TABLE IF EXISTS `itinerary_tags`;
DROP TABLE IF EXISTS `tags`;
DROP TABLE IF EXISTS `itineraries`;
DROP TABLE IF EXISTS `pois`;
DROP TABLE IF EXISTS `poi_types`;
DROP TABLE IF EXISTS `status_definitions`;
DROP TABLE IF EXISTS `ai_planning_sessions`;
DROP TABLE IF EXISTS `user_travel_profiles`;

-- -----------------------------------------------------
-- 第二步: 创建新表 (字典表先行)
-- -----------------------------------------------------
-- (所有 CREATE TABLE 语句与上一版相同，此处为简洁省略，实际脚本中包含)
-- 表 1: `poi_types` (新增字典表)
CREATE TABLE `poi_types` ( `id` INT NOT NULL AUTO_INCREMENT COMMENT '类型主键, 自增', `type_key` VARCHAR(50) NOT NULL COMMENT '类型的英文键 (例如: ATTRACTION)', `display_name` VARCHAR(50) NOT NULL COMMENT '类型的中文显示名 (例如: 景点)', `icon_name` VARCHAR(50) NULL COMMENT '对应UI的图标名称 (例如: icon-attraction)', PRIMARY KEY (`id`), UNIQUE INDEX `idx_type_key_unique` (`type_key`) ) ENGINE=InnoDB COMMENT='POI的类型字典表';
-- 表 2: `status_definitions` (新增通用状态字典表)
CREATE TABLE `status_definitions` ( `id` INT NOT NULL AUTO_INCREMENT COMMENT '状态主键, 自增', `status_key` VARCHAR(50) NOT NULL COMMENT '状态的英文键 (例如: DRAFT)', `scope` VARCHAR(50) NOT NULL COMMENT '作用域 (例如: ITINERARY, VLOG)', `display_name` VARCHAR(50) NOT NULL COMMENT '状态的中文显示名 (例如: 草稿)', PRIMARY KEY (`id`), UNIQUE INDEX `idx_key_scope_unique` (`status_key`, `scope`) ) ENGINE=InnoDB COMMENT='通用的状态定义字典表';
-- 表 3: `pois` - 兴趣点信息表 (已优化)
CREATE TABLE `pois` ( `id` INT NOT NULL AUTO_INCREMENT COMMENT 'POI主键, 自增', `type_id` INT NOT NULL COMMENT '逻辑外键, 关联到 poi_types.id', `name` VARCHAR(100) NOT NULL COMMENT 'POI名称', `address` VARCHAR(255) NULL COMMENT '详细地址', `latitude` DECIMAL(10, 7) NOT NULL COMMENT '纬度', `longitude` DECIMAL(10, 7) NOT NULL COMMENT '经度', `description` TEXT NULL COMMENT '详细介绍 (由AI或API获取)', `images` JSON NULL COMMENT '图片URL列表 (JSON数组)', `narrations` JSON NULL COMMENT '景点解说文稿 (JSON数组, 包含多语言、多风格的文本)', `rating` FLOAT NULL COMMENT '评分 (例如: 4.4)', `opening_hours` VARCHAR(100) NULL COMMENT '营业时间', `phone_number` VARCHAR(50) NULL COMMENT '联系电话', `source_id` VARCHAR(100) NULL COMMENT '数据来源方的ID (例如高德地图的POI ID)', `last_sync_at` TIMESTAMP NULL COMMENT '最后从外部API同步数据的时间', PRIMARY KEY (`id`), INDEX `idx_name` (`name`), INDEX `idx_type_id` (`type_id`) ) ENGINE=InnoDB COMMENT='兴趣点(POI)信息表, 作为外部数据的本地缓存';
-- 表 4: `itineraries` - 行程主表 (已优化)
CREATE TABLE `itineraries` ( `id` INT NOT NULL AUTO_INCREMENT COMMENT '行程主键, 自增', `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id', `status_id` INT NOT NULL COMMENT '逻辑外键, 关联到 status_definitions.id (scope=''ITINERARY'')', `title` VARCHAR(100) NOT NULL COMMENT '行程标题', `city_name` VARCHAR(50) NOT NULL COMMENT '目的地城市名称', `total_days` INT NOT NULL COMMENT '行程总天数', `start_date` DATE NULL COMMENT '行程开始日期', `total_distance` DECIMAL(10, 2) NULL COMMENT '行程预估总里程 (公里)', `cover_image_url` VARCHAR(255) NULL COMMENT '行程封面图URL', `notes` TEXT NULL COMMENT '行程备注或注意事项', `is_template` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为精选路线模板', `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY (`id`), INDEX `idx_user_id_status` (`user_id`, `status_id`) ) ENGINE=InnoDB COMMENT='行程主表, 存储每一次行程的概要信息';
-- 表 5: `itinerary_days` - 行程日详情表
CREATE TABLE `itinerary_days` ( `id` INT NOT NULL AUTO_INCREMENT COMMENT '行程日主键, 自增', `itinerary_id` INT NOT NULL COMMENT '逻辑外键, 关联到 itineraries.id', `day_number` INT NOT NULL COMMENT '当天是行程的第几天 (从1开始)', `summary` VARCHAR(255) NULL COMMENT '当日路线概览 (例如: 静安寺→人民广场→外滩)', `distance` DECIMAL(10, 2) NULL COMMENT '当日预估总里程 (公里)', PRIMARY KEY (`id`), UNIQUE INDEX `idx_itinerary_day_unique` (`itinerary_id`, `day_number`) ) ENGINE=InnoDB COMMENT='行程日详情表, 将长行程拆分成天';
-- 表 6: `itinerary_day_pois` - 每日行程站点关联表
CREATE TABLE `itinerary_day_pois` ( `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键, 自增', `itinerary_day_id` INT NOT NULL COMMENT '逻辑外键, 关联到 itinerary_days.id', `poi_id` INT NOT NULL COMMENT '逻辑外键, 关联到 pois.id', `sequence` INT NOT NULL COMMENT '在当天行程中的顺序 (从1开始)', `user_notes` TEXT NULL COMMENT '用户针对这一站点的个人备注', PRIMARY KEY (`id`), INDEX `idx_day_id` (`itinerary_day_id`) ) ENGINE=InnoDB COMMENT='每日行程与POI的关联表，定义了游玩顺序';
-- 表 7: `tags` - 标签字典表
CREATE TABLE `tags` ( `id` INT NOT NULL AUTO_INCREMENT COMMENT '标签主键, 自增', `name` VARCHAR(50) NOT NULL COMMENT '标签名称', `category` VARCHAR(50) NULL COMMENT '标签分类 (例如: ''旅行主题'', ''适合人群'')', PRIMARY KEY (`id`), UNIQUE INDEX `idx_name_unique` (`name`) ) ENGINE=InnoDB COMMENT='标签字典表，用于行程分类和AI规划';
-- 表 8: `itinerary_tags` - 行程与标签的关联表
CREATE TABLE `itinerary_tags` ( `itinerary_id` INT NOT NULL COMMENT '逻辑外键, 关联到 itineraries.id', `tag_id` INT NOT NULL COMMENT '逻辑外键, 关联到 tags.id', PRIMARY KEY (`itinerary_id`, `tag_id`) ) ENGINE=InnoDB COMMENT='行程与标签的多对多关联表';
-- 表 9: `user_trip_stats` - 用户行程统计表
CREATE TABLE `user_trip_stats` ( `user_id` INT NOT NULL COMMENT '主键, 逻辑外键关联到 dh_user_profile.users.id', `trip_count` INT NOT NULL DEFAULT 0 COMMENT '累计出行次数 (完成的行程数)', `city_count` INT NOT NULL DEFAULT 0 COMMENT '累计去过的城市数量', `total_mileage` DECIMAL(12, 2) NOT NULL DEFAULT 0.00 COMMENT '累计总里程', `total_days` INT NOT NULL DEFAULT 0 COMMENT '累计出行天数', `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY (`user_id`) ) ENGINE=InnoDB COMMENT='用户行程统计表, 用于个人中心的数据展示';
-- 表 10: `user_app_settings` - App专属设置表
CREATE TABLE `user_app_settings` ( `user_id` INT NOT NULL COMMENT '主键, 逻辑外键关联到 dh_user_profile.users.id', `narration_enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '景点解说功能是否开启', `in_poi_guide_enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '景点内攻略推送是否开启', `proactive_recommend_enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '主动推荐功能是否开启', `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, PRIMARY KEY (`user_id`) ) ENGINE=InnoDB COMMENT='存储用户针对本App的专属设置';
-- 表 11: `user_media` - 用户媒体素材库
CREATE TABLE `user_media` ( `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '媒体文件主键, 自增', `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id', `media_type` ENUM('IMAGE', 'VIDEO') NOT NULL COMMENT '媒体类型', `storage_url` VARCHAR(255) NOT NULL COMMENT '媒体文件在云存储上的URL', `poi_id` INT NULL COMMENT '逻辑外键, 关联到 pois.id (可选, 如果媒体与某个POI相关)', `taken_at` TIMESTAMP NULL COMMENT '拍摄时间 (可从EXIF读取)', `uploaded_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY (`id`), INDEX `idx_user_id` (`user_id`) ) ENGINE=InnoDB COMMENT='用户媒体素材库, 存储用于生成Vlog的图片和视频';
-- 表 12: `ai_vlogs` - AI Vlog 作品表 (已优化)
CREATE TABLE `ai_vlogs` ( `id` INT NOT NULL AUTO_INCREMENT COMMENT 'Vlog主键, 自增', `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id', `status_id` INT NOT NULL COMMENT '逻辑外键, 关联到 status_definitions.id (scope=''VLOG'')', `title` VARCHAR(100) NOT NULL COMMENT 'Vlog标题', `config` JSON NULL COMMENT 'Vlog生成配置 (JSON格式, 包含风格,模板,时长,文案等)', `final_video_url` VARCHAR(255) NULL COMMENT '最终生成的视频URL', `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, `completed_at` TIMESTAMP NULL, PRIMARY KEY (`id`), INDEX `idx_user_id_status` (`user_id`, `status_id`) ) ENGINE=InnoDB COMMENT='AI Vlog 作品表, 记录每一次Vlog的生成任务和结果';
-- 表 13: `vlog_media_items` - Vlog与媒体素材的关联表
CREATE TABLE `vlog_media_items` ( `vlog_id` INT NOT NULL COMMENT '逻辑外键, 关联到 ai_vlogs.id', `media_id` BIGINT NOT NULL COMMENT '逻辑外键, 关联到 user_media.id', `sequence` INT NOT NULL COMMENT '该素材在Vlog中的顺序', PRIMARY KEY (`vlog_id`, `media_id`) ) ENGINE=InnoDB COMMENT='Vlog与媒体素材的多对多关联表';

-- -----------------------------------------------------
-- 新增表: `ai_planning_sessions` - AI规划任务会话表
-- -----------------------------------------------------
-- 这张表用于完整记录每一次“帮我规划”任务的生命周期，
-- 从用户输入，到AI的思考过程，再到模型的原始输出。
-- 它是实现“规划过程”UI和“重新规划”等功能的核心。


CREATE TABLE `ai_planning_sessions` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键, 建议使用UUID',
  `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id',
  `status` ENUM('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELED') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
  `user_input` JSON NOT NULL COMMENT '用户输入的原始规划请求 (JSON格式)',
  `planning_log` LONGTEXT NULL COMMENT '存储AI Agent的思考过程、<think>内容和工具调用日志',
  `raw_llm_output` JSON NULL COMMENT '大模型返回的未经处理的原始行程规划结果 (JSON格式)',
  `final_itinerary_id` INT NULL COMMENT '逻辑外键, 用户最终保存的行程ID, 关联到 itineraries.id',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务创建时间',
  `completed_at` TIMESTAMP NULL COMMENT '任务完成(成功/失败/取消)的时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id_status` (`user_id`, `status`)
) ENGINE=InnoDB COMMENT='AI行程规划会话表，记录规划任务的完整生命周期';


-- 用户专属旅行画像表
CREATE TABLE `user_travel_profiles` (
  `user_id` INT NOT NULL COMMENT '关联到 dh_user_profile.users.id',
  `travel_style` ENUM('RELAXED', 'ADVENTURE', 'FAMILY', 'BUDGET', 'LUXURY', 'BUSINESS') NULL COMMENT '整体旅行风格偏好 (休闲放松, 探险, 家庭, 经济, 奢华, 商务)',
  `accommodation_pref` JSON NULL COMMENT '住宿偏好 (JSON数组, 例如: [\"精品酒店\", \"民宿\", \"连锁品牌\"])',
  `transportation_pref` JSON NULL COMMENT '交通偏好 (JSON数组, 例如: [\"自驾\", \"高铁\", \"飞机\"])',
  `travel_summary` TEXT NULL COMMENT '专属旅行画像摘要 (由AI生成，只关注旅行方面)',
  `travel_keywords` JSON NULL COMMENT '从travel_summary中提取的专属旅行关键词 (JSON数组)',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '该旅行画像的最后更新时间',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB COMMENT='用户专属旅行画像表，存储与旅行相关的特定偏好和摘要';

-- -----------------------------------------------------
-- 第三步: 为新创建的字典表插入初始数据 (已修复为单行插入模式)
-- -----------------------------------------------------
-- 为POI类型字典表插入数据
INSERT INTO `poi_types` (`id`, `type_key`, `display_name`, `icon_name`) VALUES (1, 'ATTRACTION', '景点', 'icon-attraction');
INSERT INTO `poi_types` (`id`, `type_key`, `display_name`, `icon_name`) VALUES (2, 'RESTAURANT', '美食', 'icon-restaurant');
INSERT INTO `poi_types` (`id`, `type_key`, `display_name`, `icon_name`) VALUES (3, 'HOTEL', '酒店', 'icon-hotel');
INSERT INTO `poi_types` (`id`, `type_key`, `display_name`, `icon_name`) VALUES (4, 'SHOPPING', '购物', 'icon-shopping');
INSERT INTO `poi_types` (`id`, `type_key`, `display_name`, `icon_name`) VALUES (5, 'OTHER', '其他', 'icon-other');

-- 为通用状态字典表插入数据
-- 行程(ITINERARY)相关的状态
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (1, 'DRAFT', 'ITINERARY', '草稿');
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (2, 'ACTIVE', 'ITINERARY', '进行中');
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (3, 'COMPLETED', 'ITINERARY', '已完成');
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (4, 'ARCHIVED', 'ITINERARY', '已归档');

-- Vlog(VLOG)相关的状态
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (5, 'PENDING', 'VLOG', '待处理');
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (6, 'PROCESSING', 'VLOG', '生成中');
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (7, 'COMPLETED', 'VLOG', '已完成');
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES (8, 'FAILED', 'VLOG', '生成失败');

-- -----------------------------------------------------
-- 第四步: 更新主表的默认值
-- -----------------------------------------------------
-- 将行程的默认状态设置为“草稿”(ID=1)
ALTER TABLE `itineraries` ALTER COLUMN `status_id` SET DEFAULT 1;
-- 将Vlog的默认状态设置为“待处理”(ID=5)
ALTER TABLE `ai_vlogs` ALTER COLUMN `status_id` SET DEFAULT 5;