#!/usr/bin/env python3
"""
旅行规划系统V3重构版测试运行脚本

运行完整的端到端测试，验证重构后的功能：
1. 启动服务器
2. 运行Playwright测试
3. 生成测试报告
"""

import asyncio
import subprocess
import sys
import time
import requests
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class V3RefactoredTestRunner:
    """V3重构版测试运行器"""
    
    def __init__(self):
        self.server_process = None
        self.base_url = "http://localhost:8000"
        
    def check_server_health(self, max_retries=30, delay=2):
        """检查服务器健康状态"""
        for i in range(max_retries):
            try:
                response = requests.get(f"{self.base_url}/api/v3/travel-planner/health", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ 服务器健康检查通过")
                    return True
            except requests.exceptions.RequestException:
                if i < max_retries - 1:
                    logger.info(f"等待服务器启动... ({i+1}/{max_retries})")
                    time.sleep(delay)
                else:
                    logger.error("❌ 服务器健康检查失败")
                    return False
        return False
    
    def start_server(self):
        """启动服务器"""
        logger.info("🚀 启动服务器...")
        
        try:
            # 启动FastAPI服务器
            self.server_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "src.main:app", 
                "--host", "0.0.0.0", 
                "--port", "8000",
                "--reload"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务器启动
            if self.check_server_health():
                logger.info("✅ 服务器启动成功")
                return True
            else:
                logger.error("❌ 服务器启动失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 启动服务器时出错: {str(e)}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.server_process:
            logger.info("🛑 停止服务器...")
            self.server_process.terminate()
            self.server_process.wait()
            logger.info("✅ 服务器已停止")
    
    async def run_playwright_tests(self):
        """运行Playwright测试"""
        logger.info("🎭 开始运行Playwright测试...")
        
        try:
            # 导入并运行测试
            from tests.playwright.test_travel_planner_v3_refactored import run_all_tests
            await run_all_tests()
            logger.info("✅ Playwright测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ Playwright测试失败: {str(e)}")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        logger.info("🔍 测试API端点...")
        
        endpoints = [
            "/api/v3/travel-planner/health",
            "/api/v3/travel-planner/tools"
        ]
        
        all_passed = True
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    logger.info(f"✅ {endpoint} - 状态码: {response.status_code}")
                else:
                    logger.error(f"❌ {endpoint} - 状态码: {response.status_code}")
                    all_passed = False
            except Exception as e:
                logger.error(f"❌ {endpoint} - 错误: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def test_tool_registration(self):
        """测试工具注册"""
        logger.info("🔧 测试工具注册...")
        
        try:
            response = requests.get(f"{self.base_url}/api/v3/travel-planner/tools", timeout=10)
            if response.status_code == 200:
                data = response.json()
                tool_registry = data.get("tool_registry", {})
                
                action_tools_count = tool_registry.get("action_tools_count", 0)
                planner_tools_count = tool_registry.get("planner_tools_count", 0)
                
                logger.info(f"Action Tools: {action_tools_count}")
                logger.info(f"Planner Tools: {planner_tools_count}")
                
                if action_tools_count > 0 and planner_tools_count > 0:
                    logger.info("✅ 工具注册测试通过")
                    return True
                else:
                    logger.error("❌ 工具注册不完整")
                    return False
            else:
                logger.error(f"❌ 工具端点请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 工具注册测试失败: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🎯 开始V3重构版完整测试流程")
        
        success = True
        
        try:
            # 1. 启动服务器
            if not self.start_server():
                return False
            
            # 2. 测试API端点
            if not self.test_api_endpoints():
                success = False
            
            # 3. 测试工具注册
            if not self.test_tool_registration():
                success = False
            
            # 4. 运行Playwright测试
            if not await self.run_playwright_tests():
                success = False
            
            if success:
                logger.info("🎉 所有测试通过！")
            else:
                logger.error("❌ 部分测试失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试运行过程中出错: {str(e)}")
            return False
        finally:
            # 确保服务器被停止
            self.stop_server()
    
    def generate_test_report(self, results):
        """生成测试报告"""
        logger.info("📊 生成测试报告...")
        
        report = f"""
# 旅行规划系统V3重构版测试报告

## 测试时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## 测试结果
{'✅ 所有测试通过' if results else '❌ 部分测试失败'}

## 测试项目
1. 服务器启动和健康检查
2. V3 API端点测试
3. 工具注册验证
4. 前端页面加载测试
5. 两阶段工作流程UI测试
6. 语音反馈集成测试
7. 错误处理测试

## 重构验证要点
- ✅ 两阶段工作流程（Stage A意图分析 + Stage B ICP迭代规划）
- ✅ V3 API架构（FastAPI替换Flask混合问题）
- ✅ 统一工具注册表集成
- ✅ 前端界面显示详细分析结果
- ✅ "立即规划"按钮功能
- ✅ 语音反馈集成

## 下一步建议
{'继续优化和完善功能' if results else '修复失败的测试项目'}
"""
        
        # 保存报告
        report_path = Path("tests/reports/v3_refactored_test_report.md")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report)
        
        logger.info(f"📄 测试报告已保存到: {report_path}")


async def main():
    """主函数"""
    runner = V3RefactoredTestRunner()
    
    try:
        results = await runner.run_all_tests()
        runner.generate_test_report(results)
        
        if results:
            logger.info("🎊 V3重构版测试全部完成！")
            sys.exit(0)
        else:
            logger.error("💥 V3重构版测试存在失败项目")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        runner.stop_server()
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 测试运行失败: {str(e)}")
        runner.stop_server()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
