{"1": {"inputs": {"ckpt_name": "AWPainting XL_1.0.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "2": {"inputs": {"text": ["47", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["50", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "3": {"inputs": {"text": "(worst quality, low quality:1.4), (zombie, sketch, interlocked fingers, comic)", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["50", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "4": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["2", 0], "negative": ["3", 0], "control_net": ["6", 0], "image": ["20", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "应用ControlNet（旧版高级）"}}, "6": {"inputs": {"control_net_name": "MistoLine_MistoLine_V1.0.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载ControlNet模型"}}, "9": {"inputs": {"seed": 110040976805918, "steps": 6, "cfg": 1, "sampler_name": "euler_cfg_pp", "scheduler": "normal", "denoise": 1, "model": ["50", 0], "positive": ["4", 0], "negative": ["4", 1], "latent_image": ["12", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "12": {"inputs": {"width": ["19", 0], "height": ["19", 1], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "17": {"inputs": {"image": "微信图片_20250303144457.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "18": {"inputs": {"model": "wd-v1-4-moat-tagger-v2", "threshold": 0.35, "character_threshold": 0.4, "replace_underscore": false, "trailing_comma": false, "exclude_tags": "realistic,facial_hair,stubble", "image": ["17", 0]}, "class_type": "WD14Tagger|pysssss", "_meta": {"title": "WD14 Tagger 🐍"}}, "19": {"inputs": {"image": ["20", 0]}, "class_type": "easy imageSize", "_meta": {"title": "图像尺寸"}}, "20": {"inputs": {"max_width": 1024, "max_height": 1024, "min_width": 0, "min_height": 0, "crop_if_required": "no", "images": ["17", 0]}, "class_type": "ConstrainImage|pysssss", "_meta": {"title": "Constrain Image 🐍"}}, "47": {"inputs": {"separator": ",", "prompt1": ["49", 0], "prompt2": ["18", 0]}, "class_type": "easy promptConcat", "_meta": {"title": "提示词联结"}}, "49": {"inputs": {"String": "anime_style, ", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "50": {"inputs": {"lora_name": "功能/Hyper-SDXL-8steps-lora.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["1", 0], "clip": ["1", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "加载LoRA"}}, "59": {"inputs": {"samples": ["9", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "63": {"inputs": {"filename_prefix": "magicCamera", "images": ["59", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}