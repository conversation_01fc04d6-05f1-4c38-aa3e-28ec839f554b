"""
MySQL数据库所有表的Pydantic模型定义
使用内嵌类组织，清晰区分不同数据库的表模型
"""
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field
import json

# --- 数据库 dh_tripplanner ---
class DhTripPlanner:
    """dh_tripplanner 数据库的所有表模型"""
    
    class PlanningSessionStatus(str, Enum):
        PENDING = "PENDING"
        PROCESSING = "PROCESSING"
        SUCCESS = "SUCCESS"
        FAILED = "FAILED"
        CANCELED = "CANCELED"
    
    class TravelStyle(str, Enum):
        RELAXED = "RELAXED"
        ADVENTURE = "ADVENTURE"
        FAMILY = "FAMILY"
        BUDGET = "BUDGET"
        LUXURY = "LUXURY"
        BUSINESS = "BUSINESS"
    
    class AIPlanningSession(BaseModel):
        """AI规划会话表"""
        id: str = Field(..., description="会话ID (UUID)")
        user_id: int = Field(..., description="用户ID")
        status: str = Field(default="PENDING", description="状态")
        user_input: Dict[str, Any] = Field(..., description="用户输入JSON")
        planning_log: Optional[str] = Field(None, description="规划日志")
        raw_llm_output: Optional[Dict[str, Any]] = Field(None, description="原始LLM输出")
        final_itinerary_id: Optional[int] = Field(None, description="最终行程ID")
        created_at: datetime = Field(..., description="创建时间")
        completed_at: Optional[datetime] = Field(None, description="完成时间")
        
        class Config:
            from_attributes = True
    
    class StatusDefinition(BaseModel):
        """状态定义表"""
        id: int = Field(..., description="状态ID")
        status_key: str = Field(..., description="状态键")
        display_name: str = Field(..., description="显示名称")
        
        class Config:
            from_attributes = True
    
    class Tag(BaseModel):
        """标签表"""
        id: int = Field(..., description="标签ID")
        name: str = Field(..., description="标签名称")
        
        class Config:
            from_attributes = True
    
    class POIType(BaseModel):
        """POI类型表"""
        id: int = Field(..., description="类型ID")
        type_key: str = Field(..., description="类型键名")
        display_name: str = Field(..., description="显示名称")
        icon_name: Optional[str] = Field(None, description="图标名称")
        
        class Config:
            from_attributes = True
    
    class POI(BaseModel):
        """POI表"""
        id: int = Field(..., description="POI ID")
        name: str = Field(..., description="名称")
        type_id: int = Field(..., description="类型ID")
        address: Optional[str] = Field(None, description="地址")
        latitude: float = Field(..., description="纬度")
        longitude: float = Field(..., description="经度")
        description: Optional[str] = Field(None, description="描述")
        images: Optional[str] = Field(None, description="图片JSON")
        rating: Optional[float] = Field(None, description="评分")
        opening_hours: Optional[str] = Field(None, description="营业时间")
        phone_number: Optional[str] = Field(None, description="电话")
        
        class Config:
            from_attributes = True
    
    class Itinerary(BaseModel):
        """行程表"""
        id: int = Field(..., description="行程ID")
        user_id: int = Field(..., description="用户ID")
        status_id: int = Field(default=1, description="状态ID")
        title: str = Field(..., description="标题")
        city_name: str = Field(..., description="城市名称")
        total_days: int = Field(..., description="总天数")
        start_date: Optional[date] = Field(None, description="开始日期")
        total_distance: Optional[float] = Field(None, description="总距离")
        cover_image_url: Optional[str] = Field(None, description="封面图片")
        notes: Optional[str] = Field(None, description="备注")
        is_template: bool = Field(default=False, description="是否为模板")
        created_at: datetime = Field(..., description="创建时间")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class ItineraryDay(BaseModel):
        """行程天数表"""
        id: int = Field(..., description="ID")
        itinerary_id: int = Field(..., description="行程ID")
        day_number: int = Field(..., description="第几天")
        summary: Optional[str] = Field(None, description="当日摘要")
        weather_condition: Optional[str] = Field(None, description="天气状况")
        high_temp: Optional[float] = Field(None, description="最高温度")
        low_temp: Optional[float] = Field(None, description="最低温度")
        
        class Config:
            from_attributes = True
    
    class ItineraryDayPOI(BaseModel):
        """行程天数POI表"""
        id: int = Field(..., description="ID")
        itinerary_day_id: int = Field(..., description="行程天数ID")
        poi_id: int = Field(..., description="POI ID")
        sequence: int = Field(..., description="顺序")
        user_notes: Optional[str] = Field(None, description="用户备注")
        
        class Config:
            from_attributes = True
    
    class UserTripStats(BaseModel):
        """用户行程统计表"""
        user_id: int = Field(..., description="用户ID")
        trip_count: int = Field(default=0, description="行程数量")
        city_count: int = Field(default=0, description="城市数量")
        total_mileage: float = Field(default=0.0, description="总里程")
        total_days: int = Field(default=0, description="总天数")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class UserTravelProfile(BaseModel):
        """用户旅行偏好表"""
        user_id: int = Field(..., description="用户ID")
        travel_style: Optional[str] = Field(None, description="旅行风格")
        accommodation_pref: Optional[Dict[str, Any]] = Field(None, description="住宿偏好JSON")
        transportation_pref: Optional[Dict[str, Any]] = Field(None, description="交通偏好JSON")
        travel_summary: Optional[str] = Field(None, description="旅行总结")
        travel_keywords: Optional[List[str]] = Field(None, description="旅行关键词")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class UserAppSettings(BaseModel):
        """用户应用设置表"""
        user_id: int = Field(..., description="用户ID")
        narration_enabled: bool = Field(default=False, description="是否开启解说")
        in_poi_guide_enabled: bool = Field(default=False, description="是否推送景点内攻略")
        proactive_recommend_enabled: bool = Field(default=False, description="是否开启主动推荐")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class UserMedia(BaseModel):
        """用户媒体表"""
        id: int = Field(..., description="媒体ID")
        user_id: int = Field(..., description="用户ID")
        media_type: str = Field(..., description="媒体类型")
        file_path: str = Field(..., description="文件路径")
        file_size: Optional[int] = Field(None, description="文件大小")
        created_at: datetime = Field(..., description="创建时间")
        
        class Config:
            from_attributes = True
    
    class AIVlog(BaseModel):
        """AI视频博客表"""
        id: int = Field(..., description="Vlog ID")
        user_id: int = Field(..., description="用户ID")
        itinerary_id: int = Field(..., description="行程ID")
        status_id: int = Field(..., description="状态ID")
        style_name: Optional[str] = Field(None, description="风格名称")
        aspect_ratio: str = Field(default="16:9", description="视频比例")
        duration_seconds: int = Field(default=180, description="时长(秒)")
        user_text: Optional[str] = Field(None, description="用户文字")
        final_video_url: Optional[str] = Field(None, description="最终视频URL")
        progress: int = Field(default=0, description="进度百分比")
        created_at: datetime = Field(..., description="创建时间")
        completed_at: Optional[datetime] = Field(None, description="完成时间")
        
        class Config:
            from_attributes = True
    
    class VlogMediaItem(BaseModel):
        """Vlog媒体项表"""
        id: int = Field(..., description="ID")
        vlog_id: int = Field(..., description="Vlog ID")
        media_id: int = Field(..., description="媒体ID")
        sequence: int = Field(..., description="顺序")
        
        class Config:
            from_attributes = True


# --- 数据库 dh_user_profile ---
class DhUserProfile:
    """dh_user_profile 数据库的所有表模型"""
    
    class UserStatus(str, Enum):
        ACTIVE = "ACTIVE"
        SUSPENDED = "SUSPENDED"
        DELETED = "DELETED"
    
    class BindingRole(str, Enum):
        OWNER = "OWNER"
        FAMILY = "FAMILY" 
        GUEST = "GUEST"
    
    class BindingStatus(str, Enum):
        ACTIVE = "ACTIVE"
        PENDING = "PENDING"
        INACTIVE = "INACTIVE"
    
    class User(BaseModel):
        """用户表"""
        id: int = Field(..., description="用户ID")
        nickname: str = Field(..., description="昵称")
        avatar_url: Optional[str] = Field(None, description="头像URL")
        status: str = Field(default="ACTIVE", description="状态")
        created_at: datetime = Field(..., description="创建时间")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class Vehicle(BaseModel):
        """车辆表"""
        vin: str = Field(..., description="车辆识别号")
        license_plate: Optional[str] = Field(None, description="车牌号")
        brand: Optional[str] = Field(None, description="品牌")
        model: Optional[str] = Field(None, description="型号")
        activated_at: Optional[datetime] = Field(None, description="激活时间")
        created_at: datetime = Field(..., description="创建时间")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class Credential(BaseModel):
        """用户凭证表"""
        user_id: int = Field(..., description="用户ID")
        provider: str = Field(..., description="认证提供方")
        identifier: str = Field(..., description="认证标识")
        secret: Optional[str] = Field(None, description="认证密钥")
        created_at: datetime = Field(..., description="创建时间")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class UserMemory(BaseModel):
        """用户记忆表"""
        id: int = Field(..., description="记忆ID")
        user_id: int = Field(..., description="用户ID")
        memory_content: str = Field(..., description="记忆内容")
        source_session_id: Optional[str] = Field(None, description="来源会话ID")
        confidence: float = Field(default=1.0, description="置信度")
        created_at: datetime = Field(..., description="创建时间")
        last_accessed: Optional[datetime] = Field(None, description="最后访问时间")
        
        class Config:
            from_attributes = True
    
    class UserSummary(BaseModel):
        """用户画像摘要表"""
        user_id: int = Field(..., description="用户ID")
        summary: str = Field(..., description="用户画像摘要")
        keywords: List[str] = Field(..., description="关键词列表")
        model_version: Optional[str] = Field(None, description="模型版本")
        updated_at: datetime = Field(..., description="更新时间")
        
        class Config:
            from_attributes = True
    
    class UserVehicleBinding(BaseModel):
        """用户车辆绑定表"""
        id: int = Field(..., description="绑定ID")
        user_id: int = Field(..., description="用户ID")
        vehicle_vin: str = Field(..., description="车辆VIN")
        role: str = Field(default="GUEST", description="角色")
        status: str = Field(default="PENDING", description="绑定状态")
        bind_at: Optional[datetime] = Field(None, description="绑定时间")
        unbind_at: Optional[datetime] = Field(None, description="解绑时间")
        
        class Config:
            from_attributes = True
    
    class FavoriteTag(BaseModel):
        """收藏标签表"""
        id: int = Field(..., description="标签ID")
        application_source: str = Field(..., description="应用来源")
        tag_name: str = Field(..., description="标签名称")
        weight_multiplier: float = Field(default=1.0, description="权重乘数")
        description: Optional[str] = Field(None, description="描述")
        
        class Config:
            from_attributes = True
    
    class UserFavorite(BaseModel):
        """用户收藏表"""
        id: int = Field(..., description="收藏ID")
        user_id: int = Field(..., description="用户ID")
        interaction_id: str = Field(..., description="交互ID")
        tag_id: int = Field(..., description="标签ID")
        selected_content: Optional[str] = Field(None, description="选中内容")
        user_notes: Optional[str] = Field(None, description="用户备注")
        created_at: datetime = Field(..., description="创建时间")
        
        class Config:
            from_attributes = True


# --- 便捷的导入别名 ---
# 为了方便使用，提供一些常用的别名
TripPlannerModels = DhTripPlanner
UserProfileModels = DhUserProfile

# --- 使用示例 ---
# from src.models.mysql_models import DhTripPlanner, DhUserProfile
# from src.models.mysql_models import TripPlannerModels, UserProfileModels
#
# # 创建用户模型实例
# user = DhUserProfile.User(
#     id=1,
#     nickname="张三",
#     status=DhUserProfile.UserStatus.ACTIVE,
#     created_at=datetime.now(),
#     updated_at=datetime.now()
# )
#
# # 创建行程模型实例
# itinerary = DhTripPlanner.Itinerary(
#     id=1,
#     user_id=1,
#     title="北京三日游",
#     city_name="北京",
#     total_days=3,
#     created_at=datetime.now(),
#     updated_at=datetime.now()
# ) 