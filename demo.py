#!/usr/bin/env python3
"""
AutoPilot AI 旅行规划Agent 演示脚本

展示系统的主要功能和使用方法。
"""
import asyncio
import sys
import os
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.travel_planner import TravelPlanRequest
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.core.logger import get_logger

logger = get_logger("demo")


class TravelPlannerDemo:
    """旅行规划Agent演示"""
    
    def __init__(self):
        self.agent = TravelPlannerAgent()
        
    def print_banner(self):
        """打印演示横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           🎯 AutoPilot AI 旅行规划Agent 演示                   ║
║                                                              ║
║              体验AI驱动的智能旅行规划服务                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        
    def get_demo_queries(self):
        """获取演示查询"""
        return [
            {
                "name": "上海文化美食3日游",
                "query": "我想去上海玩3天，喜欢文化景点和美食，预算3000元左右",
                "user_id": "demo_user_001"
            },
            {
                "name": "北京历史文化游",
                "query": "计划北京5天游，对历史文化感兴趣，想看故宫、长城等，预算5000元",
                "user_id": "demo_user_002"
            },
            {
                "name": "杭州休闲度假",
                "query": "杭州2天休闲游，想看西湖，品茶，住好一点的酒店",
                "user_id": "demo_user_003"
            },
            {
                "name": "成都美食探索",
                "query": "成都4天美食之旅，想吃正宗川菜，体验当地文化",
                "user_id": "demo_user_004"
            }
        ]
        
    async def run_demo_query(self, demo_query):
        """运行单个演示查询"""
        print(f"\n🎯 演示场景: {demo_query['name']}")
        print(f"📝 用户查询: {demo_query['query']}")
        print(f"👤 用户ID: {demo_query['user_id']}")
        print("-" * 60)
        
        # 创建请求
        request = TravelPlanRequest(
            user_id=demo_query['user_id'],
            query=demo_query['query']
        )
        
        # 收集事件
        events = []
        step_count = 0
        
        try:
            print("🚀 开始AI规划...")
            
            async for event in self.agent.plan_travel(request):
                events.append(event)
                step_count += 1
                
                # 显示关键事件
                if event.event_type.value == "thinking_step":
                    payload = event.payload
                    print(f"💭 思考: [{payload.get('category', 'other')}] {payload.get('content', '')}")
                    
                elif event.event_type.value == "tool_call":
                    payload = event.payload
                    print(f"🔧 调用工具: {payload.get('tool_name', '')} - {payload.get('parameters', {})}")
                    
                elif event.event_type.value == "tool_result":
                    payload = event.payload
                    success = "✅" if payload.get('success', False) else "❌"
                    print(f"{success} 工具结果: {payload.get('result_summary', '')}")
                    
                elif event.event_type.value == "planning_step":
                    payload = event.payload
                    print(f"📋 规划步骤: {payload.get('step', '')}")
                    
                elif event.event_type.value == "final_itinerary":
                    print("🎉 规划完成！")
                    self.display_itinerary_summary(event.payload)
                    
                # 添加小延迟以便观察
                await asyncio.sleep(0.1)
                
            print(f"\n✅ 演示完成，共生成 {len(events)} 个事件")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            
    def display_itinerary_summary(self, itinerary):
        """显示行程摘要"""
        print("\n" + "="*50)
        print("📋 行程摘要")
        print("="*50)
        
        summary = itinerary.get('summary', {})
        print(f"🏷️ 标题: {summary.get('title', '未知')}")
        print(f"📍 目的地: {summary.get('destination_city', '未知')}")
        print(f"📅 天数: {summary.get('days', 0)} 天")
        print(f"🏷️ 标签: {', '.join(summary.get('tags', []))}")
        
        # 显示每日计划概览
        daily_plans = itinerary.get('daily_plans', [])
        if daily_plans:
            print(f"\n📅 每日安排 ({len(daily_plans)} 天):")
            for i, plan in enumerate(daily_plans, 1):
                pois = plan.get('pois', [])
                print(f"  第{i}天: {plan.get('theme', '精彩行程')} ({len(pois)} 个景点)")
                
        # 显示预算信息
        budget = itinerary.get('budget_estimation')
        if budget:
            min_budget = budget.get('total_min', 0)
            max_budget = budget.get('total_max', 0)
            print(f"\n💰 预算估算: ¥{min_budget:.0f} - ¥{max_budget:.0f}")
            
        print("="*50)
        
    async def interactive_demo(self):
        """交互式演示"""
        print("\n🎮 交互式演示模式")
        print("请输入你的旅行规划需求，或输入 'quit' 退出")
        
        while True:
            try:
                print("\n" + "-"*40)
                query = input("📝 请描述你的旅行计划: ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("👋 感谢使用，再见！")
                    break
                    
                if not query:
                    print("⚠️ 请输入有效的旅行计划")
                    continue
                    
                user_id = input("👤 用户ID (默认: demo_user): ").strip() or "demo_user"
                
                # 运行规划
                demo_query = {
                    "name": "用户自定义规划",
                    "query": query,
                    "user_id": user_id
                }
                
                await self.run_demo_query(demo_query)
                
            except KeyboardInterrupt:
                print("\n👋 演示已中断，再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {str(e)}")
                
    async def run_all_demos(self):
        """运行所有预设演示"""
        demo_queries = self.get_demo_queries()
        
        print(f"\n📋 将运行 {len(demo_queries)} 个演示场景")
        
        for i, demo_query in enumerate(demo_queries, 1):
            print(f"\n{'='*60}")
            print(f"演示 {i}/{len(demo_queries)}")
            print('='*60)
            
            await self.run_demo_query(demo_query)
            
            # 询问是否继续
            if i < len(demo_queries):
                try:
                    response = input(f"\n⏸️ 按 Enter 继续下一个演示，或输入 'q' 退出: ").strip()
                    if response.lower() in ['q', 'quit']:
                        print("👋 演示已停止")
                        break
                except KeyboardInterrupt:
                    print("\n👋 演示已中断")
                    break
                    
        print(f"\n🎉 所有演示完成！")
        
    async def run(self):
        """运行演示"""
        self.print_banner()
        
        print("🎯 演示选项:")
        print("1. 运行预设演示场景")
        print("2. 交互式演示")
        print("3. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-3): ").strip()
                
                if choice == '1':
                    await self.run_all_demos()
                    break
                elif choice == '2':
                    await self.interactive_demo()
                    break
                elif choice == '3':
                    print("👋 再见！")
                    break
                else:
                    print("⚠️ 请输入有效选项 (1-3)")
                    
            except KeyboardInterrupt:
                print("\n👋 演示已中断，再见！")
                break


async def main():
    """主函数"""
    print("🚀 启动AutoPilot AI 旅行规划Agent演示")
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        demo = TravelPlannerDemo()
        await demo.run()
    except Exception as e:
        print(f"💥 演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
