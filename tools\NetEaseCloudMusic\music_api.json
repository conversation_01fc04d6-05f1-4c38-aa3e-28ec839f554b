{"openapi": "3.1.0", "info": {"title": "Music API", "version": "1.0.0", "description": "网易云音乐 API 服务"}, "servers": [{"url": "http://************:5000", "description": "本地开发服务器"}], "paths": {"/api/music/search_songs": {"post": {"summary": "根据关键词搜索歌曲", "description": "使用关键词搜索歌曲信息", "operationId": "searchSongs", "requestBody": {"description": "搜索关键词", "content": {"application/json": {"schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "搜索关键词"}}, "required": ["keyword"]}, "example": {"keyword": "夏天的风"}}}, "required": true}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Song"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/search_songs_by_artist": {"post": {"summary": "根据艺人搜索歌曲", "description": "使用艺人关键词搜索歌曲", "operationId": "searchSongsByArtist", "requestBody": {"description": "艺人关键词", "content": {"application/json": {"schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "艺人关键词"}}, "required": ["keyword"]}, "example": {"keyword": "周杰伦"}}}, "required": true}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Song"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/search_songs_by_artist_and_name": {"post": {"summary": "根据艺人名和歌曲名搜索", "description": "同时使用艺人名和歌曲名进行精确搜索", "operationId": "searchSongsByArtistAndName", "requestBody": {"description": "艺人名和歌曲名", "content": {"application/json": {"schema": {"type": "object", "properties": {"artistName": {"type": "string", "description": "艺人名称"}, "songName": {"type": "string", "description": "歌曲名称"}}, "required": ["<PERSON><PERSON><PERSON>", "songName"]}, "example": {"artistName": "周杰伦", "songName": "晴天"}}}, "required": true}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Song"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/search_playlists": {"post": {"summary": "搜索歌单", "description": "根据关键词搜索歌单", "operationId": "searchPlaylists", "requestBody": {"description": "搜索关键词", "content": {"application/json": {"schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "搜索关键词"}}, "required": ["keyword"]}, "example": {"keyword": "夏日"}}}, "required": true}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Playlist"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/search_albums": {"post": {"summary": "搜索专辑", "description": "根据关键词搜索专辑", "operationId": "searchAlbums", "requestBody": {"description": "搜索关键词", "content": {"application/json": {"schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "搜索关键词"}}, "required": ["keyword"]}, "example": {"keyword": "范特西"}}}, "required": true}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Album"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/search_albums_by_artist": {"post": {"summary": "根据艺人名和专辑名搜索专辑", "description": "使用艺人名和专辑名进行精确搜索", "operationId": "searchAlbumsByArtist", "requestBody": {"description": "艺人名和专辑名", "content": {"application/json": {"schema": {"type": "object", "properties": {"artistName": {"type": "string", "description": "艺人名称"}, "albumName": {"type": "string", "description": "专辑名称"}}, "required": ["<PERSON><PERSON><PERSON>", "albumName"]}, "example": {"artistName": "周杰伦", "albumName": "范特西"}}}, "required": true}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Album"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/search_artists": {"post": {"summary": "搜索艺人", "description": "根据关键词搜索艺人", "operationId": "searchArtists", "requestBody": {"description": "搜索关键词", "content": {"application/json": {"schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "搜索关键词"}}, "required": ["keyword"]}, "example": {"keyword": "周杰伦"}}}, "required": true}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Artist"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/hot_keywords": {"get": {"summary": "获取热门搜索关键词", "description": "获取当前热门搜索关键词列表", "operationId": "getHotKeywords", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/suggest_keywords": {"post": {"summary": "获取搜索建议", "description": "根据输入获取搜索关键词建议", "operationId": "getSuggestKeywords", "requestBody": {"description": "输入关键词", "content": {"application/json": {"schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "输入关键词"}}, "required": ["keyword"]}, "example": {"keyword": "周杰"}}}, "required": true}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/music/song_detail": {"post": {"summary": "获取歌曲详情", "description": "根据歌曲ID获取详细信息", "operationId": "getSongDetail", "requestBody": {"description": "歌曲ID", "content": {"application/json": {"schema": {"type": "object", "properties": {"songId": {"type": "string", "description": "歌曲ID"}}, "required": ["songId"]}, "example": {"songId": "123456789"}}}, "required": true}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SongDetail"}}}}, "400": {"description": "请求错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"Song": {"type": "object", "properties": {"id": {"type": "string", "description": "歌曲ID"}, "name": {"type": "string", "description": "歌曲名称"}, "artist": {"type": "string", "description": "艺人名称"}, "album": {"type": "string", "description": "专辑名称"}, "duration": {"type": "integer", "description": "歌曲时长（秒）"}, "url": {"type": "string", "description": "歌曲播放地址", "nullable": true}}}, "Album": {"type": "object", "properties": {"id": {"type": "string", "description": "专辑ID"}, "name": {"type": "string", "description": "专辑名称"}, "artist": {"type": "string", "description": "艺人名称"}, "description": {"type": "string", "description": "专辑描述", "nullable": true}}}, "Artist": {"type": "object", "properties": {"id": {"type": "string", "description": "艺人ID"}, "name": {"type": "string", "description": "艺人名称"}, "alias": {"type": "array", "items": {"type": "string"}, "description": "艺人别名", "nullable": true}, "cover_img_url": {"type": "string", "description": "艺人封面图片URL", "nullable": true}, "type": {"type": "string", "description": "艺人类型", "nullable": true}}}, "Playlist": {"type": "object", "properties": {"id": {"type": "string", "description": "歌单ID"}, "name": {"type": "string", "description": "歌单名称"}, "description": {"type": "string", "description": "歌单描述", "nullable": true}, "creator": {"type": "string", "description": "创建者"}, "song_count": {"type": "integer", "description": "歌曲数量"}, "play_count": {"type": "integer", "description": "播放次数"}, "songs": {"type": "array", "items": {"$ref": "#/components/schemas/Song"}, "description": "歌曲列表", "nullable": true}}}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}}, "SongDetail": {"type": "object", "properties": {"id": {"type": "string", "description": "歌曲ID"}, "name": {"type": "string", "description": "歌曲名称"}, "duration": {"type": "integer", "description": "歌曲时长"}, "albumName": {"type": "string", "description": "专辑名"}, "albumId": {"type": "string", "description": "专辑ID"}, "albumArtistId": {"type": "string", "description": "专辑艺人ID"}, "albumArtistName": {"type": "string", "description": "专辑艺人名"}, "artistId": {"type": "string", "description": "艺人ID"}, "artistName": {"type": "string", "description": "艺人名"}, "coverImgUrl": {"type": "string", "description": "封面URL", "nullable": true}, "mvId": {"type": "string", "description": "MV ID", "nullable": true}, "playUrl": {"type": "string", "description": "播放URL", "nullable": true}, "playFlag": {"type": "boolean", "description": "是否可播放", "default": false}, "downloadFlag": {"type": "boolean", "description": "是否可下载", "default": false}, "payPlayFlag": {"type": "boolean", "description": "是否需要付费播放", "default": false}, "payDownloadFlag": {"type": "boolean", "description": "是否需要付费下载", "default": false}, "vipFlag": {"type": "boolean", "description": "是否需要VIP", "default": false}, "vipPlayFlag": {"type": "boolean", "description": "是否需要VIP播放", "default": false}, "liked": {"type": "boolean", "description": "是否已喜欢", "default": false}, "maxBrLevel": {"type": "string", "description": "最大码率等级", "nullable": true}, "plLevel": {"type": "string", "description": "可播放码率等级", "nullable": true}, "dlLevel": {"type": "string", "description": "可下载码率等级", "nullable": true}, "songSize": {"type": "integer", "description": "歌曲大小", "nullable": true}, "songMd5": {"type": "string", "description": "歌曲MD5", "nullable": true}, "songTag": {"type": "array", "items": {"type": "string"}, "description": "歌曲标签", "nullable": true}, "songFee": {"type": "integer", "description": "付费类型", "nullable": true}, "br": {"type": "integer", "description": "码率", "nullable": true}, "audioFlag": {"type": "integer", "description": "音效标记", "nullable": true}, "effects": {"type": "string", "description": "音效信息", "nullable": true}, "visible": {"type": "boolean", "description": "是否有版权", "default": true}}}, "PlaylistDetail": {"type": "object", "properties": {"id": {"type": "string", "description": "歌单ID"}, "name": {"type": "string", "description": "歌单名称"}, "describe": {"type": "string", "description": "歌单描述", "nullable": true}, "coverImgUrl": {"type": "string", "description": "封面URL", "nullable": true}, "creatorNickName": {"type": "string", "description": "创建者昵称"}, "playCount": {"type": "string", "description": "播放次数"}, "subscribedCount": {"type": "string", "description": "收藏数"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签", "nullable": true}, "creatorId": {"type": "string", "description": "创建者ID"}, "createTime": {"type": "string", "description": "创建时间"}, "subed": {"type": "boolean", "description": "是否已收藏", "default": false}, "trackCount": {"type": "integer", "description": "歌曲数量", "default": 0}, "specialType": {"type": "integer", "description": "特殊类型", "nullable": true}}}, "AlbumArtist": {"type": "object", "properties": {"id": {"type": "string", "description": "艺人ID"}, "name": {"type": "string", "description": "艺人名称"}}}, "AlbumDetail": {"type": "object", "properties": {"id": {"type": "string", "description": "专辑ID"}, "name": {"type": "string", "description": "专辑名称"}, "language": {"type": "string", "description": "语言", "nullable": true}, "coverImgUrl": {"type": "string", "description": "封面URL", "nullable": true}, "company": {"type": "string", "description": "发行公司", "nullable": true}, "transName": {"type": "string", "description": "翻译名称", "nullable": true}, "aliaName": {"type": "string", "description": "别名", "nullable": true}, "genre": {"type": "string", "description": "流派", "nullable": true}, "artists": {"type": "array", "items": {"$ref": "#/components/schemas/AlbumArtist"}, "description": "艺人列表"}, "briefDesc": {"type": "string", "description": "简介", "nullable": true}, "description": {"type": "string", "description": "详细描述", "nullable": true}, "publishTime": {"type": "string", "description": "发布时间", "nullable": true}}}, "HiResCategory": {"type": "object", "properties": {"category": {"type": "string", "description": "分类名称"}, "songDetailVoList": {"type": "array", "items": {"$ref": "#/components/schemas/SongDetail"}, "description": "歌曲列表", "nullable": true}, "playlistDetailVoList": {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistDetail"}, "description": "歌单列表", "nullable": true}, "albumDetailVoList": {"type": "array", "items": {"$ref": "#/components/schemas/AlbumDetail"}, "description": "专辑列表", "nullable": true}}}}}}