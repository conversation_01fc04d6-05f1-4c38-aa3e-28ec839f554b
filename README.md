# AutoPilot AI - 智能旅行规划Agent系统

一个基于Python的智能旅行规划Agent系统，支持多种LLM后端，具备强大的个性化推荐能力和完整的数据库驱动架构。

## 🌟 主要特性

### 🚀 旅行规划核心能力
- **智能意图理解**：基于LLM的自然语言理解，准确提取旅行需求
- **个性化画像系统**：MySQL驱动的用户记忆和偏好学习
- **四阶段规划流程**：意图理解→工具执行→智能决策→结果生成
- **实时流式输出**：SSE技术展示AI思考过程和规划进度
- **高德地图集成**：完整的地理服务、POI推荐、路线规划

### 🧠 个性化智能系统
- **记忆驱动推荐**：从用户历史记忆中提取偏好和旅行习惯
- **智能POI评分**：基于用户画像的多维度评分算法
- **自动学习机制**：每次规划自动保存用户记忆，持续优化推荐
- **偏好模式识别**：智能分析用户的活动偏好、预算习惯、目的地偏好

### 🏗️ 技术架构
- **多数据库架构**：MySQL用户画像 + MongoDB行程存储 + Redis缓存
- **统一配置管理**：基于Pydantic的类型安全配置系统
- **多LLM支持**：支持智谱AI、OpenAI等多种模型提供商
- **微服务设计**：模块化组件，支持独立扩展
- **完整监控体系**：结构化日志、性能监控、分布式追踪

### 🔧 开发体验
- **TDD开发模式**：完整的单元测试和集成测试覆盖
- **类型安全**：全面的类型注解和mypy检查
- **热重载开发**：支持开发环境快速迭代
- **容器化部署**：Docker支持，一键部署

## 🚀 快速开始

### 1. 环境设置

```bash
# 克隆项目
git clone <repository-url>
cd autopilotai2

# 创建虚拟环境
python -m venv .venv
.\.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -e .
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
# AI模型配置
BASIC_LLM_MODEL=glm-4-flash
BASIC_LLM_API_KEY=your-zhipu-api-key
BASIC_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4

REASONING_LLM_MODEL=glm-z1-flash
REASONING_LLM_API_KEY=your-zhipu-api-key
REASONING_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4

# 高德地图API
AMAP_API_KEY=your-amap-api-key

# 数据库配置
MYSQL_HOST=your-mysql-host
MYSQL_PORT=19090
MYSQL_USER=root
MYSQL_PASSWORD=your-password

MONGODB_HOST=your-mongodb-host
MONGODB_PORT=27017
MONGODB_USERNAME=admin
MONGODB_PASSWORD=your-password
```

### 3. 启动服务

```bash
# 检查环境
python start_server.py --check-only

# 启动开发服务器
python start_server.py --reload

# 访问系统
# 前端: http://localhost:8000/static/index.html
# API文档: http://localhost:8000/docs
```

## 📚 使用示例

### 旅行规划API调用

```python
import asyncio
import httpx

async def plan_travel():
    async with httpx.AsyncClient() as client:
        # 创建规划请求
        response = await client.post(
            "http://localhost:8000/api/travel/plan",
            json={
                "user_id": "user_001",
                "query": "我想去西安玩3天，喜欢历史文化景点和美食"
            }
        )
        
        plan_data = response.json()
        trace_id = plan_data["trace_id"]
        
        # 获取流式规划过程
        async with client.stream(
            "GET", 
            f"http://localhost:8000/api/travel/plan/{trace_id}/stream",
            params={"user_id": "user_001"}
        ) as stream:
            async for line in stream.aiter_lines():
                if line.startswith("data: "):
                    event_data = json.loads(line[6:])
                    print(f"事件: {event_data['event_type']}")
                    
                    if event_data['event_type'] == 'final_itinerary':
                        print("最终行程:", event_data['payload'])
                        break

asyncio.run(plan_travel())
```

### 直接使用TravelPlannerAgent

```python
import asyncio
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import TravelPlanRequest

async def main():
    # 创建旅行规划Agent
    agent = TravelPlannerAgent()
    
    # 创建规划请求
    request = TravelPlanRequest(
        user_id="user_001",
        query="我想去北京玩4天，预算5000元，喜欢历史建筑和传统文化"
    )
    
    # 流式获取规划过程
    async for event in agent.plan_travel(request):
        print(f"[{event.event_type.value}] {event.payload}")
        
        # 个性化相关事件
        if "用户画像" in str(event.payload):
            print("🧠 个性化分析中...")
        elif "记忆" in str(event.payload):
            print("💾 学习用户偏好...")
        elif event.event_type.value == "final_itinerary":
            print("🎯 个性化行程生成完成！")

if __name__ == "__main__":
    asyncio.run(main())
```

## 🏗️ 项目架构

```
autopilotai2/
├── src/                          # 主要源码
│   ├── agents/                   # Agent实现
│   │   └── travel_planner_agent.py    # 核心旅行规划Agent
│   ├── core/                     # 核心模块
│   │   ├── config.py            # 配置管理
│   │   ├── logger.py            # 日志系统
│   │   └── llm_manager.py       # LLM管理器
│   ├── models/                   # 数据模型
│   │   ├── travel_planner.py    # 核心数据模型
│   │   ├── mysql_models.py      # MySQL数据库模型
│   │   └── mysql_crud.py        # 数据库CRUD操作
│   ├── database/                 # 数据库层
│   │   ├── mysql_client.py      # MySQL连接池
│   │   └── mongodb_client.py    # MongoDB连接
│   ├── tools/                    # 工具集成
│   │   ├── amap_mcp_client.py   # 高德地图MCP客户端
│   │   └── travel_planner/      # 旅行规划专用工具
│   └── api/                      # API路由
├── tests/                        # 测试套件
│   ├── unit/                    # 单元测试
│   ├── integration/             # 集成测试
│   └── scripts/                 # 测试脚本
├── config/                       # 配置文件
├── static/                       # 前端静态文件
└── docs/                         # 项目文档
```

## 🧠 核心组件详解

### TravelPlannerAgent - 四阶段规划流程

#### Phase 1: 意图理解与个性化融合
```python
async def _phase1_intent_and_personalization(self, state: AgentState):
    # 1. 使用LLM理解用户意图，提取关键信息
    await self._understand_intent(state)
    
    # 2. 从MySQL读取用户画像和历史记忆
    await self._get_user_profile(state)
    
    # 3. 融合个性化信息，构建个性化查询
```

#### Phase 2: 动态工具规划与并行执行
```python
async def _phase2_parallel_tool_execution(self, state: AgentState):
    # 四层并行工具调用
    await self._layer1_basic_info(state)      # 地理定位、天气
    await self._layer2_core_poi_info(state)   # POI搜索、推荐
    await self._layer3_route_and_auxiliary(state)  # 路线、预算
    await self._layer4_deep_mining(state)     # 深度信息挖掘
```

#### Phase 3: 数据综合与智能决策
```python
async def _phase3_data_synthesis_and_reasoning(self, state: AgentState):
    # 基于用户画像的智能决策
    await self._analyze_weather_impact(state)
    await self._score_and_rank_pois(state)    # 个性化POI评分
    await self._orchestrate_itinerary(state)  # 智能行程编排
```

#### Phase 4: 结果生成与记忆存储
```python
async def _phase4_result_generation_and_storage(self, state: AgentState):
    # 生成最终行程
    await self._generate_final_itinerary(state)
    
    # 保存用户记忆，更新画像
    await self._save_user_memory(state)
```

### MySQL用户画像系统

#### 数据库架构
- **dh_user_profile**: 用户基础信息、记忆、画像
- **dh_tripplanner**: AI规划会话、行程、POI数据

#### 个性化算法
```python
def _calculate_poi_score(self, poi: dict, state: AgentState) -> float:
    """基于用户画像和记忆的POI评分算法"""
    
    # 用户画像匹配度 (40% 权重)
    profile_score = self._calculate_profile_match_score(poi, state.user_profile)
    
    # 用户记忆匹配度 (30% 权重)
    memory_score = self._calculate_memory_match_score(poi, state.user_memories)
    
    # POI基础评分 (30% 权重)
    base_score = float(poi.get('rating', 0)) / 5.0
    
    return profile_score * 0.4 + memory_score * 0.3 + base_score * 0.3
```

## 🔧 支持的服务

### AI模型服务
- **智谱AI glm-z1-flash**: 复杂推理和决策
- **智谱AI glm-4-flash**: 快速交互和格式化
- **OpenAI系列**: 计划支持（GPT-4、GPT-3.5-turbo等）

### 数据库服务
- **MySQL 8.0+**: 用户画像和记忆系统
- **MongoDB 4.4+**: 行程文档存储
- **Redis 6.0+**: 缓存和会话管理

### 地图服务
- **高德地图API**: 地理编码、POI搜索、路线规划
- **MCP接口**: https://mcp.amap.com/sse

## 🧪 测试体系

### 测试覆盖
- **MySQL模型测试**: 数据模型定义、CRUD操作、字段验证
- **Agent功能测试**: 四阶段流程、个性化算法、偏好提取
- **集成测试**: 端到端流程验证、数据库集成测试
- **API测试**: HTTP接口、SSE流式输出

### 运行测试
```bash
# MySQL模型集成测试
python tests/scripts/test_mysql_models_integration.py

# Agent个性化功能测试
python tests/scripts/test_agent_simple.py

# 数据库集成测试
python tests/scripts/test_agent_database_basic.py

# 完整API测试
pytest tests/integration/ -v
```

## 📖 文档资源

- [快速开始指南](QUICK_START_GUIDE.md) - 详细的安装和使用指南
- [数据库架构文档](src/models/mysql_models.py) - MySQL数据模型说明
- [Agent改造总结](改造完成总结.md) - 个性化功能改造记录
- [API接口文档](http://localhost:8000/docs) - Swagger自动生成文档

## 🛠️ 开发指南

### 开发环境搭建
```bash
# 安装开发依赖
pip install -e ".[dev]"

# 代码格式化
black src/ tests/

# 类型检查
mypy src/

# 运行所有测试
pytest tests/ -v --cov=src
```

### 添加新功能
1. **扩展用户画像**: 在 `src/models/mysql_models.py` 中添加新字段
2. **优化个性化算法**: 修改 `src/agents/travel_planner_agent.py` 中的评分逻辑
3. **集成新工具**: 在 `src/tools/` 目录下添加新的工具客户端
4. **扩展API**: 在 `src/api/` 目录下添加新的路由

### 贡献指南
1. Fork项目并创建特性分支
2. 编写测试用例，确保覆盖率
3. 遵循代码规范，通过类型检查
4. 更新相关文档
5. 提交Pull Request

## 📊 项目状态

| 模块 | 状态 | 测试覆盖率 | 说明 |
|------|------|------------|------|
| 配置管理 | ✅ 完成 | 100% | 多环境配置，类型安全 |
| 日志系统 | ✅ 完成 | 99% | 结构化日志、分布式追踪 |
| LLM管理器 | ✅ 完成 | 90% | 支持多种LLM后端 |
| MySQL用户画像 | ✅ 完成 | 95% | 完整的CRUD和模型系统 |
| 旅行规划Agent | ✅ 完成 | 90% | 四阶段流程，个性化推荐 |
| 高德地图集成 | ✅ 完成 | 85% | 完整的地理服务API |
| Web API服务 | ✅ 完成 | 80% | FastAPI + SSE流式输出 |
| 前端界面 | ✅ 完成 | 70% | 基础交互界面 |

## 🔮 规划路线

### 已完成（第一阶段）
- ✅ 核心Agent框架和四阶段流程
- ✅ MySQL用户画像和记忆系统
- ✅ 个性化POI推荐算法
- ✅ 高德地图API完整集成
- ✅ 流式SSE输出和Web界面
- ✅ 完整的测试体系

### 进行中（第二阶段）
- 🔄 MongoDB连接优化和错误修复
- 🔄 个性化算法效果评估和优化
- 🔄 更丰富的偏好提取模式
- 🔄 性能监控和数据库索引优化

### 计划中（第三阶段）
- 📋 多Agent协作（专家Agent、决策Agent等）
- 📋 用户反馈学习机制
- 📋 A/B测试框架
- 📋 移动端适配和小程序集成
- 📋 企业级部署方案

## 🌟 技术亮点

### 个性化推荐系统
- **记忆驱动**: 基于用户历史记忆的智能偏好提取
- **多维度评分**: 综合用户画像、记忆匹配、基础评分的算法
- **自动学习**: 每次规划自动沉淀新知识，持续优化

### 智能决策引擎
- **四层并行**: 同时执行多个工具调用，提升效率
- **动态权重**: 根据用户偏好动态调整不同因素的权重
- **上下文感知**: 基于天气、时间、用户状态的智能决策

### 企业级架构
- **微服务设计**: 模块化组件，支持独立扩展
- **多数据库**: MySQL画像 + MongoDB存储 + Redis缓存
- **容错机制**: 完善的异常处理和降级策略

## 📞 支持

### 开发团队
- 📧 邮箱: <EMAIL>
- 💬 技术交流群: AutoPilot-AI-Dev
- 🐛 Issue跟踪: GitHub Issues
- 📖 技术博客: [待开通]

### 商业合作
- 📧 商务邮箱: <EMAIL>
- 📞 合作热线: 400-XXX-XXXX
- 🤝 企业定制: 支持定制化开发
- 🌐 官方网站: [待开通]

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**AutoPilot AI** - 智能旅行规划的未来，个性化推荐的典范

🎯 **核心价值**: 让每一次旅行规划都是独一无二的个性化体验

🚀 **技术愿景**: 构建最智能、最懂用户的AI旅行规划系统