"""
CRUD operations for itinerary related models.

This module provides Create, Read, Update, Delete operations for all models.
"""
from typing import List, Optional, TypeVar, Generic, Type
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from .itinerary_schemas import (
    AIVlog, Itinerary, ItineraryDay, ItineraryDayPOI, POI, POIType,
    StatusDefinition, Tag, UserAppSettings, UserMedia, UserTripStats,
    VlogMediaItem
)

ModelType = TypeVar("ModelType")

class CRUDBase(Generic[ModelType]):
    """Base class for CRUD operations."""
    
    def __init__(self, model: Type[ModelType]):
        self.model = model

    def get(self, db: Session, id: int) -> Optional[ModelType]:
        """Get a single record by ID."""
        return db.query(self.model).filter(self.model.id == id).first()

    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Get multiple records with pagination."""
        return db.query(self.model).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: dict) -> ModelType:
        """Create a new record."""
        db_obj = self.model(**obj_in)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, db_obj: ModelType, obj_in: dict) -> ModelType:
        """Update a record."""
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete(self, db: Session, *, id: int) -> ModelType:
        """Delete a record."""
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj

class CRUDItinerary(CRUDBase[Itinerary]):
    """CRUD operations for Itinerary model."""
    
    def create(self, db: Session, *, obj_in: dict) -> Itinerary:
        """Create a new itinerary with tags."""
        # Extract tags from input data
        tags_data = obj_in.pop('tags', [])
        
        # Filter out None values to avoid passing None to database model
        filtered_data = {k: v for k, v in obj_in.items() if v is not None}
        
        # Create the itinerary
        db_obj = self.model(**filtered_data)
        db.add(db_obj)
        db.flush()  # Flush to get the ID
        
        # Add tags if provided
        if tags_data:
            for tag_data in tags_data:
                if isinstance(tag_data, dict) and 'id' in tag_data:
                    # If tag ID is provided, find the existing tag
                    tag = db.query(Tag).filter(Tag.id == tag_data['id']).first()
                    if tag:
                        db_obj.tags.append(tag)
                elif isinstance(tag_data, str):
                    # If tag name is provided, find or create the tag
                    tag = db.query(Tag).filter(Tag.name == tag_data).first()
                    if not tag:
                        tag = Tag(name=tag_data)
                        db.add(tag)
                        db.flush()
                    db_obj.tags.append(tag)
        
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(self, db: Session, *, db_obj: Itinerary, obj_in: dict) -> Itinerary:
        """Update an itinerary with tags."""
        # Extract tags from input data
        tags_data = obj_in.pop('tags', None)
        
        # Filter out None values to avoid passing None to database model
        filtered_data = {k: v for k, v in obj_in.items() if v is not None}
        
        # Update basic fields
        for field, value in filtered_data.items():
            setattr(db_obj, field, value)
        
        # Update tags if provided
        if tags_data is not None:
            # Clear existing tags
            db_obj.tags.clear()
            
            # Add new tags
            for tag_data in tags_data:
                if isinstance(tag_data, dict) and 'id' in tag_data:
                    # If tag ID is provided, find the existing tag
                    tag = db.query(Tag).filter(Tag.id == tag_data['id']).first()
                    if tag:
                        db_obj.tags.append(tag)
                elif isinstance(tag_data, str):
                    # If tag name is provided, find or create the tag
                    tag = db.query(Tag).filter(Tag.name == tag_data).first()
                    if not tag:
                        tag = Tag(name=tag_data)
                        db.add(tag)
                        db.flush()
                    db_obj.tags.append(tag)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def _format_itinerary_with_status(self, itinerary: Itinerary) -> dict:
        """Format itinerary with status display name."""
        return {
            "id": itinerary.id,
            "title": itinerary.title,
            "city_name": itinerary.city_name,
            "total_days": itinerary.total_days,
            "start_date": itinerary.start_date,
            "total_distance": float(itinerary.total_distance) if itinerary.total_distance else None,
            "cover_image_url": itinerary.cover_image_url,
            "notes": itinerary.notes,
            "is_template": itinerary.is_template,
            "created_at": itinerary.created_at,
            "updated_at": itinerary.updated_at,
            "status_id": itinerary.status_id,
            "status_display_name": itinerary.status.display_name if itinerary.status else "未知状态",
            "tags": itinerary.tags
        }
    
    def get_by_user(self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100) -> List[Itinerary]:
        """Get all itineraries for a specific user."""
        return (db.query(self.model)
                .filter(self.model.user_id == user_id)
                .offset(skip)
                .limit(limit)
                .all())

    def get_by_user_with_status(self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100, status_key: Optional[str] = None) -> List[dict]:
        """Get all itineraries for a specific user with status display names using SQLAlchemy relationships."""
        from sqlalchemy.orm import joinedload
        
        # 构建查询
        query = (db.query(self.model)
                .options(
                    joinedload(self.model.status),  # 预加载状态信息
                    joinedload(self.model.tags)     # 预加载标签信息
                )
                .filter(self.model.user_id == user_id))
        
        # 如果提供了status_key，添加状态过滤条件
        if status_key:
            query = query.join(self.model.status).filter(self.model.status.has(status_key=status_key))
        
        # 执行查询
        itineraries = query.offset(skip).limit(limit).all()
        
        # 格式化结果
        return [self._format_itinerary_with_status(itinerary) for itinerary in itineraries]

    def get_templates(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Itinerary]:
        """Get all template itineraries."""
        return (db.query(self.model)
                .filter(self.model.is_template == True)
                .offset(skip)
                .limit(limit)
                .all())

    def get_templates_with_status(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[dict]:
        """Get all template itineraries with status display names using SQLAlchemy relationships."""
        from sqlalchemy.orm import joinedload
        
        # 使用关系预加载，一次性获取所有相关数据
        itineraries = (db.query(self.model)
                      .options(
                          joinedload(self.model.status),  # 预加载状态信息
                          joinedload(self.model.tags)     # 预加载标签信息
                      )
                      .filter(self.model.is_template == True)
                      .offset(skip)
                      .limit(limit)
                      .all())
        
        # 格式化结果
        return [self._format_itinerary_with_status(itinerary) for itinerary in itineraries]

    def search_by_city(self, db: Session, *, city_name: str, skip: int = 0, limit: int = 100) -> List[Itinerary]:
        """Search itineraries by city name."""
        return (db.query(self.model)
                .filter(self.model.city_name.ilike(f"%{city_name}%"))
                .offset(skip)
                .limit(limit)
                .all())

    def search_by_city_with_status(self, db: Session, *, city_name: str, skip: int = 0, limit: int = 100) -> List[dict]:
        """Search itineraries by city name with status display names using SQLAlchemy relationships."""
        from sqlalchemy.orm import joinedload
        
        # 使用关系预加载，一次性获取所有相关数据
        itineraries = (db.query(self.model)
                      .options(
                          joinedload(self.model.status),  # 预加载状态信息
                          joinedload(self.model.tags)     # 预加载标签信息
                      )
                      .filter(self.model.city_name.ilike(f"%{city_name}%"))
                      .offset(skip)
                      .limit(limit)
                      .all())
        
        # 格式化结果
        return [self._format_itinerary_with_status(itinerary) for itinerary in itineraries]

    def get_with_status(self, db: Session, id: int) -> Optional[dict]:
        """Get a single itinerary by ID with status display name using SQLAlchemy relationships."""
        from sqlalchemy.orm import joinedload
        
        itinerary = (db.query(self.model)
                    .options(
                        joinedload(self.model.status),  # 预加载状态信息
                        joinedload(self.model.tags)     # 预加载标签信息
                    )
                    .filter(self.model.id == id)
                    .first())
        
        if not itinerary:
            return None
        
        return self._format_itinerary_with_status(itinerary)

    def get_complete_detail(self, db: Session, id: int) -> Optional[dict]:
        """Get complete itinerary details with all related data."""
        from sqlalchemy.orm import joinedload
        
        # Get itinerary with all related data using eager loading
        itinerary = (db.query(self.model)
                    .options(
                        joinedload(self.model.status),
                        joinedload(self.model.tags),
                        joinedload(self.model.days).joinedload(ItineraryDay.pois).joinedload(POI.poi_type)
                    )
                    .filter(self.model.id == id)
                    .first())
        
        if not itinerary:
            return None
        
        # Format days with complete POI information
        days_detail = []
        for day in itinerary.days:
            pois_detail = []
            for poi in day.pois:
                # Get POI type information
                poi_type_info = {
                    "id": poi.poi_type.id,
                    "type_key": poi.poi_type.type_key,
                    "display_name": poi.poi_type.display_name,
                    "icon_name": poi.poi_type.icon_name
                } if poi.poi_type else None
                
                # Get sequence and user notes from association table
                day_poi_assoc = (db.query(ItineraryDayPOI)
                               .filter(
                                   ItineraryDayPOI.itinerary_day_id == day.id,
                                   ItineraryDayPOI.poi_id == poi.id
                               )
                               .first())
                
                sequence = day_poi_assoc.sequence if day_poi_assoc else 1
                user_notes = day_poi_assoc.user_notes if day_poi_assoc else None
                
                poi_detail = {
                    "id": poi.id,
                    "name": poi.name,
                    "address": poi.address,
                    "latitude": float(poi.latitude) if poi.latitude else None,
                    "longitude": float(poi.longitude) if poi.longitude else None,
                    "description": poi.description,
                    "images": poi.images,
                    "narrations": poi.narrations,
                    "rating": poi.rating,
                    "opening_hours": poi.opening_hours,
                    "phone_number": poi.phone_number,
                    "source_id": poi.source_id,
                    "last_sync_at": poi.last_sync_at.isoformat() if poi.last_sync_at else None,
                    "poi_type": poi_type_info,
                    "sequence": sequence,
                    "user_notes": user_notes
                }
                pois_detail.append(poi_detail)
            
            # Sort POIs by sequence
            pois_detail.sort(key=lambda x: x["sequence"])
            
            day_detail = {
                "id": day.id,
                "day_number": day.day_number,
                "summary": day.summary,
                "distance": float(day.distance) if day.distance else None,
                "pois": pois_detail
            }
            days_detail.append(day_detail)
        
        # Sort days by day_number
        days_detail.sort(key=lambda x: x["day_number"])
        
        # Format tags
        tags_detail = [
            {
                "id": tag.id,
                "name": tag.name,
                "category": tag.category
            }
            for tag in itinerary.tags
        ]
        
        # Format status information
        status_info = {
            "id": itinerary.status.id,
            "status_key": itinerary.status.status_key,
            "display_name": itinerary.status.display_name,
            "scope": itinerary.status.scope
        } if itinerary.status else None
        # Complete itinerary response
        return {
            "id": itinerary.id,
            "user_id": itinerary.user_id,
            "title": itinerary.title,
            "city_name": itinerary.city_name,
            "total_days": itinerary.total_days,
            "start_date": itinerary.start_date.isoformat() if itinerary.start_date else None,
            "total_distance": float(itinerary.total_distance) if itinerary.total_distance else None,
            "cover_image_url": itinerary.cover_image_url,
            "notes": itinerary.notes,
            "is_template": itinerary.is_template,
            "created_at": itinerary.created_at.isoformat(),
            "updated_at": itinerary.updated_at.isoformat(),
            "status": status_info,
            "tags": tags_detail,
            "days": days_detail
        }

class CRUDItineraryDay(CRUDBase[ItineraryDay]):
    """CRUD operations for ItineraryDay model."""
    
    def get_by_itinerary(self, db: Session, *, itinerary_id: int) -> List[ItineraryDay]:
        """Get all days for a specific itinerary."""
        return (db.query(self.model)
                .filter(self.model.itinerary_id == itinerary_id)
                .order_by(self.model.day_number)
                .all())

class CRUDPOI(CRUDBase[POI]):
    """CRUD operations for POI model."""
    
    def search(self, db: Session, *, keyword: str, type_id: Optional[int] = None) -> List[POI]:
        """Search POIs by keyword and optionally filter by type."""
        query = db.query(self.model).filter(
            or_(
                self.model.name.ilike(f"%{keyword}%"),
                self.model.address.ilike(f"%{keyword}%")
            )
        )
        if type_id:
            query = query.filter(self.model.type_id == type_id)
        return query.all()

    def get_by_coordinates(self, db: Session, *, lat: float, lng: float, radius: float) -> List[POI]:
        """Get POIs within a certain radius of coordinates."""
        # Note: This is a simplified version. For production, use spatial database features
        return (db.query(self.model)
                .filter(
                    and_(
                        func.abs(self.model.latitude - lat) <= radius,
                        func.abs(self.model.longitude - lng) <= radius
                    )
                )
                .all())

class CRUDUserMedia(CRUDBase[UserMedia]):
    """CRUD operations for UserMedia model."""
    
    def get_by_user(self, db: Session, *, user_id: int, media_type: Optional[str] = None) -> List[UserMedia]:
        """Get all media for a specific user, optionally filtered by type."""
        query = db.query(self.model).filter(self.model.user_id == user_id)
        if media_type:
            query = query.filter(self.model.media_type == media_type)
        return query.order_by(self.model.uploaded_at.desc()).all()

class CRUDVlog(CRUDBase[AIVlog]):
    """CRUD operations for AIVlog model."""
    
    def get_by_user(self, db: Session, *, user_id: int, status_id: Optional[int] = None) -> List[AIVlog]:
        """Get all vlogs for a specific user, optionally filtered by status."""
        query = db.query(self.model).filter(self.model.user_id == user_id)
        if status_id:
            query = query.filter(self.model.status_id == status_id)
        return query.order_by(self.model.created_at.desc()).all()

# Create CRUD instances for each model
itinerary = CRUDItinerary(Itinerary)
itinerary_day = CRUDItineraryDay(ItineraryDay)
poi = CRUDPOI(POI)
user_media = CRUDUserMedia(UserMedia)
vlog = CRUDVlog(AIVlog)
status = CRUDBase(StatusDefinition)
tag = CRUDBase(Tag)
poi_type = CRUDBase(POIType)
user_settings = CRUDBase(UserAppSettings)
user_trip_stats = CRUDBase(UserTripStats)
