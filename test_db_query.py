#!/usr/bin/env python3
"""
测试数据库查询脚本
查询用户ID为1的真实数据
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.mysql_client import get_db, fetch_one, fetch_all


async def query_user_data():
    """查询用户ID为1的所有相关数据"""
    user_id = 1
    
    print(f"🔍 开始查询用户ID {user_id} 的数据...")
    
    try:
        async with get_db() as db:
            print("\n📊 1. 查询用户基本信息 (dh_user_profile.users)")
            user_query = """
            SELECT id, nickname, avatar_url, status, created_at, updated_at
            FROM dh_user_profile.users
            WHERE id = %s
            """
            user_info = await fetch_one(user_query, (user_id,))
            print(f"用户基本信息: {user_info}")
            
            print("\n📊 2. 查询用户记忆 (dh_user_profile.user_memories)")
            memories_query = """
            SELECT user_id, memory_type, content, keywords, created_at
            FROM dh_user_profile.user_memories
            WHERE user_id = %s
            ORDER BY created_at DESC
            LIMIT 10
            """
            memories = await fetch_all(memories_query, (user_id,))
            print(f"用户记忆数量: {len(memories) if memories else 0}")
            if memories:
                for i, memory in enumerate(memories[:3]):  # 只显示前3条
                    print(f"  记忆{i+1}: {memory}")
            
            print("\n📊 3. 查询用户画像摘要 (dh_user_profile.user_summaries)")
            summary_query = """
            SELECT user_id, summary, keywords, model_version, updated_at
            FROM dh_user_profile.user_summaries
            WHERE user_id = %s
            """
            summary = await fetch_one(summary_query, (user_id,))
            print(f"用户画像摘要: {summary}")
            
            print("\n📊 4. 查询用户旅行偏好 (dh_tripplanner.user_travel_profiles)")
            travel_profile_query = """
            SELECT user_id, travel_style, accommodation_pref, transportation_pref,
                   travel_summary, travel_keywords, updated_at
            FROM dh_tripplanner.user_travel_profiles
            WHERE user_id = %s
            """
            travel_profile = await fetch_one(travel_profile_query, (user_id,))
            print(f"用户旅行偏好: {travel_profile}")
            
            print("\n📊 5. 查询历史行程 (dh_tripplanner.itineraries)")
            itineraries_query = """
            SELECT id, user_id, title, destination, start_date, end_date, 
                   status, created_at
            FROM dh_tripplanner.itineraries
            WHERE user_id = %s
            ORDER BY created_at DESC
            LIMIT 5
            """
            itineraries = await fetch_all(itineraries_query, (user_id,))
            print(f"历史行程数量: {len(itineraries) if itineraries else 0}")
            if itineraries:
                for i, itinerary in enumerate(itineraries):
                    print(f"  行程{i+1}: {itinerary}")
                    
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(query_user_data())
