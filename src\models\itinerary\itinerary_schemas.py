"""
Itinerary related data models.

This module contains the SQLAlchemy models for the trip planning system database tables.
"""
from datetime import datetime, date
from typing import List, Optional
from sqlalchemy import (
    Column, Integer, String, Float, Boolean, Text, 
    DateTime, Date, Enum, JSON, DECIMAL, ForeignKey
)
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import LONGTEXT

Base = declarative_base()

class ItineraryTag(Base):
    """Itinerary and tag many-to-many association model."""
    __tablename__ = 'itinerary_tags'
    __table_args__ = {'comment': '行程与标签的多对多关联表'}

    itinerary_id = Column(Integer, ForeignKey('itineraries.id'), primary_key=True, comment='逻辑外键, 关联到 itineraries.id')
    tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, comment='逻辑外键, 关联到 tags.id')


class AIVlog(Base):
    """AI generated vlog model."""
    __tablename__ = 'ai_vlogs'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='Vlog主键, 自增')
    external_task_id = Column(Integer, nullable=False, comment='第三方的任务ID')
    user_id = Column(Integer, nullable=False, comment='逻辑外键, 关联到 dh_user_profile.users.id')
    status_id = Column(Integer, nullable=False, default=5, comment='逻辑外键, 关联到 status_definitions.id (scope=\'VLOG\')')
    title = Column(String(100), nullable=False, comment='Vlog标题')
    config = Column(JSON, comment='Vlog生成配置 (JSON格式, 包含风格,模板,时长,文案等)')
    final_video_url = Column(String(255), comment='最终生成的视频URL')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment='创建时间')
    completed_at = Column(DateTime, comment='完成时间')

class Itinerary(Base):
    """Trip itinerary model."""
    __tablename__ = 'itineraries'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='行程主键, 自增')
    user_id = Column(Integer, nullable=False, comment='逻辑外键, 关联到 dh_user_profile.users.id')
    status_id = Column(Integer, nullable=False, default=1, comment='逻辑外键, 关联到 status_definitions.id (scope=\'ITINERARY\')')
    title = Column(String(100), nullable=False, comment='行程标题')
    city_name = Column(String(50), nullable=False, comment='目的地城市名称')
    total_days = Column(Integer, nullable=False, comment='行程总天数')
    start_date = Column(Date, comment='行程开始日期')
    total_distance = Column(DECIMAL(10, 2), comment='行程预估总里程 (公里)')
    cover_image_url = Column(String(255), comment='行程封面图URL')
    notes = Column(Text, comment='行程备注或注意事项')
    is_template = Column(Boolean, nullable=False, default=False, comment='是否为精选路线模板')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    days = relationship("ItineraryDay", back_populates="itinerary")
    tags = relationship("Tag", secondary="itinerary_tags")
    status = relationship("StatusDefinition", 
                         primaryjoin="and_(Itinerary.status_id == StatusDefinition.id, "
                                   "StatusDefinition.scope == 'ITINERARY')",
                         foreign_keys=[status_id])

class ItineraryDay(Base):
    """Daily itinerary details model."""
    __tablename__ = 'itinerary_days'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='行程日主键, 自增')
    itinerary_id = Column(Integer, ForeignKey('itineraries.id'), nullable=False, comment='关联到 itineraries.id')
    day_number = Column(Integer, nullable=False, comment='当天是行程的第几天 (从1开始)')
    summary = Column(String(255), comment='当日路线概览')
    distance = Column(DECIMAL(10, 2), comment='当日预估总里程 (公里)')

    # Relationships
    itinerary = relationship("Itinerary", back_populates="days")
    pois = relationship("POI", secondary="itinerary_day_pois")

class ItineraryDayPOI(Base):
    """Daily itinerary and POI association model."""
    __tablename__ = 'itinerary_day_pois'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键, 自增')
    itinerary_day_id = Column(Integer, ForeignKey('itinerary_days.id'), nullable=False, comment='关联到 itinerary_days.id')
    poi_id = Column(Integer, ForeignKey('pois.id'), nullable=False, comment='关联到 pois.id')
    sequence = Column(Integer, nullable=False, comment='在当天行程中的顺序 (从1开始)')
    user_notes = Column(Text, comment='用户针对这一站点的个人备注')

class POI(Base):
    """Point of Interest model."""
    __tablename__ = 'pois'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='POI主键, 自增')
    type_id = Column(Integer, ForeignKey('poi_types.id'), nullable=False, comment='关联到 poi_types.id')
    name = Column(String(100), nullable=False, comment='POI名称')
    address = Column(String(255), comment='详细地址')
    latitude = Column(DECIMAL(10, 7), nullable=False, comment='纬度')
    longitude = Column(DECIMAL(10, 7), nullable=False, comment='经度')
    description = Column(Text, comment='详细介绍')
    images = Column(JSON, comment='图片URL列表')
    narrations = Column(JSON, comment='景点解说文稿')
    rating = Column(Float, comment='评分')
    opening_hours = Column(String(100), comment='营业时间')
    phone_number = Column(String(50), comment='联系电话')
    source_id = Column(String(100), comment='数据来源方的ID')
    last_sync_at = Column(DateTime, comment='最后同步时间')

    # Relationships
    poi_type = relationship("POIType")

class POIType(Base):
    """POI type dictionary model."""
    __tablename__ = 'poi_types'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='类型主键, 自增')
    type_key = Column(String(50), nullable=False, unique=True, comment='类型的英文键')
    display_name = Column(String(50), nullable=False, comment='类型的中文显示名')
    icon_name = Column(String(50), comment='对应UI的图标名称')

class StatusDefinition(Base):
    """Status definition dictionary model."""
    __tablename__ = 'status_definitions'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='状态主键, 自增')
    status_key = Column(String(50), nullable=False, comment='状态的英文键')
    scope = Column(String(50), nullable=False, comment='作用域')
    display_name = Column(String(50), nullable=False, comment='状态的中文显示名')

class Tag(Base):
    """Tag dictionary model."""
    __tablename__ = 'tags'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='标签主键, 自增')
    name = Column(String(50), nullable=False, unique=True, comment='标签名称')
    category = Column(String(50), comment='标签分类')

class UserAppSettings(Base):
    """User app settings model."""
    __tablename__ = 'user_app_settings'

    user_id = Column(Integer, primary_key=True, comment='主键, 关联到 dh_user_profile.users.id')
    narration_enabled = Column(Boolean, nullable=False, default=False, comment='景点解说功能是否开启')
    in_poi_guide_enabled = Column(Boolean, nullable=False, default=False, comment='景点内攻略推送是否开启')
    proactive_recommend_enabled = Column(Boolean, nullable=False, default=False, comment='主动推荐功能是否开启')
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

class UserMedia(Base):
    """User media library model."""
    __tablename__ = 'user_media'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='媒体文件主键, 自增')
    user_id = Column(Integer, nullable=False, comment='关联到 dh_user_profile.users.id')
    media_type = Column(Enum('IMAGE', 'VIDEO', name='media_type'), nullable=False, comment='媒体类型')
    storage_url = Column(String(255), nullable=False, comment='媒体文件在云存储上的URL')
    poi_id = Column(Integer, ForeignKey('pois.id'), comment='关联到 pois.id')
    taken_at = Column(DateTime, comment='拍摄时间')
    uploaded_at = Column(DateTime, nullable=False, default=datetime.utcnow)

    # Relationships
    poi = relationship("POI")

class UserTripStats(Base):
    """User trip statistics model."""
    __tablename__ = 'user_trip_stats'

    user_id = Column(Integer, primary_key=True, comment='主键, 关联到 dh_user_profile.users.id')
    trip_count = Column(Integer, nullable=False, default=0, comment='累计出行次数')
    city_count = Column(Integer, nullable=False, default=0, comment='累计去过的城市数量')
    total_mileage = Column(DECIMAL(12, 2), nullable=False, default=0, comment='累计总里程')
    total_days = Column(Integer, nullable=False, default=0, comment='累计出行天数')
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

class VlogMediaItem(Base):
    """Vlog and media association model."""
    __tablename__ = 'vlog_media_items'

    vlog_id = Column(Integer, ForeignKey('ai_vlogs.id'), primary_key=True, comment='关联到 ai_vlogs.id')
    media_id = Column(Integer, ForeignKey('user_media.id'), primary_key=True, comment='关联到 user_media.id')
    sequence = Column(Integer, nullable=False, comment='该素材在Vlog中的顺序')

    # Relationships
    vlog = relationship("AIVlog")
    media = relationship("UserMedia")

class AIPlanningSession(Base):
    """AI trip planning session model."""
    __tablename__ = 'ai_planning_sessions'
    __table_args__ = {'comment': 'AI行程规划会话表，记录规划任务的完整生命周期'}

    id = Column(String(36), primary_key=True, comment='主键, 建议使用UUID')
    user_id = Column(Integer, nullable=False, index=True, comment='逻辑外键, 关联到 dh_user_profile.users.id')
    status = Column(Enum('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELED', name='planning_session_status'),
                    nullable=False, default='PENDING', index=True, comment='任务状态')
    user_input = Column(JSON, nullable=False, comment='用户输入的原始规划请求 (JSON格式)')
    planning_log = Column(LONGTEXT, comment='存储AI Agent的思考过程、<think>内容和工具调用日志')
    raw_llm_output = Column(JSON, comment='大模型返回的未经处理的原始行程规划结果 (JSON格式)')
    final_itinerary_id = Column(Integer, ForeignKey('itineraries.id'), comment='逻辑外键, 用户最终保存的行程ID, 关联到 itineraries.id')
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment='任务创建时间')
    completed_at = Column(DateTime, comment='任务完成(成功/失败/取消)的时间')

    final_itinerary = relationship("Itinerary")

