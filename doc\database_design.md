# Autopilot-AI 数据库设计与演进规范

## 1. 概述

本文档旨在为 Autopilot-AI 项目提供一套完整、可演进的数据库设计方案。设计的核心理念是**松耦合、可插拔、分阶段演进**。记忆与数据存储系统将作为一个独立的**工具（Tool）**，通过抽象接口注入到 Agent 系统中，而不是与 Agent 的核心逻辑紧密耦合。这种设计使得我们可以从一个最小化的实现（MVP）开始，平滑地过渡到功能完备的生产级系统。

## 2. 核心设计原则

- **接口抽象化**: 所有的数据库操作都必须通过一个统一的 `MemoryManager` 接口进行。上层应用（如 Agent）只与该接口交互，不关心底层的具体数据库实现（Redis, MySQL, VectorDB 等）。
- **ID 解耦**: 系统中的核心关联键是 `task_uuid`（任务唯一标识）和 `user_id`（或匿名用户的 `session_id`）。这两个 ID 在任务创建之初生成，并贯穿所有存储层，不依赖任何特定数据库的自增主键。
- **可插拔工具**: 记忆系统将被封装成一个或多个符合 `autogen` 规范的工具函数（例如 `retrieve_knowledge`, `save_note` 等），并动态地提供给需要它的 Agent。这使得 Agent 的能力可以被灵活配置。

## 3. 演进阶段

我们将项目的数据存储系统分为三个主要阶段进行建设。

### 阶段一: MVP - 核心任务流转 (依赖: Redis)

- **目标**: 快速上线核心功能，保证任务的接收、状态跟踪和基本流程能够完整运行。
- **核心组件**: L1 短期工作记忆。
- **存储**: 仅使用 Redis。
- **功能**:
    - 存储和管理 Agent 执行任务过程中的实时状态 (`AgentState`)。
    - 记录任务的分解步骤和每个步骤的执行结果。

### 阶段二: 知识增强 - 情景与语义记忆 (依赖: Redis + VectorDB)

- **目标**: 赋予 Agent 外部知识和长期对话记忆，使其能够执行需要背景知识的复杂任务。
- **核心组件**: L2 情景记忆和语义记忆。
- **存储**: Redis + 向量数据库 (如 ChromaDB, Qdrant)。
- **功能**:
    - 通过 RAG (Agentic RAG) 模式，从知识库中检索信息以增强上下文。
    - 记忆历史对话，提供连续的、个性化的交互体验。

### 阶段三: 个性化与持久化 - 用户画像 (依赖: Redis + VectorDB + MySQL)

- **目标**: 引入结构化的长期存储，实现用户画像、任务持久化归档和数据分析。
- **核心组件**: L3 长期记忆。
- **存储**: Redis + VectorDB + 关系型数据库 (如 MySQL/PostgreSQL)。
- **功能**:
    - 存储和管理结构化的用户数据（偏好、历史等）。
    - 对已完成的任务进行持久化归档，用于审计和分析。
    - 为 Agent 提供用户画像信息，以实现更深度的个性化。

## 4. 数据模型设计

### 4.1. 阶段一: Redis 数据结构 (L1 记忆)

Redis 用于存储任务执行中的所有动态数据。我们使用 `task_uuid`作为 key 的核心部分。

#### 1. 任务主状态 (`agent_state`)
用于存储任务的全局信息和当前快照。

- **Key**: `agent_state:<task_uuid>`
- **Type**: `HASH`
- **Fields**:
    - `status`: 任务的整体状态 (e.g., `pending`, `running`, `completed`, `failed`)。
    - `user_id`: 关联的用户 ID。
    - `original_prompt`: 用户的原始请求。
    - `current_step_id`: 当前正在执行的步骤 ID。
    - `state_json`: 一个 JSON 字符串，存储 `autogen` 中 `AgentState` Pydantic 模型的完整内容，包括消息历史、上下文等。
    - `created_at`: 任务创建时间戳。
    - `updated_at`: 状态最后更新时间戳。

#### 2. 任务分解步骤 (`task_steps`)
用于记录任务从规划到执行的每一步，这是实现任务过程可视化和调试的关键。

- **Key**: `task_steps:<task_uuid>`
- **Type**: `LIST`
- **Value**: 每个元素是一个 JSON 字符串，代表一个独立的任务步骤。

**步骤 (Step) JSON 结构示例**:
```json
{
  "step_id": "step_uuid_123",
  "parent_step_id": "step_uuid_parent", 
  "agent_name": "PlannerAgent",
  "tool_name": "N/A",
  "tool_input": "N/A",
  "status": "completed",
  "input": "{\"prompt\": \"Find the weather in Shanghai\"}",
  "output": "{\"plan\": [\"call_weather_api\", \"format_result\"]}",
  "timestamp": "2023-10-27T10:00:00Z",
  "is_human_intervention": false
}
```
- **操作**: 当一个新步骤产生时，使用 `RPUSH` 将其追加到列表末尾。使用 `LRANGE` 可以获取完整的步骤历史。

#### 4.1.1. 阶段一 MemoryManager 接口职责
在阶段一，`MemoryManager` 的实现 (`RedisMemoryManager`) 将专注于与 Redis 交互，提供对任务状态和步骤的原子操作。其核心职责包括：
- **创建与初始化**: 在任务开始时，创建 `agent_state` HASH 和空的 `task_steps` LIST。
- **读取**: 提供获取任务当前完整状态 (`AgentState`) 和所有历史步骤的方法。
- **更新**:
    - 更新 HASH 中的特定字段，如 `status` 和 `current_step_id`。
    - 将 `AgentState` 的最新 JSON 快照写入 `state_json` 字段。
- **记录**: 将新的步骤数据（JSON）原子地追加到 `task_steps` 列表中。

### 4.2. 阶段二: 向量数据库 (L2 记忆)

- **集合 (Collection) 1: `semantic_knowledge`**
    - **用途**: 存储外部文档、知识库等。
    - **元数据 (Metadata)**: `source`, `document_id`, `chunk_id`, `tags`
- **集合 (Collection) 2: `episodic_memory`**
    - **用途**: 存储用户与 Agent 的对话历史。
    - **元数据 (Metadata)**: `user_id`, `session_id`, `timestamp`, `agent_name`

### 4.3. 阶段三: 关系型数据库 (L3 记忆)

- **表 1: `users`**
    - `user_id` (PK, VARCHAR)
    - `preferences` (JSON)
    - `created_at` (DATETIME)
- **表 2: `tasks`**
    - `task_uuid` (PK, VARCHAR)
    - `user_id` (FK, VARCHAR)
    - `status` (VARCHAR)
    - `original_prompt` (TEXT)
    - `final_result` (TEXT)
    - `started_at` (DATETIME)
    - `completed_at` (DATETIME)

## 5. Agent 集成方案

Agent 的集成同样遵循分阶段演进的策略。

### 5.1. 阶段一的 Agent 集成

在阶段一，我们只关注将 Redis 中的短期记忆（上下文）管理功能注入 Agent。此时，Agent 并**不感知**任何外部知识库。它能做的就是记录自己的工作，并在需要时读取当前任务的状态。

**`MemoryTool` (阶段一版本):**
该工具类封装了对当前任务状态的读写操作。

**概念代码示例:**
```python
class MemoryTool:
    """
    一个封装了阶段一 Redis 记忆操作的工具。
    在 Agent 初始化时，会为每个任务实例化一个 MemoryTool。
    """
    def __init__(self, memory_manager: "RedisMemoryManager", task_id: str):
        self.manager = memory_manager
        self.task_id = task_id

    def record_step(self, step_data: dict) -> str:
        """
        记录一个任务分解步骤的结果。
        Agent (特别是 Planner或Executor) 在完成一步操作后，应调用此工具。
        
        Args:
            step_data: 一个包含步骤详情的字典，结构需符合设计文档。
        
        Returns:
            A confirmation message.
        """
        # 调用 MemoryManager 的底层方法
        self.manager.add_task_step(self.task_id, step_data)
        return f"Step recorded successfully for task {self.task_id}."

    def update_status(self, status: str) -> str:
        """
        更新任务的整体状态。
        
        Args:
            status: 新的任务状态 (e.g., 'running', 'failed').
        
        Returns:
            A confirmation message.
        """
        self.manager.update_task_status(self.task_id, status)
        return f"Task status updated to {status}."

    def get_full_history(self) -> dict:
        """
        获取当前任务的完整状态和所有步骤历史。
        这对于需要回顾之前步骤的 Agent 可能有用。
        
        Returns:
            一个包含任务状态和所有步骤历史的字典。
        """
        state = self.manager.get_task_state(self.task_id)
        steps = self.manager.get_task_steps(self.task_id)
        return {"state": state, "steps": steps}

# 在 Agent 构建时注入工具:
# memory_tool = MemoryTool(redis_manager, current_task_id)
# executor_agent.register_function(
#     function_map={
#         "record_step": memory_tool.record_step,
#         "update_status": memory_tool.update_status,
#     }
# )
```

**与 Agent 的交互:**

- **Planner Agent**: 在生成初始计划后，可以将整个计划的步骤通过 `record_step` 逐一记录。
- **Executor Agent**: 在执行完一个工具调用或一步操作后，调用 `record_step` 来记录输入、输出和结果状态。
- **GroupChat Manager**: 在任务完成或失败时，调用 `update_status` 来更新最终状态。

通过这种方式，我们确保了在第一阶段，Agent 的记忆操作仅限于其自身任务的生命周期内，实现了您所要求的"保存上下文到 Redis"以及对其的"读取、记录和更新"。后续阶段的记忆能力（如 `retrieve_knowledge`）将作为新的工具在未来被注入，而无需改动现有 Agent 的核心逻辑。 