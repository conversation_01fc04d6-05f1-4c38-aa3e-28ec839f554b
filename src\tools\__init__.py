"""
AutoPilot AI 工具系统

提供原子化的Function Call工具注册表和管理系统，支持与AutoGen框架的无缝集成。
"""
from typing import Dict, List, Callable, Any
from autogen_core.tools import FunctionTool
import inspect


class ToolRegistry:
    """工具注册表 - 统一管理所有原子化工具"""
    
    def __init__(self):
        self._tools: Dict[str, Callable] = {}
        self._schemas: Dict[str, Dict[str, Any]] = {}

    def register(self, func: Callable = None, *, schema: Dict[str, Any] = None) -> Callable:
        """
        工具注册装饰器
        
        Args:
            func: 要注册的函数
            schema: 可选的JSON Schema定义
            
        Returns:
            注册后的函数
        """
        def decorator(f: Callable) -> Callable:
            name = f.__name__
            if name in self._tools:
                raise ValueError(f"Tool with name '{name}' already registered.")
            
            self._tools[name] = f
            if schema:
                self._schemas[name] = schema
                
            return f
            
        if func is None:
            # 用作带参数的装饰器: @register_tool(schema=...)
            return decorator
        else:
            # 用作简单装饰器: @register_tool
            return decorator(func)

    def get_tool(self, name: str) -> FunctionTool:
        """按名称获取单个AutoGen FunctionTool"""
        if name not in self._tools:
            raise ValueError(f"Tool '{name}' not found.")
        return FunctionTool.from_function(self._tools[name])

    def get_tools(self, names: List[str]) -> List[FunctionTool]:
        """按名称列表获取多个AutoGen FunctionTool"""
        return [self.get_tool(name) for name in names]
        
    def get_schema(self, name: str) -> Dict[str, Any]:
        """获取工具的JSON Schema"""
        return self._schemas.get(name, {})
        
    def list_tools(self) -> List[str]:
        """列出所有已注册的工具名称"""
        return list(self._tools.keys())


# 创建全局单例
tool_registry = ToolRegistry()

# 方便导入的别名
register_tool = tool_registry.register 