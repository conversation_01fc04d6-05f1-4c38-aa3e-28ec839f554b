/* AutoPilot AI 旅行规划 - 样式文件 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

.container-fluid {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 8px;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0 !important;
    padding: 1.25rem;
}

.card-title {
    color: #495057;
    font-weight: 600;
}

.card-title i {
    margin-right: 8px;
    color: #007bff;
}

/* 用户画像样式 */
.user-profile-content {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
}

.profile-section {
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.profile-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.profile-section strong {
    color: #495057;
    font-weight: 600;
    display: inline-block;
    min-width: 80px;
}

.profile-section span {
    color: #6c757d;
    margin-left: 10px;
}

/* 规划面板样式 */
.planning-panel {
    padding-right: 10px;
}

.planning-panel .card {
    min-height: calc(100vh - 140px);
}

/* 结果面板样式 */
.result-panel {
    padding-left: 10px;
}

.result-panel .card {
    min-height: calc(100vh - 140px);
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 思考过程样式 */
.thinking-steps {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.thinking-step {
    padding: 8px 12px;
    margin-bottom: 8px;
    background-color: #fff;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    font-size: 0.9rem;
    animation: fadeInUp 0.3s ease;
}

.thinking-step.category-travel-object {
    border-left-color: #28a745;
}

.thinking-step.category-travel-time {
    border-left-color: #ffc107;
}

.thinking-step.category-attraction {
    border-left-color: #dc3545;
}

.thinking-step.category-food {
    border-left-color: #fd7e14;
}

.thinking-step.category-accommodation {
    border-left-color: #6f42c1;
}

/* 统计卡片样式 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 每日行程样式 */
.daily-plan {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.daily-plan-header {
    display: flex;
    justify-content-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.day-number {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.day-theme {
    flex: 1;
    margin-left: 15px;
}

.day-theme h5 {
    margin: 0;
    color: #495057;
}

.day-theme p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* POI卡片样式 */
.poi-card {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    position: relative;
}

.poi-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.poi-image {
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
    max-height: 200px;
}

.poi-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.poi-image img:hover {
    transform: scale(1.05);
}

.poi-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.poi-info {
    flex: 1;
}

.poi-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.poi-category {
    display: inline-block;
    padding: 2px 8px;
    background-color: #e9ecef;
    color: #495057;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.poi-category.attraction {
    background-color: #d4edda;
    color: #155724;
}

.poi-category.food {
    background-color: #fff3cd;
    color: #856404;
}

.poi-category.hotel {
    background-color: #d1ecf1;
    color: #0c5460;
}

.poi-actions {
    display: flex;
    gap: 5px;
}

.poi-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background-color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .planning-panel,
    .result-panel {
        padding: 0;
        margin-bottom: 20px;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .stat-icon {
        font-size: 1.5rem;
        margin-right: 10px;
    }
    
    .stat-value {
        font-size: 1.4rem;
    }
    
    .daily-plan-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .day-number {
        margin-bottom: 10px;
    }
    
    .day-theme {
        margin-left: 0;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.8rem;
}

/* 按钮组样式 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: 8px 0 0 8px;
}

.btn-group .btn:last-child {
    border-radius: 0 8px 8px 0;
}

/* 滚动条样式 */
.thinking-steps::-webkit-scrollbar {
    width: 6px;
}

.thinking-steps::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.thinking-steps::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.thinking-steps::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ==================== V3重构版新增样式 ==================== */

/* 车辆信息面板 */
.vehicle-info-section {
    margin-bottom: 1rem;
}

.vehicle-info-panel {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

/* 策略确认区域 */
.strategy-confirmation {
    margin-bottom: 1rem;
}

.strategy-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    animation: slideIn 0.3s ease;
}

.strategy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.strategy-content {
    margin: 1rem 0;
}

.strategy-summary {
    margin-bottom: 1rem;
}

.strategy-options {
    margin: 1rem 0;
}

.strategy-option {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    transition: all 0.2s ease;
}

.strategy-option:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.strategy-option.recommended {
    border-color: #28a745;
    background: #f8fff9;
}

.strategy-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* 分析步骤增强 */
.analysis-step {
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: white;
    transition: all 0.3s ease;
    animation: fadeInUp 0.5s ease;
}

.analysis-step.step-running {
    border-color: #007bff;
    background: #f8f9ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.analysis-step.step-completed {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
}

.analysis-step.step-error {
    border-color: #dc3545;
    background: #fff8f8;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.step-title {
    margin: 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: #495057;
}

.step-status {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
}

.step-status.text-muted {
    background: #f8f9fa;
}

.step-status.text-primary {
    background: #e3f2fd;
    color: #1976d2;
}

.step-status.text-success {
    background: #e8f5e8;
    color: #2e7d32;
}

.step-status.text-danger {
    background: #ffebee;
    color: #c62828;
}

.step-result {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.result-content {
    font-size: 0.85rem;
}

.result-tags {
    margin-bottom: 0.5rem;
}

.result-tags .badge {
    font-size: 0.75rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.driving-info .info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
    color: #495057;
}

.preference-tags .badge {
    font-size: 0.75rem;
}

.confidence-score {
    margin-top: 0.25rem;
    font-size: 0.75rem;
}

/* 语音反馈状态 */
.voice-feedback-status {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 0.75rem;
    animation: slideIn 0.3s ease;
}

.voice-indicator {
    animation: pulse 1.5s infinite;
}

.voice-content {
    flex: 1;
}

.voice-text {
    font-size: 0.85rem;
    color: #1976d2;
    font-weight: 500;
    line-height: 1.4;
}

/* TTS播报状态指示器增强 */
.tts-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 12px;
    z-index: 1000;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
    min-width: 300px;
    animation: slideInRight 0.3s ease;
}

.tts-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.tts-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tts-icon {
    font-size: 1.5em;
    color: #4CAF50;
}

.tts-wave {
    position: absolute;
    display: flex;
    gap: 2px;
    margin-left: 25px;
}

.tts-wave span {
    width: 3px;
    height: 15px;
    background: #4CAF50;
    border-radius: 2px;
    animation: wave 1.2s infinite ease-in-out;
}

.tts-wave span:nth-child(2) {
    animation-delay: 0.1s;
}

.tts-wave span:nth-child(3) {
    animation-delay: 0.2s;
}

.tts-info {
    flex: 1;
}

.tts-title {
    font-size: 0.9em;
    font-weight: 600;
    margin-bottom: 5px;
    color: #4CAF50;
}

.tts-text {
    font-size: 0.85em;
    line-height: 1.4;
    margin-bottom: 8px;
    max-width: 200px;
}

.tts-progress .progress {
    height: 3px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

.tts-progress .progress-bar {
    background: #4CAF50;
    transition: width 0.3s ease;
}

.tts-controls {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.tts-controls .btn {
    padding: 4px 8px;
    font-size: 0.8em;
    border-radius: 4px;
}

/* 路线视图 */
.route-view {
    padding: 1rem;
}

.route-overview,
.charging-plan,
.parking-suggestions {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: box-shadow 0.2s ease;
}

.route-overview:hover,
.charging-plan:hover,
.parking-suggestions:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.route-overview h6,
.charging-plan h6,
.parking-suggestions h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 统计卡片增强 */
.itinerary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.stat-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #007bff;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* 面板控制增强 */
.panel-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-controls .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes wave {
    0%, 40%, 100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.spin {
    animation: spin 1s linear infinite;
}

/* 规划进度样式 */
.planning-progress {
    padding: 2rem 1rem;
}

.planning-steps {
    margin-top: 2rem;
}

.planning-step {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.planning-step:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.planning-step .step-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
    font-size: 1.2rem;
}

.planning-step .step-content {
    flex: 1;
}

.planning-step .step-content h6 {
    margin: 0 0 0.5rem 0;
    color: #495057;
    font-weight: 600;
}

.planning-step .step-content p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 立即规划按钮增强 */
#startPlanningBtn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
    display: block !important; /* 强制显示，用于调试 */
}

#startPlanningBtn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

#startPlanningBtn:active {
    transform: translateY(0);
}

/* 控制按钮区域确保可见 */
.analysis-controls {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    min-height: 60px;
}

/* 分析完成状态 */
.analysis-complete {
    background: linear-gradient(135deg, #e8f5e8, #f0fff0);
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
    animation: completePulse 2s ease-in-out;
}

.analysis-complete h5 {
    color: #28a745;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.analysis-complete p {
    color: #155724;
    margin-bottom: 1rem;
}

@keyframes completePulse {
    0% {
        transform: scale(0.95);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.02);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .vehicle-info-panel {
        padding: 0.75rem;
    }

    .strategy-card {
        padding: 0.75rem;
    }

    .analysis-step {
        padding: 0.75rem;
    }

    .tts-indicator {
        bottom: 10px;
        right: 10px;
        min-width: 250px;
        padding: 15px;
    }

    .itinerary-stats {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 0.75rem;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .panel-controls {
        flex-wrap: wrap;
    }

    .planning-progress {
        padding: 1rem 0.5rem;
    }

    .planning-step {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .planning-step .step-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    #startPlanningBtn {
        font-size: 1rem;
        padding: 0.6rem 1.2rem;
    }
}
