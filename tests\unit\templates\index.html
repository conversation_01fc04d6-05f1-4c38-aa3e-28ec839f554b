<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .endpoint-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
        }
        .result-container {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 0.25rem;
            font-family: monospace;
        }
        .loading {
            opacity: 0.5;
            pointer-events: none;
        }
        .nav-pills .nav-link {
            margin-right: 0.5rem;
        }
        .response-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .method-badge {
            min-width: 60px;
        }
        .url-badge {
            background: #e9ecef;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1 class="mb-4">API Test Interface</h1>

        <div class="row">
            <!-- Left sidebar with navigation -->
            <div class="col-md-3">
                <div class="sticky-top" style="top: 1rem;">
                    <div class="nav flex-column nav-pills" id="api-tabs" role="tablist">
                        <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#map-api">Map API</button>
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#music-api">Music API</button>
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#agent-api">Agent API</button>
                    </div>
                </div>
            </div>

            <!-- Main content area -->
            <div class="col-md-9">
                <div class="tab-content">
                    <!-- Map API Section -->
                    <div class="tab-pane fade show active" id="map-api">
                        <h2>Map API</h2>
                        
                        <!-- Search POIs -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/map/search_pois</span>
                            </h4>
                            <form id="searchPoisForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="keywords" placeholder="Keywords" required>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="city" placeholder="City">
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="type" placeholder="Type">
                                    </div>
                                    <div class="col-md-4">
                                        <input type="number" class="form-control" name="latitude" placeholder="Latitude">
                                    </div>
                                    <div class="col-md-4">
                                        <input type="number" class="form-control" name="longitude" placeholder="Longitude">
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search POIs</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchPoisResult" class="result-container"></div>
                        </div>

                        <!-- Get Route -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/map/get_route</span>
                            </h4>
                            <form id="getRouteForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <h5>Origin</h5>
                                        <input type="number" step="any" class="form-control" name="originLat" placeholder="Origin Latitude" required>
                                        <input type="number" step="any" class="form-control mt-2" name="originLng" placeholder="Origin Longitude" required>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Destination</h5>
                                        <input type="number" step="any" class="form-control" name="destLat" placeholder="Destination Latitude" required>
                                        <input type="number" step="any" class="form-control mt-2" name="destLng" placeholder="Destination Longitude" required>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" name="transportMode">
                                            <option value="driving">Driving</option>
                                            <option value="walking">Walking</option>
                                            <option value="bicycling">Bicycling</option>
                                            <option value="transit">Transit</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get Route</button>
                                    </div>
                                </div>
                            </form>
                            <div id="getRouteResult" class="result-container"></div>
                        </div>
                    </div>

                    <!-- Music API Section -->
                    <div class="tab-pane fade" id="music-api">
                        <h2>Music API</h2>

                        <!-- Get Keyword -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/get_keyword</span>
                            </h4>
                            <form id="getKeywordForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <textarea class="form-control" name="prompt" rows="3" 
                                            placeholder="Enter your music description..." required></textarea>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get Keyword</button>
                                    </div>
                                </div>
                            </form>
                            <div id="getKeywordResult" class="result-container"></div>
                        </div>

                        <!-- Search Songs -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/search_songs</span>
                            </h4>
                            <form id="searchSongsForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" name="keyword" placeholder="Enter keywords" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search Songs</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchSongsResult" class="result-container"></div>
                        </div>

                        <!-- Search Songs by Artist -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/search_songs_by_artist</span>
                            </h4>
                            <form id="searchSongsByArtistForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" name="keyword" placeholder="Enter artist name" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchSongsByArtistResult" class="result-container"></div>
                        </div>

                        <!-- Search Songs by Artist and Name -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/search_songs_by_artist_and_name</span>
                            </h4>
                            <form id="searchSongsByArtistAndNameForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="artistName" placeholder="Artist name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="songName" placeholder="Song name" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchSongsByArtistAndNameResult" class="result-container"></div>
                        </div>

                        <!-- Search Playlists -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/search_playlists</span>
                            </h4>
                            <form id="searchPlaylistsForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" name="keyword" placeholder="Enter keywords" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search Playlists</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchPlaylistsResult" class="result-container"></div>
                        </div>

                        <!-- Search Albums -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/search_albums</span>
                            </h4>
                            <form id="searchAlbumsForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" name="keyword" placeholder="Enter keywords" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search Albums</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchAlbumsResult" class="result-container"></div>
                        </div>

                        <!-- Search Albums by Artist -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/search_albums_by_artist</span>
                            </h4>
                            <form id="searchAlbumsByArtistForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="artistName" placeholder="Artist name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="albumName" placeholder="Album name" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchAlbumsByArtistResult" class="result-container"></div>
                        </div>

                        <!-- Search Artists -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/search_artists</span>
                            </h4>
                            <form id="searchArtistsForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" name="keyword" placeholder="Enter artist name" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Search Artists</button>
                                    </div>
                                </div>
                            </form>
                            <div id="searchArtistsResult" class="result-container"></div>
                        </div>

                        <!-- Hot Keywords -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-primary method-badge">GET</span>
                                <span class="url-badge">/api/music/hot_keywords</span>
                            </h4>
                            <button id="getHotKeywordsBtn" class="btn btn-primary mt-3">Get Hot Keywords</button>
                            <div id="hotKeywordsResult" class="result-container"></div>
                        </div>

                        <!-- Suggest Keywords -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/suggest_keywords</span>
                            </h4>
                            <form id="suggestKeywordsForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" name="keyword" placeholder="Enter keyword" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get Suggestions</button>
                                    </div>
                                </div>
                            </form>
                            <div id="suggestKeywordsResult" class="result-container"></div>
                        </div>

                        <!-- Hi-Res -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/hi_res</span>
                            </h4>
                            <form id="hiResForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <select class="form-select" name="resourceTypes" multiple required>
                                            <option value="song">Songs</option>
                                            <option value="playlist">Playlists</option>
                                            <option value="album">Albums</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="bitrate" placeholder="Bitrate" value="320">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" name="withUrl">
                                            <option value="true">With URL</option>
                                            <option value="false">Without URL</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get Hi-Res Content</button>
                                    </div>
                                </div>
                            </form>
                            <div id="hiResResult" class="result-container"></div>
                        </div>
                    </div>

                    <!-- Agent API Section -->
                    <div class="tab-pane fade" id="agent-api">
                        <h2>Agent API</h2>

                        <!-- Test Music Agent -->
                        <div class="endpoint-section">
                            <h4>
                                <span class="badge bg-success method-badge">POST</span>
                                <span class="url-badge">/api/music/test_agent</span>
                            </h4>
                            <form id="testAgentForm" class="mt-3">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <textarea class="form-control" name="prompt" rows="3" 
                                            placeholder="Enter your prompt (e.g., '帮我推荐几首适合跑步的英文歌')" required></textarea>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Test Agent</button>
                                    </div>
                                </div>
                            </form>
                            <div id="testAgentResult" class="result-container"></div>
                        </div>
                    </div>
                </div>

                <!-- Additional Test Modules -->
                <div class="mt-4">
                    <!-- Song Detail Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Song Detail Test</h5>
                        </div>
                        <div class="card-body">
                            <form id="songDetailForm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="songId" placeholder="Song ID" required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="bitrate" placeholder="Bitrate">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" name="effects">
                                            <option value="">No Effects</option>
                                            <option value="eac3">Dolby Digital Plus</option>
                                            <option value="ac4">Dolby AC-4</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="withUrl" checked>
                                            <label class="form-check-label">Include URL</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="qualityFlag">
                                            <label class="form-check-label">Quality Flag</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="languageFlag">
                                            <label class="form-check-label">Language Flag</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get Song Detail</button>
                                    </div>
                                </div>
                            </form>
                            <div id="songDetailResult" class="result-container mt-3"></div>
                        </div>
                    </div>

                    <!-- Playlist Songs Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Get Playlist Songs</h5>
                        </div>
                        <div class="card-body">
                            <form id="playlistSongsForm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="playlistId" placeholder="Playlist ID" required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="limit" placeholder="Limit" value="5">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="offset" placeholder="Offset" value="0">
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get Songs</button>
                                    </div>
                                </div>
                            </form>
                            <div id="playlistSongsResult" class="result-container mt-3"></div>
                        </div>
                    </div>

                    <!-- Album Free/VIP Songs Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Get Album Songs</h5>
                        </div>
                        <div class="card-body">
                            <form id="albumSongsForm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="albumId" placeholder="Album ID" required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="limit" placeholder="Limit" value="5">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="offset" placeholder="Offset" value="0">
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" name="type">
                                            <option value="free">Free Songs</option>
                                            <option value="vip">VIP Songs</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get Album Songs</button>
                                    </div>
                                </div>
                            </form>
                            <div id="albumSongsResult" class="result-container mt-3"></div>
                        </div>
                    </div>

                    <!-- Album VIP Songs Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Get Album VIP Songs</h5>
                        </div>
                        <div class="card-body">
                            <form id="albumVipSongsForm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="albumId" placeholder="Album ID" required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="limit" placeholder="Limit" value="5">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="offset" placeholder="Offset" value="0">
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get VIP Songs</button>
                                    </div>
                                </div>
                            </form>
                            <div id="albumVipSongsResult" class="result-container mt-3"></div>
                        </div>
                    </div>

                    <!-- Additional Test Modules -->
                    <div class="mt-4">
                    <!-- Batch Song URLs Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Get Batch Song URLs</h5>
                        </div>
                        <div class="card-body">
                            <form id="batchSongUrlsForm">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <textarea class="form-control" name="songIds" placeholder="Enter song IDs (one per line)" rows="3" required></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" name="bitrate">
                                            <option value="320">320kbps (Extreme)</option>
                                            <option value="192">192kbps (Higher)</option>
                                            <option value="128">128kbps (Standard)</option>
                                            <option value="999">999kbps (Lossless)</option>
                                            <option value="1999">1999kbps (Hi-Res)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" name="effects">
                                            <option value="">No Effects</option>
                                            <option value="eac3">Dolby Digital Plus</option>
                                            <option value="ac4">Dolby AC-4</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Get URLs</button>
                                    </div>
                                </div>
                            </form>
                            <div id="batchUrlsResult" class="result-container mt-3"></div>
                        </div>
                    </div>

                    <!-- Song Like/Unlike Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Like/Unlike Song</h5>
                        </div>
                        <div class="card-body">
                            <form id="likeSongForm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="songId" placeholder="Song ID" required>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" name="isLike">
                                            <option value="true">Like</option>
                                            <option value="false">Unlike</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">Submit</button>
                                    </div>
                                </div>
                            </form>
                            <div id="likeSongResult" class="result-container mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Helper function for API calls
        async function callApi(url, formData, method = 'POST') {
            const resultsContainer = document.getElementById(formData.get('resultId'));
            
            try {
                resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>Processing...</div></div>';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(Object.fromEntries(formData.entries()))
                });
                
                const result = await response.json();
                resultsContainer.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                resultsContainer.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
            }
        }

        // Map API Form Handlers
        document.getElementById('searchPoisForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchPoisResult');
            await callApi('/api/map/search_pois', formData);
        });

        document.getElementById('getRouteForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'getRouteResult');
            await callApi('/api/map/get_route', formData);
        });

        // Music API Form Handlers
        document.getElementById('getKeywordForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'getKeywordResult');
            await callApi('/api/music/get_keyword', formData);
        });

        document.getElementById('searchSongsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchSongsResult');
            await callApi('/api/music/search_songs', formData);
        });

        document.getElementById('searchSongsByArtistForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchSongsByArtistResult');
            await callApi('/api/music/search_songs_by_artist', formData);
        });

        document.getElementById('searchSongsByArtistAndNameForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchSongsByArtistAndNameResult');
            await callApi('/api/music/search_songs_by_artist_and_name', formData);
        });

        document.getElementById('searchPlaylistsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchPlaylistsResult');
            await callApi('/api/music/search_playlists', formData);
        });

        document.getElementById('searchAlbumsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchAlbumsResult');
            await callApi('/api/music/search_albums', formData);
        });

        document.getElementById('searchAlbumsByArtistForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchAlbumsByArtistResult');
            await callApi('/api/music/search_albums_by_artist', formData);
        });

        document.getElementById('searchArtistsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'searchArtistsResult');
            await callApi('/api/music/search_artists', formData);
        });

        document.getElementById('getHotKeywordsBtn').addEventListener('click', async () => {
            const resultsContainer = document.getElementById('hotKeywordsResult');
            
            try {
                resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>Loading...</div></div>';
                
                const response = await fetch('/api/music/hot_keywords');
                const result = await response.json();
                
                resultsContainer.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                resultsContainer.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
            }
        });

        // Suggest Keywords
        document.getElementById('suggestKeywordsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'suggestKeywordsResult');
            await callApi('/api/music/suggest_keywords', formData);
        });

        // Hi-Res
        document.getElementById('hiResForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const resourceTypes = Array.from(formData.getAll('resourceTypes'));
            formData.set('resultId', 'hiResResult');
            formData.set('resourceTypes', JSON.stringify(resourceTypes));
            await callApi('/api/music/hi_res', formData);
        });

        // Agent API Form Handlers
        document.getElementById('testAgentForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'testAgentResult');
            await callApi('/api/music/test_agent', formData);
        });

        // Additional Test Modules
        // Song Detail
        document.getElementById('songDetailForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'songDetailResult');
            await callApi('/api/music/song_detail', formData);
        });

        // Playlist Songs
        document.getElementById('playlistSongsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'playlistSongsResult');
            await callApi('/api/music/playlist/songs', formData);
        });

        // Album Songs
        document.getElementById('albumSongsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'albumSongsResult');
            const type = formData.get('type');
            const endpoint = type === 'free' ? '/api/music/album/free_songs' : '/api/music/album/vip_songs';
            await callApi(endpoint, formData);
        });

        // Album VIP Songs
        document.getElementById('albumVipSongsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'albumVipSongsResult');
            await callApi('/api/music/album/vip_songs', formData);
        });

        // Batch Song URLs
        document.getElementById('batchSongUrlsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'batchUrlsResult');
            // Convert newline-separated song IDs to array
            const songIds = formData.get('songIds').split('\n').map(id => id.trim()).filter(id => id);
            formData.set('songIds', JSON.stringify(songIds));
            await callApi('/api/music/batch/song/urls', formData);
        });

        // Like/Unlike Song
        document.getElementById('likeSongForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            formData.set('resultId', 'likeSongResult');
            formData.set('isLike', formData.get('isLike') === 'true');
            await callApi('/api/music/song/like', formData);
        });
    </script>
</body>
</html>
