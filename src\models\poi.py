"""
POI数据模型定义

定义了旅游规划中使用的POI（兴趣点）数据结构，包括基础POI和丰富POI信息。
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from enum import Enum


class POIType(str, Enum):
    """POI类型枚举"""
    ATTRACTION = "attraction"  # 景点
    RESTAURANT = "restaurant"  # 餐厅
    HOTEL = "hotel"  # 酒店
    SHOPPING = "shopping"  # 购物
    ENTERTAINMENT = "entertainment"  # 娱乐
    TRANSPORT = "transport"  # 交通


class BasicPOI(BaseModel):
    """
    基础POI数据模型，包含最基本的POI信息
    """
    poi_id: str = Field(..., description="POI的唯一标识符")
    name: str = Field(..., description="POI的名称")
    poi_type: POIType = Field(..., description="POI类型")
    location: str = Field(..., description="经纬度坐标，格式为 'lng,lat'")
    address: Optional[str] = Field(None, description="详细地址")
    
    class Config:
        use_enum_values = True


class EnrichedPOI(BaseModel):
    """
    信息丰富的POI数据模型，用于在行程中展示详细信息
    """
    poi_id: str = Field(..., description="POI的唯一标识符")
    name: str = Field(..., description="POI的名称")
    poi_type: POIType = Field(..., description="POI类型")
    location: str = Field(..., description="经纬度坐标，格式为 'lng,lat'")
    address: Optional[str] = Field(None, description="详细地址")
    
    # 丰富信息字段
    introduction: str = Field(..., description="一句话亮点介绍，由AI生成，突出其核心特色")
    suggested_time: str = Field(..., description="建议的游玩或用餐时间段，例如 '上午 9:00 - 12:00' 或 '午餐 12:00 - 13:30'")
    rating: Optional[float] = Field(None, description="用户评分 (范围1-5)", ge=1.0, le=5.0)
    phone_number: Optional[str] = Field(None, description="联系电话 (主要针对酒店、餐厅)")
    image_urls: List[str] = Field(default_factory=list, description="POI的展示图片URL列表")
    
    # 扩展信息
    price_level: Optional[int] = Field(None, description="价格等级 (1-4, 1最便宜)", ge=1, le=4)
    opening_hours: Optional[str] = Field(None, description="营业时间")
    tags: List[str] = Field(default_factory=list, description="标签列表，如['历史文化', '必游景点']")
    
    # 规划相关信息
    visit_duration: Optional[int] = Field(None, description="建议游玩时长（分钟）")
    best_visit_time: Optional[str] = Field(None, description="最佳游玩时间，如'上午'、'下午'、'傍晚'")
    
    class Config:
        use_enum_values = True


class DailyPlan(BaseModel):
    """
    每日行程计划
    """
    day: int = Field(..., description="天数（第几天）")
    theme: str = Field(..., description="当天的主题或总结")
    activities: List[EnrichedPOI] = Field(default_factory=list, description="当天的活动列表")
    total_duration: Optional[int] = Field(None, description="当天总时长（分钟）")
    total_distance: Optional[float] = Field(None, description="当天总距离（公里）")
    estimated_cost: Optional[float] = Field(None, description="当天预估费用（元）")


class TravelItinerary(BaseModel):
    """
    完整的旅行行程
    """
    city: str = Field(..., description="目标城市")
    total_days: int = Field(..., description="总天数")
    daily_plans: List[DailyPlan] = Field(..., description="每日行程计划")
    
    # 行程统计信息
    total_pois: int = Field(0, description="总POI数量")
    total_distance: Optional[float] = Field(None, description="总距离（公里）")
    total_cost: Optional[float] = Field(None, description="总费用（元）")
    
    # 元数据
    created_at: Optional[str] = Field(None, description="创建时间")
    user_id: Optional[str] = Field(None, description="用户ID")
    preferences: Optional[Dict[str, Any]] = Field(None, description="用户偏好")


class RouteInfo(BaseModel):
    """
    路线信息
    """
    origin: str = Field(..., description="起点")
    destination: str = Field(..., description="终点")
    distance: int = Field(..., description="距离（米）")
    duration: int = Field(..., description="时长（秒）")
    transport_mode: str = Field(default="walking", description="交通方式")
    route_points: List[str] = Field(default_factory=list, description="路线关键点")


class POISearchResult(BaseModel):
    """
    POI搜索结果
    """
    query: str = Field(..., description="搜索关键词")
    city: str = Field(..., description="搜索城市")
    total_count: int = Field(0, description="总结果数")
    pois: List[BasicPOI] = Field(default_factory=list, description="POI列表")
    search_time: Optional[float] = Field(None, description="搜索耗时（秒）")


def create_sample_enriched_poi(poi_type: POIType, name: str, location: str) -> EnrichedPOI:
    """
    创建示例EnrichedPOI对象，用于测试和演示
    
    Args:
        poi_type: POI类型
        name: POI名称
        location: 经纬度坐标
        
    Returns:
        EnrichedPOI对象
    """
    poi_id = f"{poi_type.value}_{hash(name) % 10000}"
    
    # 根据类型生成不同的示例数据
    if poi_type == POIType.ATTRACTION:
        introduction = f"{name}是一处著名的历史文化景点，值得深度游览"
        suggested_time = "上午 9:00 - 12:00"
        visit_duration = 180
        best_visit_time = "上午"
        tags = ["历史文化", "必游景点"]
    elif poi_type == POIType.RESTAURANT:
        introduction = f"{name}提供地道的本地美食，口味正宗"
        suggested_time = "午餐 12:00 - 13:30"
        visit_duration = 90
        best_visit_time = "午餐时间"
        tags = ["本地美食", "人气餐厅"]
    elif poi_type == POIType.HOTEL:
        introduction = f"{name}位置便利，设施完善，是理想的住宿选择"
        suggested_time = "入住 15:00 - 次日退房 12:00"
        visit_duration = 720  # 12小时
        best_visit_time = "全天"
        tags = ["便利位置", "舒适住宿"]
    else:
        introduction = f"{name}是一个不错的{poi_type.value}场所"
        suggested_time = "适时游览"
        visit_duration = 120
        best_visit_time = "全天"
        tags = [poi_type.value]
    
    return EnrichedPOI(
        poi_id=poi_id,
        name=name,
        poi_type=poi_type,
        location=location,
        introduction=introduction,
        suggested_time=suggested_time,
        rating=4.2,
        visit_duration=visit_duration,
        best_visit_time=best_visit_time,
        tags=tags
    )


def convert_basic_to_enriched(basic_poi: BasicPOI, **enriched_fields) -> EnrichedPOI:
    """
    将BasicPOI转换为EnrichedPOI
    
    Args:
        basic_poi: 基础POI对象
        **enriched_fields: 额外的丰富信息字段
        
    Returns:
        EnrichedPOI对象
    """
    # 设置默认值
    defaults = {
        "introduction": f"{basic_poi.name}是一个值得游览的{basic_poi.poi_type}",
        "suggested_time": "适时游览",
        "rating": 4.0,
        "visit_duration": 120,
        "best_visit_time": "全天",
        "tags": [basic_poi.poi_type]
    }
    
    # 合并用户提供的字段
    defaults.update(enriched_fields)
    
    return EnrichedPOI(
        poi_id=basic_poi.poi_id,
        name=basic_poi.name,
        poi_type=basic_poi.poi_type,
        location=basic_poi.location,
        address=basic_poi.address,
        **defaults
    )
