"""
意图分析流程集成测试

测试两步意图分析流程：框架分析 -> 偏好分析 -> 上下文准备
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# 导入要测试的组件
from src.agents.travel_planner_lg.nodes import (
    run_framework_analysis,
    run_preference_analysis,
    prepare_planning_context
)
from src.agents.travel_planner_lg.state import StandardAgentState
from src.tools.unified_registry import unified_registry

# 确保工具被正确注册
import src.tools.travel_planner.consolidated_tools  # 这会触发工具注册


class TestIntentAnalysisFlow:
    """测试意图分析流程"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建基础状态
        self.base_state: StandardAgentState = {
            "messages": [{"content": "我想去北京玩三天"}],
            "task_id": "test_task_001",
            "user_id": "test_user",
            "original_query": "我想去北京玩三天",
            "current_phase": "initialization",
            "execution_mode": "automatic",
            "framework_analysis": None,
            "preference_analysis": None,
            "consolidated_intent": None,
            "planning_log": [],
            "current_action": None,
            "daily_plans": {},
            "daily_time_tracker": {},
            "total_budget_tracker": 0.0,
            "tool_results": {},
            "notification_service": None,
            "user_profile": {
                "age": 30,
                "interests": ["文化", "美食"],
                "budget_level": "中等"
            },
            "user_memories": None,
            "vehicle_info": None,
            "final_itinerary": None,
            "is_completed": False,
            "has_error": False,
            "error_message": None,
            "destinations": None,
            "core_intent": None,
            "multi_city_strategy": None,
            "driving_context": None
        }
    
    @pytest.mark.asyncio
    async def test_framework_analysis_success(self):
        """测试核心框架分析成功场景"""
        # 创建模拟事件总线
        mock_event_bus = AsyncMock()
        state = self.base_state.copy()
        state["notification_service"] = mock_event_bus
        
        # 执行框架分析
        result = await run_framework_analysis(state)
        
        # 验证结果
        assert "framework_analysis" in result
        assert result["current_phase"] == "framework_analysis_completed"
        assert "has_error" not in result or not result["has_error"]
        
        # 验证框架分析结果结构
        framework = result["framework_analysis"]
        assert "core_intent" in framework
        assert "multi_city_strategy" in framework
        assert "driving_context" in framework
        assert "analysis_confidence" in framework
        
        # 验证核心意图字段
        core_intent = framework["core_intent"]
        assert "destinations" in core_intent
        assert "travel_days" in core_intent
        assert isinstance(core_intent["destinations"], list)
        assert isinstance(core_intent["travel_days"], int)
        
        # 验证事件发布
        assert mock_event_bus.notify_phase_start.called
        assert mock_event_bus.notify_phase_end.called
    
    @pytest.mark.asyncio
    async def test_preference_analysis_success(self):
        """测试偏好分析成功场景"""
        # 准备带有框架分析结果的状态
        mock_event_bus = AsyncMock()
        state = self.base_state.copy()
        state["notification_service"] = mock_event_bus
        state["framework_analysis"] = {
            "core_intent": {
                "destinations": ["北京"],
                "travel_days": 3,
                "travel_theme": ["文化"],
                "budget_range": "中等"
            },
            "multi_city_strategy": {"is_multi_city": False},
            "driving_context": {"has_driving_needs": False}
        }
        
        # 执行偏好分析
        result = await run_preference_analysis(state)
        
        # 验证结果
        assert "preference_analysis" in result
        assert result["current_phase"] == "preference_analysis_completed"
        assert "has_error" not in result or not result["has_error"]
        
        # 验证偏好分析结果结构
        preferences = result["preference_analysis"]
        assert "attraction_preferences" in preferences
        assert "food_preferences" in preferences
        assert "accommodation_preferences" in preferences
        assert "preference_confidence" in preferences
        
        # 验证景点偏好字段
        attractions = preferences["attraction_preferences"]
        assert "preferred_types" in attractions
        assert "must_visit" in attractions
        assert isinstance(attractions["preferred_types"], list)
        
        # 验证事件发布
        assert mock_event_bus.notify_phase_start.called
        assert mock_event_bus.notify_phase_end.called
    
    @pytest.mark.asyncio
    async def test_prepare_planning_context_success(self):
        """测试规划上下文准备成功场景"""
        # 准备完整的分析结果状态
        mock_event_bus = AsyncMock()
        state = self.base_state.copy()
        state["notification_service"] = mock_event_bus
        state["framework_analysis"] = {
            "core_intent": {
                "destinations": ["北京"],
                "travel_days": 3,
                "travel_theme": ["文化"],
                "budget_range": "中等",
                "group_size": 2
            },
            "multi_city_strategy": {"is_multi_city": False},
            "driving_context": {"has_driving_needs": False}
        }
        state["preference_analysis"] = {
            "attraction_preferences": {
                "preferred_types": ["历史文化"],
                "must_visit": ["故宫"]
            },
            "food_preferences": {
                "cuisine_types": ["北京菜"],
                "dietary_restrictions": []
            },
            "accommodation_preferences": {
                "hotel_level": "四星级",
                "location_priority": ["交通便利"]
            }
        }
        
        # 执行上下文准备
        result = await prepare_planning_context(state)
        
        # 验证结果
        assert "consolidated_intent" in result
        assert "key_insights" in result
        assert "icp_context" in result
        assert result["current_phase"] == "planning_ready"
        assert "has_error" not in result or not result["has_error"]
        
        # 验证整合意图结构
        consolidated = result["consolidated_intent"]
        assert "destinations" in consolidated
        assert "travel_days" in consolidated
        assert "preferences" in consolidated
        assert "constraints" in consolidated
        
        # 验证关键洞察
        insights = result["key_insights"]
        assert "planning_complexity" in insights
        assert "key_challenges" in insights
        
        # 验证ICP上下文
        icp_context = result["icp_context"]
        assert "planning_goals" in icp_context
        assert "available_tools" in icp_context
        assert "constraints" in icp_context
    
    @pytest.mark.asyncio
    async def test_complete_intent_analysis_flow(self):
        """测试完整的意图分析流程"""
        # 创建模拟事件总线
        mock_event_bus = AsyncMock()
        state = self.base_state.copy()
        state["notification_service"] = mock_event_bus
        
        # 步骤1: 框架分析
        result1 = await run_framework_analysis(state)
        assert not result1.get("has_error", False)
        state.update(result1)
        
        # 步骤2: 偏好分析
        result2 = await run_preference_analysis(state)
        assert not result2.get("has_error", False)
        state.update(result2)
        
        # 步骤3: 上下文准备
        result3 = await prepare_planning_context(state)
        assert not result3.get("has_error", False)
        state.update(result3)
        
        # 验证最终状态
        assert state["current_phase"] == "planning_ready"
        assert "framework_analysis" in state
        assert "preference_analysis" in state
        assert "consolidated_intent" in state
        assert "key_insights" in state
        assert "icp_context" in state
        
        # 验证事件发布次数
        # 每个阶段都会发布start和end事件，plus context_preparation的end事件
        assert mock_event_bus.notify_phase_start.call_count == 2  # framework + preference
        assert mock_event_bus.notify_phase_end.call_count == 3    # framework + preference + context
    
    @pytest.mark.asyncio
    async def test_framework_analysis_missing_tools(self):
        """测试框架分析工具缺失的错误处理"""
        # 临时移除工具
        original_tool = unified_registry._planner_tools.pop("format_framework_analysis_prompt", None)
        
        try:
            state = self.base_state.copy()
            result = await run_framework_analysis(state)
            
            # 验证错误处理
            assert result["has_error"] is True
            assert "error_message" in result
            assert "tool not found" in result["error_message"]
            assert result["current_phase"] == "error"
            
        finally:
            # 恢复工具
            if original_tool:
                unified_registry._planner_tools["format_framework_analysis_prompt"] = original_tool
    
    @pytest.mark.asyncio
    async def test_preference_analysis_missing_framework(self):
        """测试偏好分析缺少框架结果的错误处理"""
        state = self.base_state.copy()
        # 不设置framework_analysis
        
        result = await run_preference_analysis(state)
        
        # 验证错误处理
        assert result["has_error"] is True
        assert "error_message" in result
        assert "framework_analysis result not found" in result["error_message"]
        assert result["current_phase"] == "error"
    
    @pytest.mark.asyncio
    async def test_context_preparation_missing_analysis(self):
        """测试上下文准备缺少分析结果的错误处理"""
        state = self.base_state.copy()
        # 只设置framework_analysis，不设置preference_analysis
        state["framework_analysis"] = {"core_intent": {}}
        
        result = await prepare_planning_context(state)
        
        # 验证错误处理
        assert result["has_error"] is True
        assert "error_message" in result
        assert "missing" in result["error_message"].lower()
        assert result["current_phase"] == "error"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
