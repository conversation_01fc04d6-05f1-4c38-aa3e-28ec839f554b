#!/usr/bin/env python3
"""
直接测试V3 API端点
"""
import asyncio
import httpx
import json

async def test_v3_api():
    """测试V3 API端点"""
    url = "http://localhost:8000/api/v3/travel-planner/plan"
    
    payload = {
        "user_query": "我想去北京玩3天，喜欢历史文化景点，需要自驾出行，预算中等",
        "user_id": "1",
        "execution_mode": "interactive",
        "vehicle_info": {
            "model": "特斯拉Model 3",
            "range": 500,
            "chargingType": "快充",
            "executionMode": "interactive"
        }
    }
    
    print("🚀 开始测试V3 API端点...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload)
            
            print(f"\n📊 响应状态: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                print("✅ API请求成功")
                
                # 检查是否是SSE响应
                content_type = response.headers.get('content-type', '')
                if 'text/event-stream' in content_type:
                    print("📡 检测到SSE流响应")
                    # 读取前几个事件
                    content = response.text
                    lines = content.split('\n')[:20]  # 只显示前20行
                    for line in lines:
                        if line.strip():
                            print(f"  {line}")
                else:
                    print("📄 普通JSON响应")
                    try:
                        data = response.json()
                        print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    except:
                        print(f"响应文本: {response.text[:500]}...")
                        
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
    except Exception as e:
        print(f"💥 请求异常: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_v3_api())
