"""
MySQL数据库客户端

提供一个异步的MySQL连接池，用于高效地与数据库进行交互。
使用mysql-connector-python提供稳定的MySQL连接功能。
"""
import asyncio
from contextlib import asynccontextmanager
from typing import Optional, Dict, Any, AsyncGenerator
import mysql.connector
from mysql.connector import pooling, Error
from src.core.config import get_settings
from src.core.logger import get_logger
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, sessionmaker
from urllib.parse import quote_plus
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

logger = get_logger(__name__)

# 全局连接池变量
connection_pool: Optional[pooling.MySQLConnectionPool] = None

_async_engine = None
_AsyncSessionLocal = None

def get_async_db_url():
    """构建MySQL数据库URL（用于SQLAlchemy异步）"""
    mysql_conf = get_settings().mysql
    password = quote_plus(mysql_conf.password)
    return f"mysql+aiomysql://{mysql_conf.user}:{password}@{mysql_conf.host}:{mysql_conf.port}/{mysql_conf.database}?charset=utf8mb4"

# 初始化异步SQLAlchemy引擎和SessionLocal
if _async_engine is None:
    _async_engine = create_async_engine(
        get_async_db_url(),
        pool_size=5,
        max_overflow=10,
        pool_timeout=30,
        pool_recycle=1800,
        echo=False
    )
    _AsyncSessionLocal = async_sessionmaker(
        bind=_async_engine, expire_on_commit=False, class_=AsyncSession
    )

@asynccontextmanager
async def get_db():
    """
    FastAPI异步依赖项：获取SQLAlchemy AsyncSession。
    用于依赖注入。
    """
    async with _AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error("Async DB error", error=str(e))
            raise HTTPException(status_code=500, detail="Async DB error")

async def get_mysql_pool() -> pooling.MySQLConnectionPool:
    """
    获取一个MySQL连接池实例 (单例模式)。
    如果连接池不存在，则根据配置创建一个新的。
    """
    global connection_pool
    if connection_pool:
        return connection_pool

    settings = get_settings()
    db_settings = settings.mysql
    
    logger.info(f"正在创建MySQL连接池: host={db_settings.host} port={db_settings.port} db={db_settings.database}")
    
    try:
        # 创建连接配置
        config = {
            'host': db_settings.host,
            'port': db_settings.port,
            'user': db_settings.user,
            'password': db_settings.password,
            'database': db_settings.database,
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci',
            'autocommit': True,
            'raise_on_warnings': True,
            'use_unicode': True,
            'sql_mode': 'TRADITIONAL',
            'time_zone': '+00:00',
            'connection_timeout': 30,
            'pool_name': 'autopilot_ai_pool',
            'pool_size': 10,
            'pool_reset_session': True
        }
        
        # 创建连接池
        connection_pool = pooling.MySQLConnectionPool(**config)
        
        # 测试连接
        test_conn = connection_pool.get_connection()
        if test_conn.is_connected():
            test_conn.close()
            logger.info("MySQL连接池创建成功")
        else:
            raise Error("测试连接失败")
            
        return connection_pool
        
    except Exception as e:
        logger.error(f"创建MySQL连接池失败: {e}", exc_info=True)
        raise

async def close_mysql_pool():
    """关闭全局MySQL连接池"""
    global connection_pool
    if connection_pool:
        # mysql-connector-python的连接池没有显式的关闭方法
        # 连接池会在所有连接被释放后自动清理
        connection_pool = None
        logger.info("MySQL连接池已关闭")

@asynccontextmanager
async def get_db_connection():
    """
    从连接池获取一个数据库连接。
    使用异步上下文管理器确保连接被正确释放。
    
    使用示例:
    async with get_db_connection() as conn:
        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT * FROM users")
        result = cursor.fetchall()
        cursor.close()
    """
    pool = await get_mysql_pool()
    conn = None
    try:
        # 在线程池中获取连接（因为mysql-connector-python是同步的）
        loop = asyncio.get_event_loop()
        conn = await loop.run_in_executor(None, pool.get_connection)
        
        if not conn.is_connected():
            raise Error("获取的连接无效")
            
        logger.debug("MySQL连接已获取")
        yield conn
        
    except Exception as e:
        logger.error(f"MySQL连接操作失败: {e}")
        if conn and conn.is_connected():
            await loop.run_in_executor(None, conn.rollback)
        raise
    finally:
        if conn and conn.is_connected():
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, conn.close)
            logger.debug("MySQL连接已释放")

@asynccontextmanager
async def get_db_cursor(dictionary: bool = True):
    """
    从连接池获取一个数据库游标。
    使用异步上下文管理器确保连接和游标被正确释放。
    
    Args:
        dictionary: 是否返回字典格式的结果，默认True
    
    使用示例:
    async with get_db_cursor() as cursor:
        await execute_query(cursor, "SELECT * FROM users WHERE id = %s", (user_id,))
        result = cursor.fetchall()
    """
    async with get_db_connection() as conn:
        cursor = None
        try:
            cursor = conn.cursor(dictionary=dictionary)
            logger.debug("MySQL游标已创建")
            yield cursor
        except Exception as e:
            logger.error(f"MySQL游标操作失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
                logger.debug("MySQL游标已关闭")

async def execute_query(cursor, query: str, params: tuple = None) -> None:
    """
    异步执行SQL查询
    
    Args:
        cursor: 数据库游标
        query: SQL查询语句
        params: 查询参数
    """
    loop = asyncio.get_event_loop()
    if params:
        await loop.run_in_executor(None, cursor.execute, query, params)
    else:
        await loop.run_in_executor(None, cursor.execute, query)

async def execute_many(cursor, query: str, params_list: list) -> None:
    """
    异步执行批量SQL查询
    
    Args:
        cursor: 数据库游标
        query: SQL查询语句
        params_list: 参数列表
    """
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, cursor.executemany, query, params_list)

# 便捷的查询方法
async def fetch_one(query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
    """执行查询并返回一条记录"""
    async with get_db_cursor() as cursor:
        await execute_query(cursor, query, params)
        result = cursor.fetchone()
        return result

async def fetch_all(query: str, params: tuple = None) -> list:
    """执行查询并返回所有记录"""
    async with get_db_cursor() as cursor:
        await execute_query(cursor, query, params)
        result = cursor.fetchall()
        return result

async def execute_update(query: str, params: tuple = None) -> int:
    """执行更新/插入/删除操作并返回影响的行数"""
    async with get_db_cursor() as cursor:
        await execute_query(cursor, query, params)
        return cursor.rowcount

async def test_connection() -> bool:
    """测试数据库连接是否正常"""
    try:
        result = await fetch_one("SELECT 1 as test")
        return result and result.get('test') == 1
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False

class MySQLClient:
    """
    MySQL客户端类

    提供面向对象的MySQL数据库操作接口，封装连接池和常用操作。
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.MySQLClient")

    async def connect(self) -> bool:
        """测试连接"""
        try:
            return await test_connection()
        except Exception as e:
            self.logger.error(f"MySQL连接失败: {e}")
            return False

    async def fetch_one(self, query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行查询并返回一条记录"""
        return await fetch_one(query, params)

    async def fetch_all(self, query: str, params: tuple = None) -> list:
        """执行查询并返回所有记录"""
        return await fetch_all(query, params)

    async def execute(self, query: str, params: tuple = None) -> int:
        """执行更新/插入/删除操作并返回影响的行数"""
        return await execute_update(query, params)

    async def execute_many(self, query: str, params_list: list) -> int:
        """执行批量操作"""
        async with get_db_cursor() as cursor:
            await execute_many(cursor, query, params_list)
            return cursor.rowcount

    async def close(self):
        """关闭连接池"""
        await close_mysql_pool()


# 为了向后兼容，保留原有的接口
async def get_db_cursor_legacy():
    """
    旧版本兼容接口 - 不推荐使用

    使用示例:
    async with get_db_cursor_legacy() as cursor:
        await execute_query(cursor, "SELECT * FROM users")
        result = cursor.fetchall()
    """
    logger.warning("使用了已弃用的get_db_cursor_legacy方法，请迁移到get_db_cursor")
    async with get_db_cursor() as cursor:
        yield cursor
