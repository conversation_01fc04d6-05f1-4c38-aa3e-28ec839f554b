"""
性能测试 - V3.0统一架构

测试统一架构的性能表现，包括：
1. 响应时间测试
2. 内存使用测试
3. 并发处理能力测试
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

# 导入要测试的组件
from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.tools.unified_registry import unified_registry

# 确保所有工具被正确注册
import src.tools.travel_planner.consolidated_tools
import src.tools.travel_planner.icp_tools


class TestPerformance:
    """性能测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建模拟Redis客户端
        self.mock_redis = AsyncMock()
        self.mock_redis_client = Mock()
        self.mock_redis_client.client = self.mock_redis
        
        # 创建事件总线
        self.event_bus = UnifiedEventBus(self.mock_redis_client, task_ttl=3600)
        
        # 创建图实例
        self.graph = TravelPlannerGraphV3(event_bus=self.event_bus)
        
        # 准备测试数据
        self.test_input = {
            "user_query": "我想去北京玩三天，喜欢历史文化景点",
            "user_id": "perf_test_user",
            "execution_mode": "automatic",
            "user_profile": {
                "age": 30,
                "interests": ["文化", "历史"],
                "budget_level": "中等"
            }
        }
        
        # 模拟POI搜索结果
        self.mock_poi_results = [
            {"name": "故宫", "address": "北京市东城区", "type": "景点", "rating": 4.8},
            {"name": "天安门", "address": "北京市东城区", "type": "景点", "rating": 4.7},
            {"name": "颐和园", "address": "北京市海淀区", "type": "景点", "rating": 4.6}
        ]
    
    @pytest.mark.asyncio
    async def test_response_time_single_request(self):
        """测试单个请求的响应时间"""
        print("\n=== 响应时间测试 ===")
        
        with patch.object(unified_registry, 'execute_action_tool', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = self.mock_poi_results
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行工作流
            result = await self.graph.invoke(self.test_input)
            
            # 记录结束时间
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"📊 单次请求响应时间: {response_time:.2f} 秒")
            
            # 验证结果
            assert result.get("is_completed", False) is True
            assert response_time < 10.0  # 期望在10秒内完成
            
            print(f"✅ 响应时间测试通过 (< 10秒)")
    
    @pytest.mark.asyncio
    async def test_multiple_requests_stability(self):
        """测试多次请求的稳定性"""
        print("\n=== 多次请求稳定性测试 ===")

        with patch.object(unified_registry, 'execute_action_tool', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = self.mock_poi_results

            # 执行多次工作流
            success_count = 0
            total_time = 0

            for i in range(5):
                test_input = self.test_input.copy()
                test_input["user_id"] = f"stability_test_user_{i}"

                start_time = time.time()
                result = await self.graph.invoke(test_input)
                end_time = time.time()

                request_time = end_time - start_time
                total_time += request_time

                if result.get("is_completed", False):
                    success_count += 1
                    print(f"📊 第{i+1}次请求成功，耗时: {request_time:.2f} 秒")
                else:
                    print(f"❌ 第{i+1}次请求失败")

        success_rate = success_count / 5 * 100
        avg_time = total_time / 5

        print(f"📊 成功率: {success_rate:.1f}%")
        print(f"📊 平均响应时间: {avg_time:.2f} 秒")

        # 验证稳定性
        assert success_rate >= 100.0  # 期望100%成功率
        assert avg_time < 5.0  # 期望平均响应时间小于5秒
        print(f"✅ 多次请求稳定性测试通过")
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发处理能力"""
        print("\n=== 并发处理测试 ===")
        
        async def single_request(user_id: str) -> Dict[str, Any]:
            """单个请求处理"""
            test_input = self.test_input.copy()
            test_input["user_id"] = user_id
            
            # 创建独立的图实例避免状态冲突
            graph = TravelPlannerGraphV3(event_bus=None)
            
            with patch.object(unified_registry, 'execute_action_tool', new_callable=AsyncMock) as mock_execute:
                mock_execute.return_value = self.mock_poi_results
                return await graph.invoke(test_input)
        
        # 并发执行多个请求
        concurrent_count = 3
        start_time = time.time()
        
        tasks = [
            single_request(f"concurrent_user_{i}")
            for i in range(concurrent_count)
        ]
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"📊 并发请求数: {concurrent_count}")
        print(f"📊 总处理时间: {total_time:.2f} 秒")
        print(f"📊 平均每请求时间: {total_time/concurrent_count:.2f} 秒")
        
        # 验证所有请求都成功完成
        for i, result in enumerate(results):
            assert result.get("is_completed", False) is True
            print(f"✅ 并发请求 {i+1} 完成")
        
        # 验证并发处理效率（总时间应该小于串行处理时间）
        expected_serial_time = concurrent_count * 5  # 假设单个请求需要5秒
        assert total_time < expected_serial_time
        print(f"✅ 并发处理测试通过 (总时间 < {expected_serial_time}秒)")
    
    @pytest.mark.asyncio
    async def test_tool_registry_performance(self):
        """测试工具注册表性能"""
        print("\n=== 工具注册表性能测试 ===")
        
        # 测试工具查找性能
        start_time = time.time()
        
        for _ in range(1000):
            # 查找Planner Tools
            tool = unified_registry.get_planner_tool("create_consolidated_intent")
            assert tool is not None
            
            # 查找Action Tools
            action_tools = unified_registry.get_action_tool_names()
            assert isinstance(action_tools, list)
        
        end_time = time.time()
        lookup_time = end_time - start_time
        
        print(f"📊 1000次工具查找耗时: {lookup_time:.4f} 秒")
        print(f"📊 平均单次查找耗时: {lookup_time/1000*1000:.4f} 毫秒")
        
        # 验证查找性能（应该很快）
        assert lookup_time < 1.0  # 1000次查找应该在1秒内完成
        print(f"✅ 工具注册表性能测试通过")
    
    def test_event_bus_performance(self):
        """测试事件总线性能"""
        print("\n=== 事件总线性能测试 ===")
        
        # 测试事件发布性能
        start_time = time.time()
        
        for i in range(100):
            # 模拟事件发布（同步版本）
            event_data = {
                "event": "test_event",
                "data": {"message": f"Test message {i}"}
            }
            # 这里只测试事件数据的构建，不实际发布到Redis
        
        end_time = time.time()
        event_time = end_time - start_time
        
        print(f"📊 100次事件构建耗时: {event_time:.4f} 秒")
        print(f"📊 平均单次事件构建耗时: {event_time/100*1000:.4f} 毫秒")
        
        # 验证事件构建性能
        assert event_time < 0.1  # 100次事件构建应该在0.1秒内完成
        print(f"✅ 事件总线性能测试通过")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
