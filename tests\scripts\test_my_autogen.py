#!/usr/bin/env python3
"""
AutoGen Agent 自定义测试文件

这个文件提供了多种AutoGen测试方式：
1. 基础功能测试
2. 交互式对话测试  
3. 调试和诊断测试
4. 性能对比测试

使用方法：
python test_my_autogen.py
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置测试环境变量
def setup_test_environment():
    """设置测试环境变量"""
    print("🔧 设置测试环境...")
    
    # 使用真实的智谱AI API配置
    test_env = {
        "BASIC_LLM_MODEL": "glm-4-flash",
        "BASIC_LLM_API_KEY": "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW",
        "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
        "REASONING_LLM_MODEL": "glm-z1-flash",
        "REASONING_LLM_API_KEY": "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW",
        "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
        "LOG_LEVEL": "INFO"
    }
    
    for key, value in test_env.items():
        if not os.getenv(key):
            os.environ[key] = value
    
    print("✅ 环境变量设置完成")

# 设置环境
setup_test_environment()

try:
    from tests.integration.test_autogen_agent_integration import AutoGenIntegration, AUTOGEN_AVAILABLE
    from src.core.logger import get_logger
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保您在项目根目录下运行此脚本")
    sys.exit(1)

logger = get_logger(__name__)

class AutoGenTester:
    """AutoGen测试器类"""
    
    def __init__(self):
        self.integration = None
        self.test_results = []
    
    async def check_environment(self):
        """检查环境和依赖"""
        print("\n🔍 环境检查")
        print("=" * 50)
        
        # 检查AutoGen可用性
        print(f"AutoGen 可用性: {'✅ 可用' if AUTOGEN_AVAILABLE else '❌ 不可用'}")
        
        if not AUTOGEN_AVAILABLE:
            print("💡 安装命令: pip install autogen-agentchat[all]")
            return False
        
        # 检查配置
        try:
            self.integration = AutoGenIntegration()
            print("✅ AutoGen集成实例创建成功")
        except Exception as e:
            print(f"❌ 集成实例创建失败: {e}")
            return False
        
        # 检查API配置
        api_key = os.getenv("BASIC_LLM_API_KEY", "")
        if not api_key or len(api_key) < 10:
            print("⚠️  警告: API密钥无效，实际对话功能不可用")
            print("💡 请在环境变量或.env文件中设置真实的API密钥")
        else:
            print("✅ API密钥已配置，可以进行真实对话测试")
        
        return True
    
    async def test_basic_functions(self):
        """测试基础功能"""
        print("\n🧪 基础功能测试")
        print("=" * 50)
        
        try:
            # 测试模型客户端创建
            print("🔹 创建基础模型客户端...")
            basic_client = self.integration.create_model_client("basic")
            print(f"✅ 基础客户端创建成功")
            
            print("🔹 创建推理模型客户端...")
            reasoning_client = self.integration.create_model_client("reasoning")
            print(f"✅ 推理客户端创建成功")
            
            # 测试Agent创建
            print("🔹 创建基础助手...")
            basic_assistant = self.integration.create_simple_assistant("测试助手", "basic")
            print(f"✅ 基础助手创建成功: {basic_assistant.name}")
            
            print("🔹 创建推理助手...")
            reasoning_assistant = self.integration.create_simple_assistant("推理助手", "reasoning")
            print(f"✅ 推理助手创建成功: {reasoning_assistant.name}")
            
            self.test_results.append("基础功能测试: ✅ 通过")
            return True
            
        except Exception as e:
            print(f"❌ 基础功能测试失败: {e}")
            self.test_results.append("基础功能测试: ❌ 失败")
            return False
    
    async def test_conversation(self, use_real_api=False):
        """测试对话功能"""
        print("\n💬 对话功能测试")
        print("=" * 50)
        
        if not use_real_api:
            print("⏭️  跳过对话测试（使用 --with-api 参数启用真实API测试）")
            return True
        
        try:
            # 测试基础对话
            print("🔹 测试基础模型对话...")
            response = await self.integration.simple_chat_test(
                "你好，请简单介绍一下AutoGen框架", 
                "basic"
            )
            print(f"🤖 基础模型回复: {response[:100]}...")
            
            # 测试推理对话
            print("🔹 测试推理模型对话...")
            response = await self.integration.simple_chat_test(
                "请深入分析AutoGen框架的架构设计优势", 
                "reasoning"
            )
            print(f"🧠 推理模型回复: {response[:100]}...")
            
            self.test_results.append("对话功能测试: ✅ 通过")
            return True
            
        except Exception as e:
            print(f"❌ 对话功能测试失败: {e}")
            self.test_results.append("对话功能测试: ❌ 失败")
            return False
    
    async def performance_comparison(self, use_real_api=False):
        """性能对比测试"""
        print("\n⚡ 性能对比测试")
        print("=" * 50)
        
        if not use_real_api:
            print("⏭️  跳过性能测试（需要真实API）")
            return True
        
        question = "什么是微服务架构？请简要说明其优缺点。"
        
        try:
            # 基础模型测试
            print("🔹 测试基础模型性能...")
            start_time = time.time()
            basic_response = await self.integration.simple_chat_test(question, "basic")
            basic_time = time.time() - start_time
            
            # 推理模型测试
            print("🔹 测试推理模型性能...")
            start_time = time.time()
            reasoning_response = await self.integration.simple_chat_test(question, "reasoning")
            reasoning_time = time.time() - start_time
            
            # 性能对比
            print(f"\n📊 性能对比结果:")
            print(f"基础模型 (glm-4-flash):")
            print(f"  ⏱️  响应时间: {basic_time:.2f}秒")
            print(f"  📝 回复长度: {len(basic_response)}字符")
            
            print(f"推理模型 (glm-z1-flash):")
            print(f"  ⏱️  响应时间: {reasoning_time:.2f}秒")
            print(f"  📝 回复长度: {len(reasoning_response)}字符")
            print(f"  🎯 详细程度: {'高' if len(reasoning_response) > len(basic_response) else '中'}")
            
            self.test_results.append("性能对比测试: ✅ 通过")
            return True
            
        except Exception as e:
            print(f"❌ 性能对比测试失败: {e}")
            self.test_results.append("性能对比测试: ❌ 失败")
            return False
    
    async def interactive_chat(self):
        """交互式聊天模式"""
        print("\n🎮 交互式聊天模式")
        print("=" * 50)
        print("💡 输入 'quit' 退出")
        print("💡 输入 'switch' 切换模型 (basic ↔ reasoning)")
        print("💡 输入 'status' 查看当前状态")
        print("💡 输入 'help' 查看帮助")
        
        current_role = "basic"
        
        # 检查API密钥
        api_key = os.getenv("BASIC_LLM_API_KEY", "")
        if not api_key or len(api_key) < 10:
            print("⚠️  警告: 未配置有效API密钥，无法进行真实对话")
            print("💡 请在 .env 文件中设置 BASIC_LLM_API_KEY 和 REASONING_LLM_API_KEY")
            return
        
        while True:
            model_name = "glm-4-flash" if current_role == "basic" else "glm-z1-flash"
            print(f"\n🤖 当前模型: {current_role} ({model_name})")
            
            try:
                user_input = input("👤 您: ").strip()
            except (KeyboardInterrupt, EOFError):
                print("\n👋 再见!")
                break
            
            if not user_input:
                continue
            
            if user_input.lower() == 'quit':
                print("👋 再见!")
                break
            elif user_input.lower() == 'switch':
                current_role = "reasoning" if current_role == "basic" else "basic"
                model_name = "glm-4-flash" if current_role == "basic" else "glm-z1-flash"
                print(f"🔄 已切换到 {current_role} 模型 ({model_name})")
                continue
            elif user_input.lower() == 'status':
                print(f"🔍 当前状态:")
                print(f"  模型角色: {current_role}")
                print(f"  模型名称: {model_name}")
                print(f"  AutoGen可用: {AUTOGEN_AVAILABLE}")
                continue
            elif user_input.lower() == 'help':
                print("📚 帮助信息:")
                print("  quit   - 退出聊天")
                print("  switch - 切换模型")
                print("  status - 查看状态")
                print("  help   - 显示帮助")
                continue
            
            try:
                print("🤖 思考中...")
                response = await self.integration.simple_chat_test(user_input, current_role)
                print(f"🤖 助手: {response}")
            except Exception as e:
                print(f"❌ 对话失败: {e}")
                print("💡 请检查API密钥配置和网络连接")
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n📋 测试总结")
        print("=" * 50)
        
        for result in self.test_results:
            print(f"  {result}")
        
        passed = sum(1 for r in self.test_results if "✅" in r)
        total = len(self.test_results)
        
        print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！AutoGen集成工作正常")
        else:
            print("⚠️  部分测试失败，请检查配置和环境")

async def main():
    """主函数"""
    print("🎯 AutoGen Agent 自定义测试")
    print("=" * 60)
    
    # 解析命令行参数
    use_real_api = "--with-api" in sys.argv
    interactive_mode = "--interactive" in sys.argv
    
    tester = AutoGenTester()
    
    # 环境检查
    if not await tester.check_environment():
        print("❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 基础功能测试
    await tester.test_basic_functions()
    
    # 对话功能测试（需要API密钥）
    await tester.test_conversation(use_real_api)
    
    # 性能对比测试（需要API密钥）
    await tester.performance_comparison(use_real_api)
    
    # 打印测试总结
    tester.print_test_summary()
    
    # 交互式模式
    if interactive_mode:
        await tester.interactive_chat()
    else:
        print("\n💡 使用 --interactive 参数启动交互式聊天模式")
        print("💡 使用 --with-api 参数启用真实API测试")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 