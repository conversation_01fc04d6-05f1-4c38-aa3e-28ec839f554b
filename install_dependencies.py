#!/usr/bin/env python3
"""
依赖安装脚本

自动安装旅行规划Agent所需的所有依赖包。
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并处理错误"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    if sys.version_info < (3, 9):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.9或更高版本")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True


def install_basic_dependencies():
    """安装基础依赖"""
    basic_packages = [
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "pyyaml>=6.0.1",
        "httpx>=0.25.0",
        "structlog>=23.2.0",
        "python-dotenv>=1.0.0",
        "jinja2>=3.1.0"
    ]
    
    print("📦 安装基础依赖包...")
    for package in basic_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            return False
    return True


def install_database_dependencies():
    """安装数据库依赖"""
    db_packages = [
        "motor>=3.3.0",  # MongoDB异步驱动
        "pymongo>=4.5.0",  # MongoDB同步驱动
        "sqlalchemy>=2.0.0",  # SQL ORM
        "pymysql>=1.1.0",  # MySQL驱动
        "redis>=5.0.0"  # Redis客户端
    ]
    
    print("🗄️ 安装数据库依赖...")
    for package in db_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            return False
    return True


def install_ai_dependencies():
    """安装AI相关依赖"""
    ai_packages = [
        "openai>=1.0.0",  # OpenAI客户端
        "tiktoken>=0.5.0",  # Token计算
    ]
    
    print("🤖 安装AI依赖...")
    for package in ai_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            return False
    return True


def install_dev_dependencies():
    """安装开发依赖"""
    dev_packages = [
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "black>=23.0.0",
        "isort>=5.12.0",
        "flake8>=6.0.0"
    ]
    
    print("🛠️ 安装开发依赖...")
    for package in dev_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            print(f"⚠️ 开发依赖 {package} 安装失败，但不影响主要功能")
    return True


def create_directories():
    """创建必要的目录"""
    print("📁 创建项目目录...")
    
    directories = [
        "logs",
        "static/css",
        "static/js",
        "static/images",
        "data",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        
    print("✅ 目录创建完成")


def verify_installation():
    """验证安装"""
    print("🔍 验证安装...")
    
    test_imports = [
        "fastapi",
        "uvicorn", 
        "pydantic",
        "motor",
        "httpx",
        "structlog",
        "yaml",
        "openai"
    ]
    
    failed_imports = []
    for module in test_imports:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            failed_imports.append(module)
            
    if failed_imports:
        print(f"\n⚠️ 以下模块导入失败: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ 所有依赖验证通过")
        return True


def main():
    """主函数"""
    print("🚀 AutoPilot AI 旅行规划Agent - 依赖安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
        
    # 升级pip
    if not run_command("python -m pip install --upgrade pip", "升级pip"):
        print("⚠️ pip升级失败，但继续安装")
        
    # 安装各类依赖
    steps = [
        ("基础依赖", install_basic_dependencies),
        ("数据库依赖", install_database_dependencies), 
        ("AI依赖", install_ai_dependencies),
        ("开发依赖", install_dev_dependencies)
    ]
    
    failed_steps = []
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}")
        print("-" * 30)
        if not step_func():
            failed_steps.append(step_name)
            
    # 创建目录
    create_directories()
    
    # 验证安装
    print("\n" + "=" * 60)
    if verify_installation():
        print("\n🎉 依赖安装完成！")
        
        if failed_steps:
            print(f"⚠️ 以下步骤有警告: {', '.join(failed_steps)}")
            
        print("\n📋 下一步:")
        print("1. 运行: python start_server.py --check-only")
        print("2. 运行: python start_server.py --test")
        print("3. 启动: python start_server.py --reload")
        
    else:
        print("\n❌ 依赖安装不完整，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
