# API 及通信协议规范

**版本**: 1.0
**状态**: 草案

## 1. 概述

本文档详细定义了 AutoPilot AI 项目的前后端通信接口、数据交换格式以及基于 SSE (Server-Sent Events) 的实时交互协议。它是前后端协作开发的核心依据。

设计的核心目标是提供一个高度响应、信息透明、可流式交互的用户体验。为此，我们采用 **HTTP POST + SSE** 的复合通信模型：
- **上行 (Client -> Server)**: 用户的请求和交互输入通过标准的 HTTPS POST 请求发送。
- **下行 (Server -> Client)**: 任务执行过程中的所有状态更新、中间结果和最终响应，都通过一个长连接的 SSE 通道以事件流的方式实时推送给客户端。

---

## 2. API 端点定义

### 2.1. 创建新任务
- **Endpoint**: `POST /v1/tasks`
- **Description**: 客户端通过此端点提交一个新任务，启动一次Agent协同工作。
- **Request Body**:
  ```json
  {
    "user_id": "user-abc-123",
    "prompt": "这张图里有什么？帮我写一段描述。",
    "attachments": [
      {
        "asset_id": "asset-uuid-for-the-file",
        "type": "image"
      }
    ],
    "context": {
      "user_location": "Shanghai"
    },
    "config": {
      "llm_role": "vision" 
    }
  }
  ```
  **说明**:
  - `attachments` (可选): 一个附件对象列表。每个对象必须包含 `asset_id`（来自上传请求的步骤1）和 `type` (`image`, `audio`, `video`)。
  - `context` (可选): 一个灵活的JSON对象，用于客户端向Agent传递环境上下文。这对于实现个性化和上下文感知至关重要。服务器应将此内容直接注入任务的 `AgentState`。

- **Success Response (202 Accepted)**:
  服务器立即接受任务并返回一个包含任务ID和SSE流地址的响应，客户端应立即使用此地址建立SSE连接。
  ```json
  {
    "task_id": "task-uuid-xyz-789",
    "sse_url": "/v1/tasks/task-uuid-xyz-789/stream"
  }
  ```

### 2.2. 监听任务事件流
- **Endpoint**: `GET /v1/tasks/{task_id}/stream`
- **Description**: 一个 SSE 端点。客户端在此建立长连接，以接收关于特定任务的所有实时事件。连接将保持开放，直到服务器发送 `task_completed` 或 `task_failed` 事件，或发生网络错误。
- **Response Format**: `text/event-stream`，具体事件格式见 **第3章**。

### 2.3. 提交人工输入
- **Endpoint**: `POST /v1/tasks/{task_id}/input`
- **Description**: 当任务因需要人工干预而暂停时（服务器会发送 `human_intervention_required` 事件），客户端通过此端点提交用户的输入。
- **Request Body**:
  ```json
  {
    "user_input": "我更喜欢公共交通，而不是打车。"
  }
  ```
- **Success Response (200 OK)**:
  ```json
  {
    "status": "input_received"
  }
  ```

### 2.4. 处理多模态输入 (两步上传流程)
当用户需要上传图片、音频、视频等多模态文件时，必须遵循以下两步流程：

#### 步骤 1: 请求上传URL
- **Endpoint**: `POST /v1/uploads/request`
- **Description**: 客户端向后端请求一个安全的上传地址，用于上传单个文件。
- **Request Body**:
  ```json
  {
    "filename": "beijing-trip.jpg",
    "content_type": "image/jpeg"
  }
  ```
- **Success Response (200 OK)**:
  服务器返回一个唯一的资源ID (`asset_id`) 和一个用于上传的预签名URL。
  ```json
  {
    "asset_id": "asset-uuid-for-the-file",
    "upload_url": "https://<cloud-storage-provider>/path/to/upload?signature=..."
  }
  ```
  
#### 步骤 2: 上传文件 & 创建任务
客户端使用上一步获取的 `upload_url`，以 `PUT` 方法直接上传文件。**请注意**，`PUT` 请求的 `Content-Type` 头必须与请求时提供的 `content_type` 完全一致。

文件上传成功后，客户端调用 `POST /v1/tasks` 接口创建任务，并通过 `attachments` 字段将文件与任务关联。

---

## 3. SSE 通信协议

所有通过SSE通道下发的消息都遵循统一的结构。每个消息都是一个独立的事件，包含事件类型和对应的数据负载。

### 3.1. 通用事件结构
每个SSE事件都以 `event:` 和 `data:` 字段进行组织。

- **`event:`**: 事件类型，一个描述事件性质的字符串（详见3.2）。
- **`data:`**: 一个JSON字符串，包含了该事件的具体数据。

**JSON 数据负载通用结构**:
```json
{
  "event_id": "evt-uuid-unique-per-event", // 每个事件的唯一ID
  "task_id": "task-uuid-xyz-789",
  "timestamp": 1698397200, // Unix Timestamp (in seconds)
  "data": {
    // 特定于事件类型的数据负载
  }
}
```

### 3.2. 事件类型详解
以下是预定义的事件类型及其 `data` 负载的详细说明。前端应根据 `event` 类型来决定如何解析 `data` 并更新UI。

| Event Type | 触发时机 | `data` 负载内容 | UI 响应示例 |
| :--- | :--- | :--- | :--- |
| **`task_created`** | 任务成功创建后发送的第一个事件。 | `{"prompt": "用户原始提示", "attachments": [{"asset_id": "...", "type": "image"}]}` | 显示 "任务已开始..."，并展示用户的问题和附件缩略图。 |
| **`plan_generated`** | `PlannerAgent` 生成了执行计划。 | `{"plan": {"thought": "...", "steps": [...]}}` (完整的Plan对象) | 将计划的多个步骤以清单的形式展示给用户。 |
| **`step_started`** | 某个 `ExecutorAgent` 开始执行计划中的一个步骤。 | `{"step_id": 1, "title": "分析图像内容"}` | 将清单中对应的步骤标记为"进行中"状态 (e.g., loading spinner)。 |
| **`tool_call_started`** | Agent 准备调用一个工具。 | `{"tool_name": "vision_analyzer", "tool_input": {"asset_id": "..."}}` | 在对应步骤下显示更具体的状态，如 "正在分析图像..."。 |
| **`tool_call_completed`** | 工具执行完毕。 | `{"tool_name": "vision_analyzer", "tool_output_summary": "图中有一座塔和一片湖。"}` | 显示 "分析完成"。 |
| **`step_completed`** | 一个步骤完整执行成功。 | `{"step_id": 1, "status": "completed", "result_summary": "已识别出图像中的主要元素。"}` | 将对应步骤标记为"已完成" (e.g., green checkmark)。 |
| **`step_failed`** | 一个步骤执行失败。 | `{"step_id": 1, "status": "failed", "error_message": "无法识别的图像格式。"}` | 将对应步骤标记为"失败" (e.g., red cross) 并显示错误信息。 |
| **`response_chunk`** | `ResponseAgent` 正在流式生成最终的自然语言答案。 | `{"chunk": "这张美丽的图片展示了..."}` | 将文本块追加到最终的答案显示区域，实现打字机效果。 |
| **`human_intervention_required`** | 任务暂停，等待用户输入。 | `{"prompt": "您想让我关注图中的哪个部分？"}` | 锁定UI，并弹出一个输入框，向用户展示 `prompt` 中的问题。 |
| **`task_completed`** | 任务所有步骤成功完成。 | `{"final_response": {"...": "..."}}` (完整的、最终的结构化JSON输出) | 停止所有加载动画，明确告知用户任务已完成，并展示完整的最终结果。 |
| **`task_failed`** | 任务因不可恢复的错误而终止。 | `{"error_message": "无法处理该附件。"}` | 停止所有加载动画，显示任务失败的最终错误信息。 |

### 3.3. 事件流的生命周期与终止 (Stream Lifecycle & Termination)
一个SSE事件流代表一个任务的完整生命周期。客户端通过监听特定的**终端事件 (Terminal Events)** 来确认流的结束。

- **终端事件**: `task_completed` 和 `task_failed`。
- **客户端职责**:
  1. 客户端在接收到这两个事件中的**任意一个**后，应认为任务已结束，不会再有新的事件传来。
  2. 此时，客户端应执行清理工作，例如停止所有加载动画，并可以安全地、主动地关闭SSE连接 (`eventSource.close()`)。
  3. 如果服务器在发送终端事件后没有立即关闭连接，客户端也应在处理完事件后自行关闭。

---

## 4. 交互流程示例

以下是一个典型的成功任务交互流程，展示了API调用和SSE事件的顺序：

1.  **Client**: `POST /v1/tasks` with prompt "规划北京3日游"。
2.  **Server**: Responds with `{"task_id": "task-123", "sse_url": "/v1/tasks/task-123/stream"}`.
3.  **Client**: `GET /v1/tasks/task-123/stream` to establish SSE connection.
4.  **Server -> Client (SSE)**:
    - `event: task_created`, `data: {"prompt": "规划北京3日游"}`
    - `event: plan_generated`, `data: {"plan": ...}`
    - `event: step_started`, `data: {"step_id": 1, "title": "研究景点"}`
    - `event: tool_call_started`, `data: {"tool_name": "web_search"}`
    - `event: tool_call_completed`, `data: {"summary": "..."}`
    - `event: step_completed`, `data: {"step_id": 1, ...}`
    - `event: step_started`, `data: {"step_id": 2, "title": "规划路线"}`
    - `...` (more steps)
    - `event: response_chunk`, `data: {"chunk": "第一天..."}`
    - `event: response_chunk`, `data: {"chunk": "我们首先..."}`
    - `...` (more chunks)
    - `event: task_completed`, `data: {"final_response": ...}`
5.  **Server**: Closes the SSE connection.

---

## 5. 客户端错误处理与重连机制

为确保在不稳定的网络环境下依然能提供流畅的用户体验，客户端必须实现健壮的错误处理和重连逻辑。

### 5.1. 网络中断与自动重连
现代浏览器的 `EventSource` API 原生支持自动重连。当网络连接意外断开时，`EventSource` 对象会自动在几秒钟后尝试重新建立连接。

### 5.2. 保证消息连续性: `Last-Event-ID`
- **客户端 (自动行为)**: 当 `EventSource` 尝试重连时，它会自动在HTTP请求头中包含一个 `Last-Event-ID` 字段。该字段的值是客户端收到的最后一个事件的 `event_id`。
- **服务端 (必要实现)**:
  1. 服务器必须能够处理这个 `Last-Event-ID` 请求头。
  2. 当收到一个带有 `Last-Event-ID` 的重连请求时，服务器的职责是**从该ID之后开始，重新发送所有客户端可能错过的事件**。
  3. 这是通过查询存储在Redis中的 `task_steps` 历史记录来实现的。服务器可以对比历史记录与 `Last-Event-ID`，找出差异并补发。
  4. 这种机制确保了即使用户经历了短暂的网络中断，也不会丢失任何任务进度信息。

### 5.3. 服务器心跳 (Keep-Alive)
- **问题**: 某些网络代理或防火墙可能会在连接长时间没有数据传输时，主动关闭空闲的TCP连接。
- **解决方案**: 服务器应实现心跳机制。建议每隔15-20秒，向客户端发送一个不包含任何业务逻辑的"注释"行或一个专用的 `ping` 事件。
  - **注释行示例**:
    ```
    : a keep-alive comment
    ```
  - **Ping事件示例**:
    ```
    event: ping
    data: {"timestamp": 1698397500}
    ```
- 客户端通常可以忽略这些心跳事件，但它们的存在可以有效地保持连接活跃，防止被意外断开。

### 5.4. 最终失败
如果 `EventSource` 在多次尝试后（通常由浏览器策略决定，可能是几分钟）仍然无法重新建立连接，它将触发 `error` 事件并停止重连。此时，客户端应将此视为最终的连接失败，并向用户显示一个明确的错误提示（例如 "与服务器连接已断开，请刷新重试"）。

---

## 6. 客户端实现指南

本节为不同平台的客户端开发提供具体建议。

### 6.1. Web 前端
- **SSE (Server-Sent Events)**: 现代浏览器原生支持 `EventSource` API，可直接使用，无需任何第三方库。应重点实现对不同 `event` 类型的监听和对 `error` 事件的处理。
- **HTTP 请求**: 可使用 `fetch` API 或 `axios` 等库来发送 `POST` 请求。

### 6.2. Android 客户端
- **SSE (Server-Sent Events)**: Android SDK 没有内置的 `EventSource` 实现。**强烈推荐**使用 `OkHttp` 库及其官方的 `okhttp-sse` 扩展。
  - **依赖**: 在 `build.gradle.kts` 或 `build.gradle` 中添加 `com.squareup.okhttp3:okhttp-sse`。
  - **实现**: `okhttp-sse` 提供了与Web `EventSource` 非常相似的 `EventSourceListener` 接口，可以轻松实现对我们协议中所有事件的监听。它会自动处理 `Last-Event-ID` 的重连逻辑。
- **HTTP 请求**: 同样推荐使用 `OkHttp` 和 `Retrofit`。`Retrofit` 可以通过注解将我们的API定义转换为类型安全的方法调用。
- **JSON 解析**: 推荐使用 `Moshi` 或 `kotlinx.serialization` 库，它们性能高，且与 Kotlin 语言特性结合得很好。
- **线程管理**: 所有网络请求都必须在后台线程中执行，以避免阻塞UI主线程。推荐使用 Kotlin Coroutines 来进行异步操作管理。

---

## 7. Dify API 兼容层规范

### 7.1. 概述与策略
为加速原型验证和前端开发，本项目提供一个可选的、与 [Dify](httpss://dify.ai/) 流式API高度兼容的适配器层。
- **策略**: 采用**适配器模式 (Adapter Pattern)**。我们的核心后端保持其原生、信息丰富的API不变。同时，我们提供一个轻量级的"翻译"网关，该网关暴露与Dify一致的API，并将我们内部的事件流实时翻译成Dify的格式。
- **好处**:
  - 前端团队可以立即使用Dify的文档和现有服务进行开发，无需等待后端完成。
  - 后端架构的先进性和可观测性不受影响。
  - 项目后期，前端应用只需切换API基地址，即可无缝从Dify验证环境切换到我们的生产后端。

### 7.2. 兼容API端点
该适配器将实现Dify的`/v1/chat-messages`端点。
- **Endpoint**: `POST /compatible/dify/v1/chat-messages`
- **Request Body**: 与Dify的请求体完全一致，包含`inputs`, `query`, `user`, `response_mode: 'streaming'`等字段。
- **Response**: 返回一个`text/event-stream`类型的SSE流，其事件格式遵循以下映射规则。

### 7.3. 事件映射规则
这是适配器的核心翻译逻辑。它订阅我们内部的原生事件流，并按以下规则进行转换：

| 原生事件 (AutopilotAI) | 触发条件 | 翻译为 Dify 事件 | Dify data 负载内容 | 备注 |
| :--- | :--- | :--- | :--- | :--- |
| `task_created` | 任务开始 | `workflow_started` | `{"task_id": "...", "data": {"status": "running"}}` | 标志着整个流程的开始。 |
| `plan_generated` | `PlannerAgent`生成计划 | `node_started` | `{"task_id": "...", "data": {"node_id": "planner", "node_type": "Planning"}}` | 将我们的"规划"阶段映射为Dify的一个节点。 |
| `step_started` | `ExecutorAgent`开始执行步骤 | `node_started` | `{"task_id": "...", "data": {"node_id": "step_X", "node_type": "Execution"}}` | 每个执行步骤都被视为一个独立的Dify节点。 |
| `tool_call_started` | Agent调用工具 | `agent_thought` | `{"task_id": "...", "data": {"thought": "Calling tool: [tool_name]..."}}` | 将工具调用作为一种"思考"过程展示。 |
| `response_chunk` | `ResponseAgent`生成最终回复 | `message` | `{"task_id": "...", "answer": "chunk_content"}` | 文本块直接映射。 |
| `task_completed` | 任务成功结束 | `workflow_finished` & `message_end` | `{"task_id": "...", "data": {"status": "succeeded"}}` & `{"id": "..."}` | 发送两个事件来标志成功结束。 |
| `task_failed` | 任务失败 | `error` | `{"status": 400, "code": "...", "message": "..."}` | 见下文错误码映射。 |

### 7.4. 错误码映射
为保证前端能一致地处理错误，我们将内部错误码映射到Dify的错误码体系。

| 原生错误 | Dify 错误码 | 描述 |
| :--- | :--- | :--- |
| `AgentExecutionError` | `completion_request_error` | Agent执行或文本生成失败。 |
| `TaskExecutionError` | `app_unavailable` | 整个任务流失败。 |
| `SystemConfigurationError` | `provider_not_initialize` | 系统配置或依赖服务问题。 |
| ... | ... | ... |

### 7.5. 可插拔实现策略
为保证架构的模块化和可扩展性，所有兼容层适配器都必须遵循"可插拔"原则。
- **独立模块**: 每个适配器（如Dify适配器）都应作为一个独立的Python模块实现（例如，在`src/adapters/dify_adapter.py`中）。它应包含所有必需的路由、请求模型和翻译逻辑。
- **框架集成**: 在Web框架层面，每个适配器应被封装成一个可注册的单元（例如FastAPI的`APIRouter`或Flask的`Blueprint`）。
- **配置驱动**: 系统在启动时，应通过配置文件（例如，`settings.ENABLED_ADAPTERS`列表）来决定加载和注册哪些适配器。这使得开启或关闭某个平台的兼容性只需修改配置，而无需改动代码。
- **未来扩展**: 当需要支持新的第三方平台时，只需新增一个独立的适配器模块，并在配置中启用它即可，对现有核心业务逻辑无任何侵入。 