"""
Music Function Tools for AutoGen
This module defines all music-related function tools for use with AutoGen
"""
from autogen import FunctionTool
from .music_tool import MusicTool

def create_music_function_tools() -> list:
    """
    Create all music-related function tools
    
    Returns:
        List of FunctionTool objects for music operations
    """
    music_tool = MusicTool()
    
    # Create function tools for all music operations
    search_songs_tool = FunctionTool(
        name="search_songs",
        description="Search for songs by keywords",
        func=music_tool.search_songs,
        async_fn=False
    )
    
    search_songs_by_artist_tool = FunctionTool(
        name="search_songs_by_artist",
        description="Search for songs by artist name",
        func=music_tool.search_songs_by_artist,
        async_fn=False
    )
    
    search_songs_by_artist_and_name_tool = FunctionTool(
        name="search_songs_by_artist_and_name",
        description="Search for songs by both artist name and song name",
        func=music_tool.search_songs_by_artist_and_name,
        async_fn=False
    )
    
    search_playlists_tool = FunctionTool(
        name="search_playlists",
        description="Search for playlists by keywords",
        func=music_tool.search_playlists,
        async_fn=False
    )
    
    search_albums_tool = FunctionTool(
        name="search_albums",
        description="Search for albums by keywords",
        func=music_tool.search_albums,
        async_fn=False
    )
    
    search_albums_by_artist_tool = FunctionTool(
        name="search_albums_by_artist",
        description="Search for albums by artist name and album keywords",
        func=music_tool.search_albums_by_artist,
        async_fn=False
    )
    
    search_artists_tool = FunctionTool(
        name="search_artists",
        description="Search for artists by keywords",
        func=music_tool.search_artists,
        async_fn=False
    )
    
    get_hot_keywords_tool = FunctionTool(
        name="get_hot_keywords",
        description="Get hot search keywords",
        func=music_tool.get_hot_keywords,
        async_fn=False
    )
    
    get_suggest_keywords_tool = FunctionTool(
        name="get_suggest_keywords",
        description="Get search keyword suggestions based on input",
        func=music_tool.get_suggest_keywords,
        async_fn=False
    )
    
    get_hi_res_tool = FunctionTool(
        name="get_hi_res",
        description="Get Hi-Res content. Supports songs, playlists, and albums.",
        func=music_tool.get_hi_res,
        async_fn=False
    )
    
    get_song_detail_tool = FunctionTool(
        name="get_song_detail",
        description="Get detailed information about a song, including qualities, play URL, and more.",
        func=music_tool.get_song_detail,
        async_fn=False
    )

    # Return all tools in a list
    return [
        search_songs_tool,
        search_songs_by_artist_tool,
        search_songs_by_artist_and_name_tool,
        search_playlists_tool,
        search_albums_tool,
        search_albums_by_artist_tool,
        search_artists_tool,
        get_hot_keywords_tool,
        get_suggest_keywords_tool,
        get_hi_res_tool,
        get_song_detail_tool
    ]
