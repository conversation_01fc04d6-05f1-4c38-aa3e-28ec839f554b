#!/usr/bin/env python3
"""
规划流程调试脚本
模拟实际的规划过程，查看POI搜索的具体参数和结果
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.unified_registry import unified_registry
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_planning_flow():
    """测试规划流程中的POI搜索"""
    print("🔍 开始规划流程调试...")

    try:
        # 初始化应用程序以注册工具
        print("🔧 初始化应用程序...")

        # 导入并初始化必要的模块来注册工具
        from src.agents.services.amap_service import AmapService
        from src.tools.travel_planner.amap_poi_tools import search_poi

        print("✅ 工具模块已导入")

        # 检查POI搜索工具是否注册
        action_tools = unified_registry.get_action_tool_names()
        if "search_poi" not in action_tools:
            print(f"❌ search_poi工具未注册。可用工具: {action_tools}")
            return

        print("✅ search_poi工具已注册")
        
        # 模拟规划过程中的POI搜索调用
        # 这些参数应该与实际规划过程中使用的参数一致
        
        # 测试1: 模拟第一天的景点搜索
        print(f"\n🏛️ 测试1: 模拟第一天上海景点搜索...")
        day1_params = {
            "keywords": "景点",
            "city": "上海",
            "types": None,
            "page_size": 10
        }
        
        print(f"   搜索参数: {day1_params}")
        day1_results = await unified_registry.execute_action_tool("search_poi", **day1_params)
        
        print(f"✅ 第一天搜索结果 ({len(day1_results)}个):")
        for i, poi in enumerate(day1_results[:5]):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            
            # 检查是否包含济南相关信息
            if "济南" in address or "济南" in name:
                print(f"     ⚠️ 发现济南POI！这不应该出现在上海搜索中")
        
        # 测试2: 模拟第二天的景点搜索
        print(f"\n🏛️ 测试2: 模拟第二天上海景点搜索...")
        day2_params = {
            "keywords": "景点",
            "city": "上海",
            "types": None,
            "page_size": 10
        }
        
        print(f"   搜索参数: {day2_params}")
        day2_results = await unified_registry.execute_action_tool("search_poi", **day2_params)
        
        print(f"✅ 第二天搜索结果 ({len(day2_results)}个):")
        for i, poi in enumerate(day2_results[:5]):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            
            # 检查是否包含济南相关信息
            if "济南" in address or "济南" in name:
                print(f"     ⚠️ 发现济南POI！这不应该出现在上海搜索中")
        
        # 测试3: 检查是否有缓存或重复数据
        print(f"\n🔄 测试3: 检查搜索结果是否重复...")
        if day1_results and day2_results:
            day1_names = [poi.get("name", "") for poi in day1_results]
            day2_names = [poi.get("name", "") for poi in day2_results]
            
            common_names = set(day1_names) & set(day2_names)
            if common_names:
                print(f"⚠️ 发现重复POI: {list(common_names)}")
            else:
                print(f"✅ 两天的搜索结果没有重复")
        
        # 测试4: 模拟具体的"都市现代园林展区"搜索
        print(f"\n🎯 测试4: 直接搜索'都市现代园林展区'...")
        target_params = {
            "keywords": "都市现代园林展区",
            "city": "上海",
            "page_size": 5
        }
        
        print(f"   搜索参数: {target_params}")
        target_results = await unified_registry.execute_action_tool("search_poi", **target_params)
        
        print(f"✅ 直接搜索结果 ({len(target_results)}个):")
        for i, poi in enumerate(target_results):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            
            if "都市现代园林展区" in name:
                print(f"     🎯 找到目标POI！")
                print(f"     完整POI数据: {poi}")
        
        # 测试5: 检查回退数据
        print(f"\n🔧 测试5: 测试回退数据机制...")
        fallback_params = {
            "keywords": "不存在的景点12345",
            "city": "上海",
            "page_size": 5
        }
        
        print(f"   搜索参数: {fallback_params}")
        fallback_results = await unified_registry.execute_action_tool("search_poi", **fallback_params)
        
        print(f"✅ 回退数据结果 ({len(fallback_results)}个):")
        for i, poi in enumerate(fallback_results):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            
            if "都市现代园林展区" in name:
                print(f"     🎯 在回退数据中找到目标POI！")
                print(f"     这可能是问题的根源")
        
    except Exception as e:
        print(f"❌ 规划流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_planning_flow())
