"""
旅行规划整合工具集 (V3.0 - 统一架构版)

实现两步意图分析和ICP规划所需的Planner Tools，
包括意图整合、格式化和上下文准备等功能。
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from src.tools.unified_registry import unified_registry

logger = logging.getLogger(__name__)


@unified_registry.register_planner_tool
def create_consolidated_intent(framework_analysis: Dict[str, Any], preference_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    将两步分析结果整合为统一的意图上下文
    
    Args:
        framework_analysis: 核心框架分析结果
        preference_analysis: 偏好分析结果
    
    Returns:
        consolidated_intent: 整合后的统一意图
    """
    try:
        # 提取核心信息
        core_intent = framework_analysis.get("core_intent", {})
        multi_city = framework_analysis.get("multi_city_strategy", {})
        driving = framework_analysis.get("driving_context", {})
        
        attractions = preference_analysis.get("attraction_preferences", {})
        food = preference_analysis.get("food_preferences", {})
        accommodation = preference_analysis.get("accommodation_preferences", {})
        
        # 整合为统一格式
        consolidated = {
            "destinations": core_intent.get("destinations", []),
            "travel_days": core_intent.get("travel_days", 1),
            "travel_theme": core_intent.get("travel_theme", []),
            "budget_range": core_intent.get("budget_range", "中等"),
            "group_info": {
                "size": core_intent.get("group_size", 1),
                "members": core_intent.get("group_members", []),
                "special_requirements": core_intent.get("special_requirements", [])
            },
            "transportation": {
                "departure_city": core_intent.get("departure_city", ""),
                "has_driving": driving.get("has_driving_needs", False),
                "vehicle_requirements": driving.get("vehicle_requirements", ""),
                "between_cities": multi_city.get("transportation_between_cities", ""),
                "driving_scenarios": driving.get("driving_scenarios", [])
            },
            "preferences": {
                "attractions": attractions,
                "food": food,
                "accommodation": accommodation
            },
            "constraints": {
                "budget_per_day": core_intent.get("budget_per_day", 0),
                "accessibility_needs": attractions.get("accessibility_needs", []),
                "dietary_restrictions": food.get("dietary_restrictions", []),
                "time_allocation": multi_city.get("time_allocation", {})
            },
            "confidence_scores": {
                "framework_confidence": framework_analysis.get("analysis_confidence", {}).get("overall_confidence", 0.8),
                "preference_confidence": preference_analysis.get("preference_confidence", {}).get("overall_confidence", 0.8)
            }
        }
        
        logger.info("Successfully consolidated intent from two-step analysis")
        return consolidated
        
    except Exception as e:
        logger.error(f"Failed to consolidate intent: {str(e)}")
        return {}


@unified_registry.register_planner_tool
def format_framework_analysis_prompt(user_query: str, user_profile: Dict[str, Any], current_time: str = None) -> str:
    """
    格式化核心框架分析的提示词
    
    Args:
        user_query: 用户查询
        user_profile: 用户画像
        current_time: 当前时间
    
    Returns:
        格式化后的提示词
    """
    try:
        if current_time is None:
            current_time = datetime.now().isoformat()
        
        # 读取Prompt模板
        prompt_template = """
# 旅行规划核心框架分析 (V3.0 - 统一架构版)

你是一位专业的旅行规划师，负责对用户的旅行需求进行核心框架分析。

## 用户输入

**用户查询**: {user_query}

**用户画像**: {user_profile}

**当前时间**: {current_time}

## 分析要求

请严格按照以下JSON格式输出分析结果：

```json
{{
  "core_intent": {{
    "destinations": ["目的地城市列表"],
    "travel_days": 天数(数字),
    "travel_theme": ["旅行主题列表"],
    "budget_range": "预算范围",
    "group_size": 人数(数字),
    "group_members": ["成员类型"],
    "departure_city": "出发城市",
    "travel_time": "出行时间",
    "special_requirements": ["特殊需求"]
  }},
  "multi_city_strategy": {{
    "is_multi_city": 是否多城市(布尔值),
    "city_priority": ["城市优先级列表"],
    "transportation_between_cities": "城市间交通方式",
    "time_allocation": {{"城市名": 天数}},
    "route_optimization": "路线优化策略"
  }},
  "driving_context": {{
    "has_driving_needs": 是否需要自驾(布尔值),
    "driving_scenarios": ["自驾场景"],
    "vehicle_requirements": "车辆要求",
    "parking_considerations": ["停车考虑"],
    "driving_experience_level": "驾驶经验",
    "fuel_type_preference": "燃料类型偏好"
  }},
  "analysis_confidence": {{
    "core_intent_confidence": 置信度(0-1),
    "multi_city_confidence": 置信度(0-1),
    "driving_context_confidence": 置信度(0-1),
    "overall_confidence": 置信度(0-1)
  }},
  "clarification_needed": ["需要澄清的问题"],
  "assumptions_made": ["做出的假设"]
}}
```

请仔细分析用户查询，提取关键信息，并严格按照上述JSON格式输出。
        """
        
        return prompt_template.format(
            user_query=user_query,
            user_profile=json.dumps(user_profile, ensure_ascii=False, indent=2),
            current_time=current_time
        )
        
    except Exception as e:
        logger.error(f"Failed to format framework analysis prompt: {str(e)}")
        return f"分析用户查询: {user_query}"


@unified_registry.register_planner_tool
def format_preference_analysis_prompt(
    user_query: str, 
    user_profile: Dict[str, Any], 
    framework_result: Dict[str, Any],
    current_time: str = None
) -> str:
    """
    格式化偏好分析的提示词
    
    Args:
        user_query: 用户查询
        user_profile: 用户画像
        framework_result: 框架分析结果
        current_time: 当前时间
    
    Returns:
        格式化后的提示词
    """
    try:
        if current_time is None:
            current_time = datetime.now().isoformat()
        
        prompt_template = """
# 旅行规划个性化偏好分析 (V3.0 - 统一架构版)

你是一位专业的旅行规划师，负责在已确定的旅行框架基础上，深入分析用户的个性化偏好。

## 输入信息

**用户查询**: {user_query}

**用户画像**: {user_profile}

**已确定的旅行框架**: {framework_result}

**当前时间**: {current_time}

## 分析要求

请严格按照以下JSON格式输出偏好分析结果：

```json
{{
  "attraction_preferences": {{
    "preferred_types": ["偏好的景点类型"],
    "must_visit": ["必游景点"],
    "avoid_types": ["避免的类型"],
    "accessibility_needs": ["无障碍需求"],
    "time_preferences": {{
      "morning": "上午偏好",
      "afternoon": "下午偏好",
      "evening": "晚上偏好"
    }},
    "activity_intensity": "活动强度",
    "crowd_tolerance": "人群容忍度",
    "photo_priority": "拍照重要性",
    "cultural_interest": "文化兴趣程度"
  }},
  "food_preferences": {{
    "cuisine_types": ["偏好菜系"],
    "dietary_restrictions": ["饮食限制"],
    "spice_tolerance": "辣度容忍",
    "budget_per_meal": {{
      "breakfast": "早餐预算",
      "lunch": "午餐预算",
      "dinner": "晚餐预算"
    }},
    "dining_scenarios": ["用餐场景"],
    "meal_timing": {{
      "breakfast": "早餐偏好",
      "lunch": "午餐偏好",
      "dinner": "晚餐偏好"
    }},
    "local_specialties": "本地特色兴趣",
    "dining_atmosphere": "用餐氛围偏好"
  }},
  "accommodation_preferences": {{
    "hotel_level": "酒店等级",
    "location_priority": ["位置优先级"],
    "room_requirements": ["房间要求"],
    "amenities_needed": ["必需设施"],
    "amenities_preferred": ["偏好设施"],
    "budget_per_night": "每晚预算",
    "booking_flexibility": "预订灵活性",
    "check_in_preferences": {{
      "early_check_in": 是否早入住(布尔值),
      "late_check_out": 是否晚退房(布尔值)
    }},
    "special_services": ["特殊服务"],
    "brand_preference": "品牌偏好"
  }},
  "preference_confidence": {{
    "attraction_confidence": 置信度(0-1),
    "food_confidence": 置信度(0-1),
    "accommodation_confidence": 置信度(0-1),
    "overall_confidence": 置信度(0-1)
  }},
  "personalization_insights": ["个性化洞察"],
  "recommendation_strategy": {{
    "primary_focus": "主要关注点",
    "decision_factors": ["决策因素"],
    "flexibility_areas": ["灵活区域"]
  }}
}}
```

请基于旅行框架和用户查询，深入分析用户偏好，并严格按照上述JSON格式输出。
        """
        
        return prompt_template.format(
            user_query=user_query,
            user_profile=json.dumps(user_profile, ensure_ascii=False, indent=2),
            framework_result=json.dumps(framework_result, ensure_ascii=False, indent=2),
            current_time=current_time
        )
        
    except Exception as e:
        logger.error(f"Failed to format preference analysis prompt: {str(e)}")
        return f"分析用户偏好: {user_query}"


@unified_registry.register_planner_tool
def validate_analysis_result(analysis_result: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
    """
    验证分析结果的完整性和有效性
    
    Args:
        analysis_result: 分析结果
        analysis_type: 分析类型 (framework/preference)
    
    Returns:
        验证结果和修正建议
    """
    try:
        validation_result = {
            "is_valid": True,
            "missing_fields": [],
            "invalid_values": [],
            "suggestions": []
        }
        
        if analysis_type == "framework":
            # 验证框架分析必需字段
            required_fields = ["core_intent", "multi_city_strategy", "driving_context"]
            for field in required_fields:
                if field not in analysis_result:
                    validation_result["missing_fields"].append(field)
                    validation_result["is_valid"] = False
            
            # 验证核心意图字段
            if "core_intent" in analysis_result:
                core_intent = analysis_result["core_intent"]
                if not core_intent.get("destinations"):
                    validation_result["invalid_values"].append("destinations不能为空")
                    validation_result["is_valid"] = False
                
                if not isinstance(core_intent.get("travel_days"), int) or core_intent.get("travel_days", 0) <= 0:
                    validation_result["invalid_values"].append("travel_days必须是正整数")
                    validation_result["is_valid"] = False
        
        elif analysis_type == "preference":
            # 验证偏好分析必需字段
            required_fields = ["attraction_preferences", "food_preferences", "accommodation_preferences"]
            for field in required_fields:
                if field not in analysis_result:
                    validation_result["missing_fields"].append(field)
                    validation_result["is_valid"] = False
        
        # 生成修正建议
        if validation_result["missing_fields"]:
            validation_result["suggestions"].append(f"请补充缺失字段: {', '.join(validation_result['missing_fields'])}")
        
        if validation_result["invalid_values"]:
            validation_result["suggestions"].append("请修正无效值")
        
        return validation_result
        
    except Exception as e:
        logger.error(f"Failed to validate analysis result: {str(e)}")
        return {
            "is_valid": False,
            "error": str(e),
            "suggestions": ["验证过程出错，请检查分析结果格式"]
        }


@unified_registry.register_planner_tool
def extract_key_insights(consolidated_intent: Dict[str, Any]) -> Dict[str, Any]:
    """
    从整合意图中提取关键洞察
    
    Args:
        consolidated_intent: 整合后的意图
    
    Returns:
        关键洞察和规划建议
    """
    try:
        insights = {
            "planning_complexity": "简单",
            "key_challenges": [],
            "optimization_opportunities": [],
            "resource_requirements": {},
            "timeline_suggestions": {}
        }
        
        # 分析规划复杂度
        destinations = consolidated_intent.get("destinations", [])
        travel_days = consolidated_intent.get("travel_days", 1)
        has_driving = consolidated_intent.get("transportation", {}).get("has_driving", False)
        
        complexity_score = 0
        if len(destinations) > 1:
            complexity_score += 2
        if travel_days > 5:
            complexity_score += 1
        if has_driving:
            complexity_score += 1
        
        if complexity_score >= 3:
            insights["planning_complexity"] = "复杂"
        elif complexity_score >= 1:
            insights["planning_complexity"] = "中等"
        
        # 识别关键挑战
        if len(destinations) > 1:
            insights["key_challenges"].append("多城市协调")
        
        if has_driving:
            insights["key_challenges"].append("自驾路线优化")
        
        group_size = consolidated_intent.get("group_info", {}).get("size", 1)
        if group_size is not None and group_size > 4:
            insights["key_challenges"].append("大团队协调")
        
        # 优化机会
        budget_range = consolidated_intent.get("budget_range", "中等")
        if budget_range in ["经济型", "中等"]:
            insights["optimization_opportunities"].append("成本优化")
        
        if travel_days >= 3:
            insights["optimization_opportunities"].append("时间分配优化")
        
        return insights
        
    except Exception as e:
        logger.error(f"Failed to extract key insights: {str(e)}")
        return {"error": str(e)}


@unified_registry.register_planner_tool
def prepare_icp_context(consolidated_intent: Dict[str, Any]) -> Dict[str, Any]:
    """
    为ICP迭代规划准备上下文
    
    Args:
        consolidated_intent: 整合后的意图
    
    Returns:
        ICP规划上下文
    """
    try:
        icp_context = {
            "planning_goals": [],
            "available_tools": [],
            "constraints": {},
            "success_criteria": {},
            "initial_state": {}
        }
        
        # 设定规划目标
        destinations = consolidated_intent.get("destinations", [])
        travel_days = consolidated_intent.get("travel_days", 1)
        
        for i, dest in enumerate(destinations):
            icp_context["planning_goals"].append(f"为{dest}规划{travel_days}天行程")
        
        # 推荐可用工具
        icp_context["available_tools"] = [
            "search_poi",
            "geocode", 
            "get_driving_route"
        ]
        
        if consolidated_intent.get("transportation", {}).get("has_driving"):
            icp_context["available_tools"].append("search_parking")
        
        # 设定约束条件
        icp_context["constraints"] = {
            "max_days": travel_days,
            "budget_range": consolidated_intent.get("budget_range", "中等"),
            "group_size": consolidated_intent.get("group_info", {}).get("size", 1),
            "dietary_restrictions": consolidated_intent.get("constraints", {}).get("dietary_restrictions", [])
        }
        
        # 成功标准
        icp_context["success_criteria"] = {
            "min_attractions_per_day": 2,
            "max_attractions_per_day": 4,
            "include_meals": True,
            "include_accommodation": travel_days > 1
        }
        
        # 初始状态
        icp_context["initial_state"] = {
            "current_day": 1,
            "planned_activities": {},
            "remaining_budget": 1000,  # 默认预算
            "current_location": consolidated_intent.get("transportation", {}).get("departure_city", "")
        }
        
        return icp_context
        
    except Exception as e:
        logger.error(f"Failed to prepare ICP context: {str(e)}")
        return {"error": str(e)}
