#!/usr/bin/env python3
"""
AutoPilot AI 旅行规划Agent 启动脚本

提供便捷的服务启动方式，支持开发和生产环境配置。
"""
import os
import sys
import argparse
import asyncio
import uvicorn
from pathlib import Path

# 添加src和tools目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

from src.core.config import get_settings
from src.core.logger import get_logger

logger = get_logger("startup")


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print("❌ Python版本过低，需要Python 3.9+")
        return False
        
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的依赖
    required_packages = [
        "fastapi", "uvicorn", "pydantic", "motor", "httpx", "structlog"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
            
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -e .")
        return False
        
    print("✅ 依赖包检查通过")
    
    # 检查配置文件
    config_file = project_root / "config" / "default.yaml"
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
        
    print("✅ 配置文件存在")
    
    return True


def check_services():
    """检查外部服务连接"""
    print("🔗 检查外部服务连接...")
    
    try:
        settings = get_settings()
        
        # 这里可以添加数据库连接检查
        print("✅ 配置加载成功")
        
        # 检查高德API密钥
        amap_key = "e8f742bdb09f99c8c6b035b7f1f04e66"  # 从配置中获取
        if not amap_key:
            print("⚠️ 高德地图API密钥未配置")
        else:
            print("✅ 高德地图API密钥已配置")
            
        return True
        
    except Exception as e:
        print(f"❌ 服务检查失败: {str(e)}")
        return False


def create_directories():
    """创建必要的目录"""
    print("📁 创建必要的目录...")
    
    directories = [
        project_root / "logs",
        project_root / "static" / "css",
        project_root / "static" / "js",
        project_root / "static" / "images"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        
    print("✅ 目录创建完成")


def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║              🛩️  AutoPilot AI 旅行规划Agent                   ║
║                                                              ║
║              智能AI驱动的个性化旅行规划服务                      ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_startup_info(host: str, port: int, reload: bool):
    """打印启动信息"""
    print("\n🚀 服务启动信息:")
    print(f"   📍 地址: http://{host}:{port}")
    print(f"   📚 API文档: http://{host}:{port}/docs")
    print(f"   🔄 热重载: {'启用' if reload else '禁用'}")
    print(f"   🌐 前端页面: http://{host}:{port}/static/index.html")
    print("\n✨ 主要功能:")
    print("   • 智能意图理解")
    print("   • 个性化推荐")
    print("   • 实时规划进度")
    print("   • 高德地图集成")
    print("   • 记忆学习系统")
    print("\n🎯 开始使用:")
    print("   1. 访问前端页面开始规划")
    print("   2. 查看API文档了解接口")
    print("   3. 使用健康检查端点监控服务")
    print("\n" + "="*60)


async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试配置加载
        settings = get_settings()
        assert settings.app.name == "AutoPilot AI"
        print("✅ 配置系统正常")
        
        # 测试日志系统
        logger.info("测试日志消息")
        print("✅ 日志系统正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AutoPilot AI 旅行规划Agent 启动脚本")
    parser.add_argument("--host", default="0.0.0.0", help="服务监听地址")
    parser.add_argument("--port", type=int, default=8000, help="服务端口")
    parser.add_argument("--reload", action="store_true", help="启用热重载（开发模式）")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    parser.add_argument("--log-level", default="info", choices=["debug", "info", "warning", "error"], help="日志级别")
    parser.add_argument("--check-only", action="store_true", help="仅检查环境，不启动服务")
    parser.add_argument("--test", action="store_true", help="运行测试")
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请修复后重试")
        sys.exit(1)
        
    # 检查服务
    if not check_services():
        print("\n⚠️ 服务检查有警告，但可以继续启动")
        
    # 创建目录
    create_directories()
    
    # 测试基本功能
    if not asyncio.run(test_basic_functionality()):
        print("\n❌ 基本功能测试失败")
        sys.exit(1)
        
    print("\n✅ 所有检查通过")
    
    # 如果只是检查环境，则退出
    if args.check_only:
        print("\n🎉 环境检查完成，系统准备就绪")
        return
        
    # 如果是测试模式
    if args.test:
        print("\n🧪 运行完整测试...")
        os.system("python test_travel_planner.py")
        return
        
    # 打印启动信息
    print_startup_info(args.host, args.port, args.reload)
    
    # 启动服务
    try:
        uvicorn.run(
            "src.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers if not args.reload else 1,
            log_level=args.log_level,
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n⏹️ 服务已停止")
    except Exception as e:
        print(f"\n💥 服务启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
