"""
结构化日志系统

支持JSON和文本格式的结构化日志记录，包含分布式追踪和性能监控功能。
遵循业界最佳实践，便于日志聚合和分析。
"""
import json
import sys
import time
import uuid
import logging
import threading
from datetime import datetime
from typing import Dict, Any, Optional, Union
from contextlib import contextmanager
from functools import wraps


class StructuredLogger:
    """
    结构化日志记录器
    
    支持JSON和文本格式输出，自动添加时间戳、模块名等上下文信息。
    """
    
    def __init__(
        self, 
        name: str, 
        level: str = "INFO", 
        format: str = "json",
        file_path: Optional[str] = None
    ):
        self.name = name
        self.level = level.upper()
        self.format = format.lower()
        self.file_path = file_path
        
        # 设置日志级别映射
        self._level_mapping = {
            "DEBUG": 10,
            "INFO": 20,
            "WARNING": 30,
            "ERROR": 40,
            "CRITICAL": 50
        }
        
        self._min_level = self._level_mapping.get(self.level, 20)
    
    def _should_log(self, level: str) -> bool:
        """检查是否应该记录指定级别的日志"""
        return self._level_mapping.get(level.upper(), 0) >= self._min_level
    
    def _format_log(self, level: str, message: str, **kwargs) -> str:
        """格式化日志消息"""
        log_data = {
            "timestamp": datetime.now().isoformat() + "Z",
            "level": level.upper(),
            "module": self.name,
            "message": message,
            **kwargs
        }
        
        if self.format == "json":
            return json.dumps(log_data, ensure_ascii=False)
        else:
            # 文本格式
            extras = " ".join([f"{k}={v}" for k, v in kwargs.items()])
            return f"{log_data['timestamp']} {level.upper()} [{self.name}] {message} {extras}".strip()
    
    def _write_log(self, formatted_message: str):
        """写入日志到输出目标"""
        if self.file_path:
            with open(self.file_path, 'a', encoding='utf-8') as f:
                f.write(formatted_message + '\n')
        else:
            print(formatted_message, file=sys.stdout)
    
    def _log(self, level: str, message: str, **kwargs):
        """内部日志记录方法"""
        if not self._should_log(level):
            return
            
        # 处理异常信息
        if 'exc_info' in kwargs and kwargs['exc_info']:
            import traceback
            kwargs['exception'] = traceback.format_exc()
            del kwargs['exc_info']
        
        formatted = self._format_log(level, message, **kwargs)
        self._write_log(formatted)
    
    def debug(self, message: str, **kwargs):
        """记录DEBUG级别日志"""
        self._log("DEBUG", message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录INFO级别日志"""
        self._log("INFO", message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录WARNING级别日志"""
        self._log("WARNING", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录ERROR级别日志"""
        self._log("ERROR", message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录CRITICAL级别日志"""
        self._log("CRITICAL", message, **kwargs)


class PerformanceLogger:
    """
    性能监控日志记录器
    
    提供上下文管理器和装饰器两种方式来监控操作性能。
    """
    
    def __init__(self, name: str):
        self.logger = StructuredLogger(name, format="json")
    
    @contextmanager
    def measure(self, operation: str):
        """性能监控上下文管理器"""
        start_time = time.time()
        try:
            yield
        finally:
            duration_ms = (time.time() - start_time) * 1000
            self.logger.info(
                f"Performance measurement for {operation}",
                operation=operation,
                duration_ms=round(duration_ms, 2)
            )
    
    def monitor_performance(self, func):
        """性能监控装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            with self.measure(func.__name__):
                return func(*args, **kwargs)
        return wrapper


class TraceLogger:
    """
    分布式追踪日志记录器
    
    支持追踪上下文的创建、传播和结束。
    """
    
    def __init__(self, name: str):
        self.logger = StructuredLogger(name, format="json")
        self._local = threading.local()
    
    def start_trace(self, trace_name: str) -> str:
        """开始一个新的追踪上下文"""
        trace_id = str(uuid.uuid4())
        self._local.trace_id = trace_id
        self._local.trace_name = trace_name
        self._local.trace_start_time = time.time()
        
        self.logger.info(
            f"Trace started: {trace_name}",
            event="trace_start",
            trace_id=trace_id,
            trace_name=trace_name
        )
        
        return trace_id
    
    def end_trace(self):
        """结束当前追踪上下文"""
        if not hasattr(self._local, 'trace_id'):
            return
        
        duration_ms = (time.time() - self._local.trace_start_time) * 1000
        
        self.logger.info(
            f"Trace ended: {self._local.trace_name}",
            event="trace_end",
            trace_id=self._local.trace_id,
            trace_name=self._local.trace_name,
            total_duration_ms=round(duration_ms, 2)
        )
        
        # 清理上下文
        del self._local.trace_id
        del self._local.trace_name
        del self._local.trace_start_time
    
    def info(self, message: str, **kwargs):
        """记录带追踪上下文的信息日志"""
        if hasattr(self._local, 'trace_id'):
            kwargs.update({
                'trace_id': self._local.trace_id,
                'trace_name': self._local.trace_name
            })
        self.logger.info(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录带追踪上下文的错误日志"""
        if hasattr(self._local, 'trace_id'):
            kwargs.update({
                'trace_id': self._local.trace_id,
                'trace_name': self._local.trace_name
            })
        self.logger.error(message, **kwargs)


# 全局日志记录器缓存
_logger_cache: Dict[str, StructuredLogger] = {}
_performance_logger_cache: Dict[str, PerformanceLogger] = {}
_trace_logger_cache: Dict[str, TraceLogger] = {}


def get_logger(name: str) -> StructuredLogger:
    """
    获取结构化日志记录器（单例模式）
    
    Args:
        name: 日志记录器名称
        
    Returns:
        StructuredLogger实例
    """
    if name not in _logger_cache:
        _logger_cache[name] = StructuredLogger(name)
    return _logger_cache[name]


def get_performance_logger(name: str) -> PerformanceLogger:
    """
    获取性能监控日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        PerformanceLogger实例
    """
    if name not in _performance_logger_cache:
        _performance_logger_cache[name] = PerformanceLogger(name)
    return _performance_logger_cache[name]


def get_trace_logger(name: str) -> TraceLogger:
    """
    获取追踪日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        TraceLogger实例
    """
    if name not in _trace_logger_cache:
        _trace_logger_cache[name] = TraceLogger(name)
    return _trace_logger_cache[name]


def configure_logging_from_settings(settings):
    """
    从配置文件配置日志系统
    
    Args:
        settings: Settings配置实例
    """
    # 这里可以根据settings配置全局日志行为
    # 暂时简单实现，后续可以扩展
    pass


# 使用示例：
# logger = get_logger("my_module")
# logger.info("用户登录", user_id="123", ip="***********")
#
# perf_logger = get_performance_logger("api")
# with perf_logger.measure("database_query"):
#     # 执行数据库查询
#     pass
#
# trace_logger = get_trace_logger("request")
# trace_id = trace_logger.start_trace("user_registration")
# trace_logger.info("验证用户输入", step="validation")
# trace_logger.end_trace() 