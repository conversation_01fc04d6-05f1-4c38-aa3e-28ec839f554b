#!/usr/bin/env python3
"""
测试前端行程显示功能
验证修复后的前端能否正确显示完整的15个活动
"""
import asyncio
from playwright.async_api import async_playwright
import time

async def test_frontend_itinerary_display():
    """测试前端行程显示功能"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 监听控制台消息
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"[{msg.type}] {msg.text}"))
        
        try:
            print("🚀 开始测试前端行程显示功能...")
            
            # 1. 导航到页面
            await page.goto("http://localhost:8000/static/travel_planner_v3_refactored.html")
            await page.wait_for_load_state("networkidle")
            print("✅ 页面加载完成")
            
            # 2. 输入测试数据
            await page.fill('#userQuery',
                           "我想去北京玩3天，喜欢历史文化景点，需要自驾出行，预算中等")
            print("✅ 输入旅行需求")
            
            # 3. 开始分析
            await page.click('button:has-text("开始分析")')
            print("✅ 点击开始分析")
            
            # 4. 等待分析完成（等待"立即规划"按钮出现）
            print("⏳ 等待分析完成...")
            await page.wait_for_selector('button:has-text("立即规划")', timeout=120000)
            print("✅ 分析完成，立即规划按钮出现")
            
            # 5. 检查右侧面板的行程显示
            print("\n🔍 检查行程显示...")
            
            # 等待一下确保所有事件都处理完成
            await page.wait_for_timeout(3000)
            
            # 检查是否有行程容器
            daily_itinerary = await page.query_selector('#dailyItinerary')
            if daily_itinerary:
                print("✅ 找到行程容器")
                
                # 检查天数容器
                day_containers = await page.query_selector_all('.day-container')
                print(f"📅 找到 {len(day_containers)} 个天数容器")
                
                total_activities = 0
                for i, day_container in enumerate(day_containers, 1):
                    # 获取天数标题
                    day_title = await day_container.query_selector('.day-title')
                    day_title_text = await day_title.inner_text() if day_title else f"第{i}天"
                    
                    # 获取活动卡片
                    activity_cards = await day_container.query_selector_all('.activity-card')
                    activity_count = len(activity_cards)
                    total_activities += activity_count
                    
                    print(f"  {day_title_text}: {activity_count}个活动")
                    
                    # 检查每个活动的详细信息
                    for j, card in enumerate(activity_cards, 1):
                        # 获取活动名称
                        name_element = await card.query_selector('.card-title')
                        name = await name_element.inner_text() if name_element else "未知活动"
                        
                        # 获取时间信息
                        time_badge = await card.query_selector('.time-badge')
                        start_time = await time_badge.inner_text() if time_badge else ""
                        
                        time_end = await card.query_selector('.time-badge-end')
                        end_time = await time_end.inner_text() if time_end else ""
                        
                        # 获取活动类型
                        type_badge = await card.query_selector('.badge')
                        activity_type = await type_badge.inner_text() if type_badge else ""
                        
                        print(f"    {j}. {name.strip()} ({activity_type}) {start_time} {end_time}")
                
                print(f"\n📊 总计: {total_activities}个活动")
                
                # 验证是否达到预期的15个活动
                if total_activities >= 15:
                    print("✅ 活动数量符合预期（≥15个）")
                elif total_activities >= 10:
                    print("⚠️ 活动数量基本符合预期（≥10个）")
                else:
                    print(f"❌ 活动数量不足（只有{total_activities}个，预期15个）")
                
                # 检查统计信息
                total_days_element = await page.query_selector('#totalDays')
                total_activities_element = await page.query_selector('#totalActivities')
                
                if total_days_element and total_activities_element:
                    total_days = await total_days_element.inner_text()
                    total_activities_stat = await total_activities_element.inner_text()
                    print(f"📈 统计信息: {total_days}天, {total_activities_stat}个活动")
                
            else:
                print("❌ 未找到行程容器")
            
            # 6. 检查控制台消息中的行程更新事件
            print(f"\n🔍 控制台消息分析:")
            itinerary_update_count = 0
            for msg in console_messages:
                if "收到行程更新" in msg:
                    itinerary_update_count += 1
                elif "添加活动到第" in msg:
                    print(f"  {msg}")
            
            print(f"📊 收到 {itinerary_update_count} 个行程更新事件")
            
            # 7. 测试结果总结
            print(f"\n🎯 测试结果总结:")
            print(f"  - 行程更新事件: {itinerary_update_count}个")
            print(f"  - 显示的活动: {total_activities}个")
            print(f"  - 显示的天数: {len(day_containers)}天")
            
            if total_activities >= 15 and len(day_containers) == 3:
                print("🎉 测试通过！前端正确显示了完整的行程")
            elif total_activities >= 10:
                print("⚠️ 测试部分通过，但活动数量可能不完整")
            else:
                print("❌ 测试失败，前端行程显示有问题")
            
            # 保持浏览器打开一段时间供检查
            print("\n⏳ 保持浏览器打开30秒供手动检查...")
            await page.wait_for_timeout(30000)
            
        except Exception as e:
            print(f"💥 测试异常: {str(e)}")
            
            # 输出控制台消息帮助调试
            print(f"\n🔍 控制台消息:")
            for msg in console_messages[-20:]:  # 只显示最后20条
                print(f"  {msg}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_frontend_itinerary_display())
