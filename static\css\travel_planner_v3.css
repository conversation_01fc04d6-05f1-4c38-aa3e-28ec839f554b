/* 旅行规划系统V3重构版样式文件 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.container-fluid {
    padding: 20px;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
    border: none;
}

.card-header h4 {
    margin: 0;
    font-weight: 600;
}

.card-header small {
    opacity: 0.9;
}

/* 分析步骤样式 */
.analysis-step {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    background: #ffffff;
    transition: all 0.3s ease;
}

.analysis-step:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.analysis-step.completed {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.analysis-step.running {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
    animation: pulse 2s infinite;
}

.analysis-step.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.analysis-step-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.analysis-step-title {
    font-weight: 600;
    color: #495057;
}

.analysis-step-status {
    font-size: 0.9em;
    padding: 2px 8px;
    border-radius: 12px;
    background: #6c757d;
    color: white;
}

.analysis-step-status.completed {
    background: #28a745;
}

.analysis-step-status.running {
    background: #ffc107;
    color: #212529;
}

.analysis-step-status.error {
    background: #dc3545;
}

.analysis-step-content {
    color: #6c757d;
    font-size: 0.95em;
    line-height: 1.5;
}

/* 占位符样式 */
.analysis-placeholder {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.analysis-placeholder i {
    font-size: 3em;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* 控制按钮样式 */
.control-buttons {
    margin: 20px 0;
}

.control-buttons button {
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.control-buttons button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 车辆信息面板 */
#vehicleInfoPanel {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

#vehicleInfoPanel h6 {
    color: #495057;
    font-weight: 600;
}

/* 语音控制样式 */
.voice-controls {
    margin: 15px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.voice-controls button {
    margin-right: 8px;
    border-radius: 20px;
    font-size: 0.85em;
}

/* 进度文本样式 */
.progress-text {
    font-weight: 600;
    color: #0d6efd;
    margin: 15px 0;
    padding: 10px;
    background: #e7f3ff;
    border-radius: 8px;
    border-left: 4px solid #0d6efd;
}

/* 错误消息样式 */
.error-message {
    color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #dc3545;
}

.error-message i {
    margin-right: 8px;
}

/* 策略确认样式 */
#strategyConfirmation {
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border: 1px solid #b6d4d9;
    border-radius: 12px;
    border-left: 4px solid #17a2b8;
}

#strategyConfirmation h6 {
    color: #0c5460;
    font-weight: 600;
    margin-bottom: 10px;
}

#strategyConfirmation p {
    color: #0c5460;
    margin-bottom: 15px;
}

/* 规划视图样式 */
#planningView {
    min-height: 400px;
}

.itinerary-day {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.itinerary-day-header {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
}

.itinerary-activity {
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s ease;
}

.itinerary-activity:hover {
    background-color: #f8f9fa;
}

.itinerary-activity:last-child {
    border-bottom: none;
}

.activity-time {
    font-weight: 600;
    color: #0d6efd;
    font-size: 0.9em;
}

.activity-name {
    font-weight: 600;
    color: #495057;
    margin: 5px 0;
}

.activity-description {
    color: #6c757d;
    font-size: 0.9em;
    line-height: 1.4;
}

.activity-location {
    color: #28a745;
    font-size: 0.85em;
    margin-top: 5px;
}

/* 视图切换按钮 */
.btn-group .btn {
    border-radius: 25px;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .control-buttons button {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .voice-controls button {
        width: calc(33.33% - 5px);
        margin-right: 5px;
    }
    
    #vehicleInfoPanel .row .col-md-6 {
        margin-bottom: 15px;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
