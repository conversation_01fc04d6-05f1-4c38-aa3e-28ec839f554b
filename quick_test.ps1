#!/usr/bin/env powershell
# AutoPilot AI 快速测试脚本

Write-Host "🚀 AutoPilot AI 快速测试" -ForegroundColor Magenta
Write-Host "=" * 50 -ForegroundColor Gray

# 1. 检查虚拟环境
Write-Host "📋 检查虚拟环境..." -ForegroundColor Blue
if ($env:VIRTUAL_ENV) {
    Write-Host "✅ 虚拟环境已激活: $env:VIRTUAL_ENV" -ForegroundColor Green
} else {
    Write-Host "❌ 虚拟环境未激活" -ForegroundColor Red
    Write-Host "请运行: .\.venv\Scripts\Activate.ps1" -ForegroundColor Yellow
    exit 1
}

# 2. 设置测试环境变量
Write-Host "🔧 设置环境变量..." -ForegroundColor Blue
$env:REASONING_LLM_MODEL = "glm-z1-flash"
$env:REASONING_LLM_API_KEY = "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
$env:REASONING_LLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4/"
$env:BASIC_LLM_MODEL = "glm-z1-flash"
$env:BASIC_LLM_API_KEY = "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
$env:BASIC_LLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4/"
$env:LOG_LEVEL = "INFO"
$env:LOG_FORMAT = "text"
Write-Host "✅ 环境变量设置完成" -ForegroundColor Green

# 3. 快速模块测试
Write-Host "🧪 测试核心模块..." -ForegroundColor Blue

Write-Host "  📁 配置模块..." -NoNewline
try {
    python -c "from src.core.config import get_settings; s = get_settings(); print(f'项目名: {s.project_name}')"
    Write-Host " ✅" -ForegroundColor Green
} catch {
    Write-Host " ❌" -ForegroundColor Red
}

Write-Host "  📝 日志模块..." -NoNewline
try {
    python -c "from src.core.logger import get_logger; logger = get_logger('test'); logger.info('测试消息'); print('日志系统正常')"
    Write-Host " ✅" -ForegroundColor Green
} catch {
    Write-Host " ❌" -ForegroundColor Red
}

Write-Host "  🤖 LLM管理器..." -NoNewline
try {
    python -c "from src.core.llm_manager import get_default_manager; print('LLM管理器正常')"
    Write-Host " ✅" -ForegroundColor Green
} catch {
    Write-Host " ❌" -ForegroundColor Red
}

# 4. Agent测试
Write-Host "🤖 测试Agent系统..." -ForegroundColor Blue

Write-Host "  🔸 基础Agent..." -NoNewline
try {
    python -c "import asyncio; from src.agents.simple_assistant import quick_chat; result = asyncio.run(quick_chat('Hello')); print('Agent响应正常')"
    Write-Host " ✅" -ForegroundColor Green
} catch {
    Write-Host " ❌" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 快速测试完成！" -ForegroundColor Green

# 5. 运行单元测试（可选）
$runFullTests = Read-Host "是否运行完整单元测试? (y/N)"
if ($runFullTests -eq "y" -or $runFullTests -eq "Y") {
    Write-Host "🧪 运行单元测试..." -ForegroundColor Blue
    pytest tests/ -v --cov=src --cov-report=term-missing
}

Write-Host ""
Write-Host "📚 下一步操作建议:" -ForegroundColor Cyan
Write-Host "  1. 运行完整测试: pytest tests/ -v" -ForegroundColor Gray
Write-Host "  2. 运行Agent测试: python test_agent.py" -ForegroundColor Gray
Write-Host "  3. 查看测试覆盖率: pytest --cov=src --cov-report=html" -ForegroundColor Gray 