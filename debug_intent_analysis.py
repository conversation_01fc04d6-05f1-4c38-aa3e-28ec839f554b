#!/usr/bin/env python3
"""
意图分析数据一致性诊断脚本

用于诊断为什么用户输入"莆田"却得到"北京"景点的问题
"""

import asyncio
import json
from datetime import datetime
from src.core.logger import get_logger
from src.agents.services.analysis_service import AnalysisService
from src.agents.services.reasoning_service import ReasoningService

logger = get_logger("debug_intent")

async def test_intent_analysis():
    """测试意图分析的完整流程"""
    print("🔍 意图分析数据一致性诊断")
    print("=" * 60)
    
    # 测试用例：用户的真实输入
    test_query = "我在福州闽东大厦，这周末要去莆田玩两天"
    user_id = "test_user_1"
    
    print(f"📝 用户输入: {test_query}")
    print(f"👤 用户ID: {user_id}")
    print()
    
    try:
        # 步骤1: 测试核心意图分析
        print("🎯 步骤1: 核心意图分析")
        print("-" * 40)
        
        analysis_service = AnalysisService()
        core_intent = await analysis_service.analyze_core_intent(
            original_query=test_query,
            user_id=user_id,
            user_profile={}
        )
        
        print("✅ 核心意图分析结果:")
        print(json.dumps(core_intent, ensure_ascii=False, indent=2))
        
        # 检查目的地是否正确
        destinations = core_intent.get("destinations", [])
        print(f"\n🎯 识别的目的地: {destinations}")
        
        if "莆田" in destinations:
            print("✅ 正确识别了莆田作为目的地")
        else:
            print("❌ 未能正确识别莆田作为目的地")
            print("⚠️ 这是问题的根源！")
        
        print()
        
        # 步骤2: 测试偏好分析
        print("🎨 步骤2: 偏好分析")
        print("-" * 40)

        preference_analysis = await analysis_service.analyze_preferences(
            core_intent=core_intent,
            user_profile={},
            user_memories=[]
        )
        
        print("✅ 偏好分析结果:")
        print(json.dumps(preference_analysis, ensure_ascii=False, indent=2))
        print()
        
        # 步骤3: 测试整合结果
        print("🔗 步骤3: 意图整合")
        print("-" * 40)
        
        # 模拟整合过程
        consolidated_intent = {
            "destinations": core_intent.get("destinations", []),
            "days": core_intent.get("days", 2),
            "travel_theme": core_intent.get("travel_theme", "leisure"),
            "preferences": preference_analysis.get("preferences", {})
        }
        
        print("✅ 整合后的意图:")
        print(json.dumps(consolidated_intent, ensure_ascii=False, indent=2))
        
        # 检查整合后的目的地
        final_destinations = consolidated_intent.get("destinations", [])
        print(f"\n🎯 整合后的目的地: {final_destinations}")
        
        if "莆田" in final_destinations:
            print("✅ 整合过程保持了正确的目的地")
        else:
            print("❌ 整合过程中丢失了正确的目的地")
            print("⚠️ 这可能是数据传递问题！")
        
        return consolidated_intent
        
    except Exception as e:
        print(f"❌ 意图分析过程出错: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

async def test_poi_search_with_correct_city():
    """测试使用正确城市进行POI搜索"""
    print("\n🏛️ POI搜索测试")
    print("=" * 60)
    
    try:
        from src.tools.travel_planner.amap_poi_tools import search_poi
        
        # 测试莆田的景点搜索
        print("🔍 搜索莆田的景点...")
        putian_pois = await search_poi(
            keywords="景点",
            city="莆田",
            limit=5
        )
        
        print(f"✅ 莆田景点搜索结果 ({len(putian_pois)}个):")
        for i, poi in enumerate(putian_pois[:3]):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            print(f"  {i+1}. {name} - {address}")
        
        # 对比：搜索北京的景点
        print("\n🔍 对比：搜索北京的景点...")
        beijing_pois = await search_poi(
            keywords="景点",
            city="北京", 
            limit=5
        )
        
        print(f"✅ 北京景点搜索结果 ({len(beijing_pois)}个):")
        for i, poi in enumerate(beijing_pois[:3]):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            print(f"  {i+1}. {name} - {address}")
        
        # 分析差异
        print("\n📊 结果分析:")
        putian_names = [poi.get("name", "") for poi in putian_pois]
        beijing_names = [poi.get("name", "") for poi in beijing_pois]
        
        if any("莆田" in name or "湄洲" in name or "妈祖" in name for name in putian_names):
            print("✅ 莆田搜索返回了正确的本地景点")
        else:
            print("⚠️ 莆田搜索结果可能不够准确")
            
        if any("北京" in name or "天安门" in name or "故宫" in name for name in beijing_names):
            print("✅ 北京搜索返回了正确的本地景点")
        else:
            print("⚠️ 北京搜索结果可能不够准确")
            
    except Exception as e:
        print(f"❌ POI搜索测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

async def test_default_fallback_logic():
    """测试默认值回退逻辑"""
    print("\n🔄 默认值回退逻辑测试")
    print("=" * 60)
    
    # 模拟各种可能导致使用默认值的情况
    test_cases = [
        {"destinations": [], "expected_fallback": "北京"},
        {"destinations": None, "expected_fallback": "北京"},
        {"destinations": [""], "expected_fallback": "北京"},
        {"destinations": ["莆田"], "expected_fallback": "莆田"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case}")
        
        # 模拟consolidated_intent.get("destinations", ["北京"])的逻辑
        destinations = case["destinations"]
        fallback_result = destinations[0] if destinations and destinations[0] else "北京"
        
        print(f"  结果: {fallback_result}")
        print(f"  预期: {case['expected_fallback']}")
        
        if fallback_result == case["expected_fallback"]:
            print("  ✅ 回退逻辑正确")
        else:
            print("  ❌ 回退逻辑有问题")

async def main():
    """主函数"""
    print("🚨 意图分析数据一致性问题诊断")
    print("=" * 80)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行所有测试
    consolidated_intent = await test_intent_analysis()
    await test_poi_search_with_correct_city()
    await test_default_fallback_logic()
    
    print("\n" + "=" * 80)
    print("🎯 诊断总结")
    print("=" * 80)
    
    if consolidated_intent:
        destinations = consolidated_intent.get("destinations", [])
        if "莆田" in destinations:
            print("✅ 意图分析正确识别了莆田")
            print("❓ 问题可能在数据传递或POI搜索阶段")
        else:
            print("❌ 意图分析未能正确识别莆田")
            print("🔧 需要修复意图分析的城市识别逻辑")
    else:
        print("❌ 意图分析过程失败")
        print("🔧 需要检查LLM配置和API调用")
    
    print("\n📋 建议的修复步骤:")
    print("1. 检查意图分析的提示词模板")
    print("2. 验证LLM API调用是否正常")
    print("3. 修复默认值回退逻辑")
    print("4. 确保数据传递链路的完整性")
    print("5. 添加更多的城市识别模式")

if __name__ == "__main__":
    asyncio.run(main())
