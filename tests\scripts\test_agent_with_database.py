#!/usr/bin/env python3
"""
测试改造后的TravelPlannerAgent数据库集成功能
"""
import asyncio
import sys
import json
import uuid
import traceback
from datetime import datetime

# 添加项目路径
sys.path.append('.')

from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import TravelPlanRequest
from src.core.logger import get_logger
from src.database.mysql_client import get_db_cursor
from src.models.mysql_crud import user_crud, user_memory_crud, user_summary_crud

logger = get_logger(__name__)

async def create_test_user_data():
    """创建测试用户数据"""
    test_user_id = "test_user_001"
    
    try:
        async with get_db_cursor() as cursor:
            # 1. 创建测试用户（如果不存在）
            existing_user = await user_crud.get(cursor, test_user_id)
            if not existing_user:
                user_data = {
                    'id': test_user_id,
                    'nickname': '测试用户',
                    'email': '<EMAIL>',
                    'status': 'ACTIVE',
                    'created_at': datetime.now()
                }
                await user_crud.create(cursor, user_data)
                print(f"✅ 创建测试用户: {test_user_id}")
            else:
                print(f"✅ 测试用户已存在: {test_user_id}")
            
            # 2. 创建测试用户记忆
            test_memories = [
                {
                    'user_id': test_user_id,
                    'content': '用户喜欢历史文化景点，特别是博物馆和古迹',
                    'memory_type': 'preference',
                    'keywords': json.dumps(['历史', '文化', '博物馆'], ensure_ascii=False),
                    'confidence_score': 0.9,
                    'source': 'user_behavior',
                    'created_at': datetime.now(),
                    'last_accessed_at': datetime.now()
                },
                {
                    'user_id': test_user_id,
                    'content': '用户喜欢尝试当地美食，对川菜和粤菜有偏好',
                    'memory_type': 'preference',
                    'keywords': json.dumps(['美食', '川菜', '粤菜'], ensure_ascii=False),
                    'confidence_score': 0.8,
                    'source': 'user_behavior',
                    'created_at': datetime.now(),
                    'last_accessed_at': datetime.now()
                },
                {
                    'user_id': test_user_id,
                    'content': '用户预算偏好中等，不喜欢过于奢华的场所',
                    'memory_type': 'preference',
                    'keywords': json.dumps(['预算', '中等', '经济实用'], ensure_ascii=False),
                    'confidence_score': 0.7,
                    'source': 'user_feedback',
                    'created_at': datetime.now(),
                    'last_accessed_at': datetime.now()
                }
            ]
            
            for memory in test_memories:
                await user_memory_crud.create(cursor, memory)
            
            print(f"✅ 创建了 {len(test_memories)} 条测试记忆")
            
            # 3. 创建用户画像总结
            summary_data = {
                'user_id': test_user_id,
                'interests': json.dumps(['历史', '文化', '美食', '博物馆'], ensure_ascii=False),
                'travel_style': '文化深度游',
                'budget_preference': '中等',
                'personality_traits': json.dumps(['好奇心强', '喜欢学习'], ensure_ascii=False),
                'updated_at': datetime.now()
            }
            
            await user_summary_crud.create(cursor, summary_data)
            print(f"✅ 创建用户画像总结")
            
            return test_user_id
            
    except Exception as e:
        print(f"❌ 创建测试数据失败: {str(e)}")
        traceback.print_exc()
        return None

async def test_agent_with_database():
    """测试Agent与数据库的集成"""
    print("🔍 测试TravelPlannerAgent数据库集成功能")
    print("=" * 60)
    
    # 1. 创建测试数据
    print("1. 创建测试用户数据...")
    test_user_id = await create_test_user_data()
    if not test_user_id:
        print("❌ 无法创建测试数据，退出测试")
        return False
    
    print()
    
    # 2. 初始化Agent
    print("2. 初始化TravelPlannerAgent...")
    try:
        agent = TravelPlannerAgent()
        print("✅ Agent初始化成功")
    except Exception as e:
        print(f"❌ Agent初始化失败: {str(e)}")
        return False
    
    print()
    
    # 3. 创建测试请求
    print("3. 创建旅行规划请求...")
    request = TravelPlanRequest(
        user_id=test_user_id,
        query="我想去西安玩3天，看看历史文化景点"
    )
    print(f"✅ 请求创建成功: {request.query}")
    print()
    
    # 4. 执行旅行规划
    print("4. 执行旅行规划...")
    try:
        events_received = []
        user_profile_loaded = False
        memory_saved = False
        final_itinerary_generated = False
        
        async for event in agent.plan_travel(request):
            events_received.append(event)
            
            # 检查关键事件
            if event.event_type.value == "thinking_step":
                content = event.payload.get('content', '')
                print(f"   💭 {content}")
                
                if "用户完整画像" in content:
                    user_profile_loaded = True
                    print(f"   ✅ 用户画像加载: {content}")
                
                if "保存记忆" in content:
                    memory_saved = True
                    print(f"   ✅ 记忆保存: {content}")
            
            elif event.event_type.value == "final_itinerary":
                final_itinerary_generated = True
                itinerary = event.payload
                print(f"   ✅ 最终行程: {itinerary.get('summary', {}).get('title', '未知标题')}")
        
        print(f"\n📊 事件统计:")
        print(f"   总事件数: {len(events_received)}")
        print(f"   用户画像加载: {'✅' if user_profile_loaded else '❌'}")
        print(f"   记忆保存: {'✅' if memory_saved else '❌'}")
        print(f"   最终行程生成: {'✅' if final_itinerary_generated else '❌'}")
        
        return user_profile_loaded and memory_saved and final_itinerary_generated
        
    except Exception as e:
        print(f"❌ 旅行规划执行失败: {str(e)}")
        traceback.print_exc()
        return False

async def verify_saved_data(user_id: str):
    """验证保存的数据"""
    print("\n5. 验证保存的数据...")
    
    try:
        async with get_db_cursor() as cursor:
            # 检查新增的记忆
            memories = await user_memory_crud.get_by_user(cursor, user_id=user_id, limit=10)
            print(f"   📝 用户记忆总数: {len(memories)}")
            
            # 检查最新记忆
            latest_memory = None
            for memory in memories:
                if hasattr(memory, 'memory_type') and memory.memory_type == 'travel_planning':
                    latest_memory = memory
                    break
            
            if latest_memory:
                print(f"   ✅ 找到旅行规划记忆: {latest_memory.content}")
            else:
                print(f"   ❌ 未找到旅行规划记忆")
            
            # 检查更新的用户画像
            summary = await user_summary_crud.get_by_user(cursor, user_id=user_id)
            if summary:
                interests = summary.interests
                if isinstance(interests, str):
                    try:
                        interests = json.loads(interests)
                    except:
                        interests = [interests]
                print(f"   ✅ 用户兴趣标签: {interests}")
            else:
                print(f"   ❌ 未找到用户画像总结")
            
            return latest_memory is not None and summary is not None
            
    except Exception as e:
        print(f"❌ 验证数据失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🧪 AutoPilot AI Agent数据库集成测试")
    print("=" * 60)
    
    success = False
    test_user_id = None
    
    try:
        # 执行Agent测试
        success = await test_agent_with_database()
        
        # 如果Agent测试成功，验证保存的数据
        if success:
            test_user_id = "test_user_001"
            data_verified = await verify_saved_data(test_user_id)
            success = success and data_verified
        
    except Exception as e:
        print(f"💥 测试过程中发生异常: {str(e)}")
        traceback.print_exc()
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 数据库集成测试通过！")
        print("✅ 用户画像读取正常")
        print("✅ 用户记忆加载正常") 
        print("✅ 记忆保存功能正常")
        print("✅ 画像更新功能正常")
        return 0
    else:
        print("❌ 数据库集成测试失败")
        print("⚠️ 请检查错误信息并修复问题")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⛔ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"💥 程序异常: {str(e)}")
        traceback.print_exc()
        sys.exit(1) 