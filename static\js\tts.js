/**
 * TTS (Text-to-Speech) 播报功能
 * 
 * 提供语音播报功能，支持中文语音合成
 */

class TTSManager {
    constructor() {
        this.isEnabled = false;
        this.isPlaying = false;
        this.currentUtterance = null;
        this.queue = [];
        
        // 检查浏览器支持
        this.isSupported = 'speechSynthesis' in window;
        
        // 语音配置
        this.voiceConfig = {
            lang: 'zh-CN',
            rate: 0.9,
            pitch: 1.0,
            volume: 0.8
        };
        
        this.init();
    }
    
    init() {
        if (!this.isSupported) {
            console.warn('当前浏览器不支持语音合成功能');
            return;
        }
        
        // 绑定事件
        this.bindEvents();
        
        // 等待语音列表加载
        this.loadVoices();
    }
    
    bindEvents() {
        // TTS开关按钮
        const ttsToggle = document.getElementById('ttsToggle');
        if (ttsToggle) {
            ttsToggle.addEventListener('click', () => {
                this.toggle();
            });
        }
        
        // 停止播报按钮
        const stopTTS = document.getElementById('stopTTS');
        if (stopTTS) {
            stopTTS.addEventListener('click', () => {
                this.stop();
            });
        }
        
        // 监听语音合成事件
        if (this.isSupported) {
            speechSynthesis.addEventListener('voiceschanged', () => {
                this.loadVoices();
            });
        }
    }
    
    loadVoices() {
        if (!this.isSupported) return;
        
        const voices = speechSynthesis.getVoices();
        
        // 优先选择中文语音
        this.selectedVoice = voices.find(voice => 
            voice.lang.includes('zh') || 
            voice.lang.includes('CN') ||
            voice.name.includes('Chinese')
        ) || voices[0];
        
        console.log('TTS语音已加载:', this.selectedVoice?.name || '默认语音');
    }
    
    toggle() {
        this.isEnabled = !this.isEnabled;
        
        const toggleBtn = document.getElementById('ttsToggle');
        if (toggleBtn) {
            if (this.isEnabled) {
                toggleBtn.classList.remove('btn-outline-secondary');
                toggleBtn.classList.add('btn-success');
                toggleBtn.innerHTML = '<i class="bi bi-volume-up"></i> 语音播报';
                this.speak('语音播报已开启');
            } else {
                toggleBtn.classList.remove('btn-success');
                toggleBtn.classList.add('btn-outline-secondary');
                toggleBtn.innerHTML = '<i class="bi bi-volume-mute"></i> 语音播报';
                this.stop();
            }
        }
        
        console.log('TTS播报', this.isEnabled ? '已开启' : '已关闭');
    }
    
    speak(text, options = {}) {
        if (!this.isSupported || !this.isEnabled || !text) {
            return Promise.resolve();
        }
        
        return new Promise((resolve, reject) => {
            // 清理文本
            const cleanText = this.cleanText(text);
            if (!cleanText) {
                resolve();
                return;
            }
            
            // 创建语音合成实例
            const utterance = new SpeechSynthesisUtterance(cleanText);
            
            // 配置语音参数
            utterance.voice = this.selectedVoice;
            utterance.lang = options.lang || this.voiceConfig.lang;
            utterance.rate = options.rate || this.voiceConfig.rate;
            utterance.pitch = options.pitch || this.voiceConfig.pitch;
            utterance.volume = options.volume || this.voiceConfig.volume;
            
            // 事件监听
            utterance.onstart = () => {
                this.isPlaying = true;
                this.currentUtterance = utterance;
                this.showTTSIndicator(cleanText);
                console.log('开始播报:', cleanText.substring(0, 50) + '...');
            };
            
            utterance.onend = () => {
                this.isPlaying = false;
                this.currentUtterance = null;
                this.hideTTSIndicator();
                resolve();
            };
            
            utterance.onerror = (event) => {
                this.isPlaying = false;
                this.currentUtterance = null;
                this.hideTTSIndicator();
                console.error('TTS播报错误:', event.error);
                reject(event.error);
            };
            
            // 开始播报
            speechSynthesis.speak(utterance);
        });
    }
    
    stop() {
        if (!this.isSupported) return;
        
        speechSynthesis.cancel();
        this.isPlaying = false;
        this.currentUtterance = null;
        this.queue = [];
        this.hideTTSIndicator();
        
        console.log('TTS播报已停止');
    }
    
    pause() {
        if (!this.isSupported) return;
        
        speechSynthesis.pause();
        console.log('TTS播报已暂停');
    }
    
    resume() {
        if (!this.isSupported) return;
        
        speechSynthesis.resume();
        console.log('TTS播报已恢复');
    }
    
    cleanText(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }
        
        return text
            // 移除HTML标签
            .replace(/<[^>]*>/g, '')
            // 移除特殊字符
            .replace(/[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\s，。！？；：""''（）【】]/g, '')
            // 替换多个空格为单个空格
            .replace(/\s+/g, ' ')
            // 去除首尾空格
            .trim();
    }
    
    showTTSIndicator(text) {
        const indicator = document.getElementById('ttsIndicator');
        if (indicator) {
            const textElement = indicator.querySelector('.tts-text');
            if (textElement) {
                textElement.textContent = text.length > 20 ? text.substring(0, 20) + '...' : text;
            }
            indicator.style.display = 'block';
        }
    }
    
    hideTTSIndicator() {
        const indicator = document.getElementById('ttsIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    // 队列播报功能
    async speakQueue(texts) {
        if (!Array.isArray(texts)) {
            texts = [texts];
        }
        
        for (const text of texts) {
            if (this.isEnabled) {
                await this.speak(text);
                // 短暂停顿
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
    }
    
    // 播报分析步骤
    speakAnalysisStep(title, content) {
        if (!this.isEnabled) return;
        
        const text = `${title}：${content}`;
        this.speak(text);
    }
    
    // 播报状态更新
    speakStatusUpdate(status) {
        if (!this.isEnabled) return;
        
        this.speak(status);
    }
    
    // 播报行程信息
    speakItineraryInfo(title, description) {
        if (!this.isEnabled) return;
        
        const text = `${title}。${description}`;
        this.speak(text);
    }
    
    // 获取TTS状态
    getStatus() {
        return {
            isSupported: this.isSupported,
            isEnabled: this.isEnabled,
            isPlaying: this.isPlaying,
            selectedVoice: this.selectedVoice?.name || '默认语音'
        };
    }
}

// 创建全局TTS管理器实例
window.ttsManager = new TTSManager();

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TTSManager;
}
