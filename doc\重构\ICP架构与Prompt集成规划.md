# ICP架构与Prompt集成规划 (V3.1)

## 1. 核心定位：Prompt即Agent的“大脑”

首先必须明确，`src/prompts/travel_planner/04_itinerary_generator.md` 文件并非一个用于一次性生成完整行程的简单Prompt。它是一套复杂的 **规则和决策指令集**，其真正的角色是作为 `规划距离逻辑.md` 中定义的 **“迭代式上下文规划”（ICP）架构** 的核心——`planner_agent`（规划器Agent）的 **“大脑”或“行动手册”**。

`planner_agent` 在ICP的每一次循环中，都会参照这个Prompt里的指令，根据当前的上下文（*我刚刚规划了什么？现在几点了？预算还剩多少？*），来决定下一步最合理的操作是什么（*是该搜索周边的餐厅了，还是该规划下一个景点了？*）。

**结论：** `04_itinerary_generator.md` 的设计是先进且正确的，我们不需要修改它。相反，我们需要升级我们的代码，构建一个能够完全理解并执行这份“行动手册”的强大Agent。

## 2. 代码实现差距分析 (Gap Analysis)

当前代码库虽然有了基础的工具和服务，但尚未完全实现ICP架构，无法支撑`planner_agent`依据`04_itinerary_generator.md`进行复杂的迭代式规划。主要差距体现在以下几个方面：

| 缺失组件 | 期望实现路径 | 现状 | 影响 |
| :--- | :--- | :--- | :--- |
| **核心规划循环** | `src/agents/travel_planner_lg/graph.py` | 缺失 | 无法实现“思考->行动->观察”的迭代规划模式。 |
| **规划器Agent节点** | `src/agents/travel_planner_lg/nodes.py` | 缺失 | Agent没有“大脑”的执行单元，无法使用Prompt进行决策。 |
| **详细信息获取工具** | `src/tools/travel_planner/amap_poi_tools.py` | `get_poi_details`缺失 | Agent无法获取POI的详细评分、电话等信息，导致`EnrichedPOI`数据不完整。 |
| **图片获取工具** | `src/tools/travel_planner/amap_poi_tools.py` | `get_poi_images`缺失 | Agent无法获取POI图片，影响最终行程的展示效果。 |
| **最终格式定义工具** | `src/tools/travel_planner/consolidated_tools.py` | `define_final_itinerary_schema`缺失 | Agent无法确保最终生成的行程符合标准化的输出格式。 |

## 3. 开发实施路线图 (Actionable Roadmap)

为了弥合上述差距，让`04_itinerary_generator.md`这个强大的“大脑”能够真正驱动规划，需要按照以下步骤进行代码开发和补全。

### 任务一：补全核心`Action Tools`和`Planner Tools`

这是实现精细化规划的基础。

1.  **在 `src/tools/travel_planner/amap_poi_tools.py` 中**:
    *   **补充 `get_poi_details(poi_id: str)` 函数**:
        *   **功能**: 根据`poi_id`调用高德地图的地点详情API。
        *   **目标**: 获取更精确的`rating`, `phone_number`, `address`等信息。
        *   **注册**: 使用`@unified_registry.register_action_tool`装饰器进行注册。
    *   **补充 `get_poi_images(poi_id: str)` 函数**:
        *   **功能**: 调用高德或第三方API获取POI的图片URL列表。
        *   **目标**: 填充`EnrichedPOI`模型的`image_urls`字段。
        *   **注册**: 使用`@unified_registry.register_action_tool`装饰器进行注册。

2.  **在 `src/tools/travel_planner/consolidated_tools.py` 中**:
    *   **补充 `define_final_itinerary_schema()` 函数**:
        *   **功能**: 实现`规划距离逻辑.md`中13.6节定义的完整JSON Schema。
        *   **目标**: 为`planner_agent`在最后一步生成标准化的行程数据提供格式依据。
        *   **注册**: 使用`@unified_registry.register_planner_tool`装饰器进行注册。

### 任务二：构建ICP核心规划循环

这是让Agent“动起来”的关键。

1.  **在 `src/agents/travel_planner_lg/graph.py` 中**:
    *   **修改 `build_graph()` 方法**:
        *   **目标**: 按照`规划距离逻辑.md`中第8章的伪代码，构建一个以`planner_agent`为核心的条件循环图。
        *   **关键逻辑**:
            *   定义`planner_agent`和`tool_executor`节点。
            *   设置条件边，根据`planner_agent`的输出（是调用工具还是结束规划）进行路由。
            *   确保`tool_executor`执行后，流程返回`planner_agent`，形成闭环。

### 任务三：实现`planner_agent`的大脑和手脚

这是ICP架构的核心实现。

1.  **在 `src/agents/travel_planner_lg/nodes.py` 中**:
    *   **创建 `planner_agent_node(state)` 函数 (大脑)**:
        *   **功能**: 在ICP循环的每一步中，决定下一步行动。
        *   **核心逻辑**:
            1.  从`state`中提取当前规划上下文（已规划的POI、时间、预算等）。
            2.  从`UnifiedToolRegistry`获取所有可用的`Action Tools`的Schema。
            3.  **加载 `04_itinerary_generator.md` 的Prompt模板**。
            4.  将上下文和工具Schema注入到Prompt中。
            5.  调用`ReasoningService`执行LLM推理。
            6.  解析LLM返回的行动指令（如`{"tool_name": "search_around", "parameters": {...}}`），并更新到`state`中。
    *   **创建 `tool_executor_node(state)` 函数 (手脚)**:
        *   **功能**: 执行`planner_agent`决定的动作。
        *   **核心逻辑**:
            1.  从`state`中读取行动指令。
            2.  调用`unified_registry.execute_action_tool()`方法，传入工具名和参数来执行。
            3.  将工具返回的结果写回`state`中，供下一轮循环的“大脑”参考。

## 4. Prompt在ICP循环中的应用示例

为了更清晰地展示`04_itinerary_generator.md`如何驱动规划，以下是一个简化的伪代码示例，演示`planner_agent_node`的工作流程：

```python
# 伪代码: agetravel_planner_lg/nodes.py -> planner_agent_node

async def planner_agent_node(state: StandardAgentState):
    
    # 1. 准备给“大脑”的输入
    prompt_template = load_prompt("04_itinerary_generator.md")
    available_tools = unified_registry.get_all_action_schemas()
    current_plan = state.get("daily_plans")
    last_action_result = state.get("tool_results")

    # 2. 格式化Prompt，让AI知道当前所有情况
    # 注意：这里可能需要一个专门的Planner Tool来格式化这个复杂的Prompt
    formatted_prompt = format_icp_prompt(
        template=prompt_template,
        tools=available_tools,
        plan=current_plan,
        last_result=last_action_result,
        # ... 其他上下文 ...
    )
    
    # 3. 调用LLM进行“思考”，让“大脑”下达指令
    response_json = await reasoning_service.call_llm(formatted_prompt)
    
    # 4. 从“大脑”的输出中提取行动指令
    action = response_json.get("action")  # e.g., {"tool_name": "search_poi", "parameters": ...}
    thinking_log = response_json.get("thought") # e.g., "用户想去历史景点，我先搜索故宫"
    
    # 5. 更新状态，准备执行或结束
    state["current_action"] = action
    state["planning_log"].append(thinking_log)
    
    return state
```

通过以上步骤，我们就将一份静态的、规则丰富的Prompt文档，转化为了一个动态的、智能的、能够步步为营解决复杂规划问题的AI Agent的核心驱动力。
