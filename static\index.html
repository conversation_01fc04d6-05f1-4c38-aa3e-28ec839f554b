<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoPilot AI - 智能旅行规划</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style-refactored.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-airplane"></i>
                AutoPilot AI
            </a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-light btn-sm me-2" id="ttsToggle">
                    <i class="bi bi-volume-up"></i>
                    语音播报
                </button>
                <button class="btn btn-outline-light btn-sm" id="historyBtn">
                    <i class="bi bi-clock-history"></i>
                    历史行程
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid p-0">
        <!-- 用户查询输入区域 -->
        <div class="query-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="query-input-card">
                            <form id="planningForm">
                                <!-- 旅行需求输入 -->
                                <div class="mb-3">
                                    <label for="userQuery" class="form-label">
                                        <i class="bi bi-compass"></i>
                                        告诉我您的自驾旅行想法
                                    </label>
                                    <textarea
                                        class="form-control query-input"
                                        id="userQuery"
                                        rows="3"
                                        placeholder="例如：我想从福州出发去莆田玩两天，主要想看历史文化景点，开的是特斯拉Model 3..."
                                        required
                                    >我在福州闽东大厦，这周末要去莆田玩两天</textarea>
                                </div>

                                <!-- 车辆信息输入（可选） -->
                                <div class="vehicle-info-section">
                                    <div class="d-flex align-items-center mb-2">
                                        <label class="form-label mb-0 me-2">
                                            <i class="bi bi-car-front"></i>
                                            车辆信息（可选，有助于更精准的续航规划）
                                        </label>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleVehicleInfo">
                                            <i class="bi bi-chevron-down"></i>
                                            展开
                                        </button>
                                    </div>

                                    <div id="vehicleInfoPanel" class="vehicle-info-panel" style="display: none;">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="vehicleModel" class="form-label">车辆型号</label>
                                                <input type="text" class="form-control" id="vehicleModel"
                                                       placeholder="例如：特斯拉 Model 3">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="vehicleRange" class="form-label">标称续航（公里）</label>
                                                <input type="number" class="form-control" id="vehicleRange"
                                                       placeholder="例如：450">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="chargingType" class="form-label">充电类型</label>
                                                <select class="form-select" id="chargingType">
                                                    <option value="">请选择</option>
                                                    <option value="tesla_supercharger">特斯拉超充</option>
                                                    <option value="ccs">国标快充</option>
                                                    <option value="chademo">CHAdeMO</option>
                                                    <option value="ac">交流慢充</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="drivingMode" class="form-label">运行模式</label>
                                                <select class="form-select" id="drivingMode">
                                                    <option value="interactive">交互式（推荐）</option>
                                                    <option value="automatic">全自动</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg query-submit" id="planButton">
                                        <i class="bi bi-send"></i>
                                        开始智能分析
                                    </button>
                                </div>
                                <input type="hidden" id="userId" value="1">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 双栏布局主体 -->
        <div class="main-content">
            <div class="container-fluid">
                <div class="row g-0">
                    <!-- 左栏：分析面板 -->
                    <div class="col-lg-5 analysis-panel">
                        <div class="panel-header">
                            <h5 class="panel-title">
                                <i class="bi bi-cpu"></i>
                                AI智能分析过程
                            </h5>
                            <div class="panel-controls">
                                <button class="btn btn-sm btn-outline-secondary" id="ttsToggleAnalysis">
                                    <i class="bi bi-volume-up"></i>
                                    语音播报
                                </button>
                                <button class="btn btn-sm btn-outline-info" id="analysisProgress">
                                    <i class="bi bi-info-circle"></i>
                                    <span id="progressText">等待开始</span>
                                </button>
                            </div>
                        </div>

                        <div class="panel-body">
                            <!-- 宏观策略确认区域 -->
                            <div id="strategyConfirmation" class="strategy-confirmation" style="display: none;">
                                <div class="strategy-card">
                                    <div class="strategy-header">
                                        <h6><i class="bi bi-map"></i> 宏观策略建议</h6>
                                        <span class="badge bg-warning">需要确认</span>
                                    </div>
                                    <div class="strategy-content" id="strategyContent">
                                        <!-- 策略内容将动态填充 -->
                                    </div>
                                    <div class="strategy-actions">
                                        <button class="btn btn-success btn-sm" id="confirmStrategy">
                                            <i class="bi bi-check-circle"></i> 确认策略
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" id="modifyStrategy">
                                            <i class="bi bi-pencil"></i> 修改
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 动态分析步骤容器 -->
                            <div id="analysisSteps" class="analysis-steps">
                                <div class="analysis-placeholder">
                                    <div class="text-center text-muted">
                                        <i class="bi bi-clock-history"></i>
                                        <p class="mt-2">等待开始分析...</p>
                                        <small>AI将为您透明化展示整个分析思考过程</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="analysis-controls mt-4">
                                <button class="btn btn-success w-100" id="startPlanningBtn" style="display: none;">
                                    <i class="bi bi-play-circle"></i>
                                    立即开始规划
                                </button>
                                <button class="btn btn-outline-warning w-100 mt-2" id="pausePlanningBtn" style="display: none;">
                                    <i class="bi bi-pause-circle"></i>
                                    暂停分析
                                </button>
                                <button class="btn btn-outline-danger w-100 mt-2" id="cancelPlanningBtn" style="display: none;">
                                    <i class="bi bi-x-circle"></i>
                                    取消生成
                                </button>
                            </div>

                            <!-- 语音反馈状态 -->
                            <div id="voiceFeedbackStatus" class="voice-feedback-status mt-3" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="voice-indicator">
                                        <i class="bi bi-volume-up text-primary"></i>
                                    </div>
                                    <div class="voice-content ms-2">
                                        <small class="text-muted">正在播报：</small>
                                        <div id="currentVoiceText" class="voice-text">...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右栏：行程与状态面板 -->
                    <div class="col-lg-7 itinerary-panel">
                        <div class="panel-header">
                            <h5 class="panel-title">
                                <i class="bi bi-car-front"></i>
                                智能自驾行程规划
                            </h5>
                            <div class="panel-controls">
                                <button class="btn btn-sm btn-outline-primary" id="viewModeList">
                                    <i class="bi bi-list-ul"></i>
                                    列表
                                </button>
                                <button class="btn btn-sm btn-outline-primary" id="viewModeMap">
                                    <i class="bi bi-geo-alt"></i>
                                    地图
                                </button>
                                <button class="btn btn-sm btn-outline-success" id="viewModeRoute">
                                    <i class="bi bi-signpost"></i>
                                    路线
                                </button>
                            </div>
                        </div>

                        <div class="panel-body">
                            <!-- 阶段一：等待状态 -->
                            <div id="waitingView" class="status-view">
                                <div class="status-content">
                                    <i class="bi bi-compass status-icon"></i>
                                    <h4 class="status-title">开始你的智能旅行规划</h4>
                                    <p class="status-description">输入你的旅行想法，AI将为你生成个性化的旅行行程</p>
                                </div>
                            </div>

                            <!-- 阶段一：分析状态 -->
                            <div id="analysisView" class="status-view" style="display: none;">
                                <div class="status-content">
                                    <div class="status-spinner">
                                        <div class="spinner-border text-primary" role="status"></div>
                                    </div>
                                    <h4 class="status-title" id="analysisStatusTitle">正在分析您的需求...</h4>
                                    <p class="status-description" id="analysisStatusDesc">AI正在理解您的旅行偏好和需求</p>
                                </div>
                            </div>

                            <!-- 阶段二：流式行程构建 -->
                            <div id="itineraryView" class="itinerary-content" style="display: none;">
                                <!-- 行程头部信息 -->
                                <div class="itinerary-header">
                                    <div class="itinerary-title-section">
                                        <h3 id="itineraryTitle" class="itinerary-title">行程标题</h3>
                                        <p id="itineraryDescription" class="itinerary-description">行程描述</p>
                                    </div>
                                    <div class="itinerary-actions">
                                        <button class="btn btn-outline-success btn-sm" id="saveItinerary">
                                            <i class="bi bi-save"></i>
                                            保存
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" id="editItinerary">
                                            <i class="bi bi-pencil"></i>
                                            编辑
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" id="shareItinerary">
                                            <i class="bi bi-share"></i>
                                            分享
                                        </button>
                                    </div>
                                </div>

                                <!-- 行程统计卡片 -->
                                <div class="itinerary-stats">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-calendar-event"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="totalDays">0</div>
                                            <div class="stat-label">天数</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-geo-alt"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="totalPOIs">0</div>
                                            <div class="stat-label">景点</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-signpost-2"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="totalDistance">0</div>
                                            <div class="stat-label">总里程</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-battery-charging"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="chargingStops">0</div>
                                            <div class="stat-label">充电站</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-p-square"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="parkingSpots">0</div>
                                            <div class="stat-label">停车场</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-currency-dollar"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="estimatedBudget">¥0</div>
                                            <div class="stat-label">预算</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 每日行程卡片 -->
                                <div id="dailyItinerary" class="daily-itinerary">
                                    <!-- 每日行程卡片将在这里流式添加 -->
                                </div>
                            </div>

                            <!-- 地图视图 -->
                            <div id="mapView" class="map-view" style="display: none;">
                                <div id="mapContainer" class="map-container">
                                    <div class="map-placeholder">
                                        <i class="bi bi-map display-4 text-muted"></i>
                                        <p class="text-muted mt-2">地图功能开发中...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 路线视图 -->
                            <div id="routeView" class="route-view" style="display: none;">
                                <div class="route-container">
                                    <!-- 路线概览 -->
                                    <div class="route-overview">
                                        <h6><i class="bi bi-signpost"></i> 自驾路线概览</h6>
                                        <div id="routeOverviewContent">
                                            <!-- 路线概览内容 -->
                                        </div>
                                    </div>

                                    <!-- 充电规划 -->
                                    <div class="charging-plan mt-3">
                                        <h6><i class="bi bi-battery-charging"></i> 充电规划</h6>
                                        <div id="chargingPlanContent">
                                            <!-- 充电规划内容 -->
                                        </div>
                                    </div>

                                    <!-- 停车建议 -->
                                    <div class="parking-suggestions mt-3">
                                        <h6><i class="bi bi-p-square"></i> 停车建议</h6>
                                        <div id="parkingSuggestionsContent">
                                            <!-- 停车建议内容 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史行程模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock-history"></i>
                        历史行程
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="historyList" class="history-list">
                        <!-- 历史行程列表 -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载历史行程...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- TTS播报状态指示器 -->
    <div id="ttsIndicator" class="tts-indicator" style="display: none;">
        <div class="tts-content">
            <div class="tts-icon-container">
                <i class="bi bi-volume-up tts-icon"></i>
                <div class="tts-wave">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            <div class="tts-info">
                <div class="tts-title">AI语音播报</div>
                <div class="tts-text" id="ttsCurrentText">正在播报分析结果...</div>
                <div class="tts-progress">
                    <div class="progress">
                        <div class="progress-bar" id="ttsProgressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <div class="tts-controls">
                <button class="btn btn-sm btn-outline-light" id="pauseTTS">
                    <i class="bi bi-pause"></i>
                </button>
                <button class="btn btn-sm btn-outline-light" id="stopTTS">
                    <i class="bi bi-stop"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 全局加载遮罩 -->
    <div id="globalLoading" class="global-loading" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status"></div>
            </div>
            <h5 class="loading-title">AI正在为您规划行程</h5>
            <p class="loading-description">请稍候，这可能需要几分钟时间...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/tts.js"></script>
    <!-- 暂时注释掉旧的planning-phase.js，使用V3统一架构 -->
    <!-- <script src="/static/js/planning-phase.js?v=1751989000"></script> -->
    <script src="/static/js/app-v3-refactored.js?v=1751989000"></script>
</body>
</html>
