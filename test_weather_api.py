"""
测试天气API功能

验证天气API端点是否正常工作。
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_weather_forecast_api():
    """测试天气预报API"""
    print("=" * 60)
    print("测试天气预报API")
    print("=" * 60)
    
    url = "http://localhost:8000/api/weather/forecast"
    
    # 测试数据
    test_cases = [
        {"city": "莆田", "days": 3},
        {"city": "北京", "days": 5},
        {"city": "上海", "days": 3},
        {"city": "厦门", "days": 3},
    ]
    
    for i, test_data in enumerate(test_cases):
        print(f"\n{i+1}. 测试城市: {test_data['city']}")
        
        try:
            response = requests.post(url, json=test_data, timeout=30)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 请求成功")
                print(f"   状态: {result.get('status')}")
                print(f"   城市: {result.get('city')}")
                print(f"   描述: {result.get('description')}")
                
                weather_data = result.get('weather', [])
                print(f"   天气数据条数: {len(weather_data)}")
                
                for j, weather in enumerate(weather_data[:2]):  # 只显示前2条
                    print(f"     {j+1}. {weather.get('date')}: {weather.get('temperature')} {weather.get('weather')}")
                    
            else:
                print(f"   ❌ 请求失败")
                print(f"   错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时")
        except requests.exceptions.ConnectionError:
            print(f"   🔌 连接错误")
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")


def test_current_weather_api():
    """测试当前天气API"""
    print("\n" + "=" * 60)
    print("测试当前天气API")
    print("=" * 60)
    
    # 测试城市
    test_cities = ["莆田", "北京", "上海", "厦门"]
    
    for i, city in enumerate(test_cities):
        print(f"\n{i+1}. 测试城市: {city}")
        
        url = f"http://localhost:8000/api/weather/current/{city}"
        
        try:
            response = requests.get(url, timeout=30)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 请求成功")
                print(f"   状态: {result.get('status')}")
                print(f"   城市: {result.get('city')}")
                print(f"   描述: {result.get('description')}")
                
                weather = result.get('weather', {})
                if weather:
                    print(f"   温度: {weather.get('temperature')}")
                    print(f"   天气: {weather.get('weather')}")
                    print(f"   湿度: {weather.get('humidity')}")
                    print(f"   风向风力: {weather.get('wind')}")
                    
            else:
                print(f"   ❌ 请求失败")
                print(f"   错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时")
        except requests.exceptions.ConnectionError:
            print(f"   🔌 连接错误")
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")


def main():
    """主测试函数"""
    print("AutoPilot AI - 天气API测试")
    print("=" * 60)
    print("本测试将验证:")
    print("1. 天气预报API (/api/weather/forecast)")
    print("2. 当前天气API (/api/weather/current/{city})")
    print("=" * 60)
    
    try:
        # 测试天气预报API
        test_weather_forecast_api()
        
        # 测试当前天气API
        test_current_weather_api()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        print("✅ 天气API功能测试完成！")
        print("💡 如果所有测试都通过，说明天气API正常工作。")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    main()
