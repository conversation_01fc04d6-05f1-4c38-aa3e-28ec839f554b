import asyncio
from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from tools.Amap.map_tool import MapTool
from autogen_core.tools import FunctionTool

# 1. 封装地图工具
map_tool = MapTool()
search_pois_tool = FunctionTool(
    name="search_pois",
    description="搜索POI（兴趣点），如餐厅、酒店、景点等",
    func=map_tool.search_pois
)
get_route_tool = FunctionTool(
    name="get_route",
    description="获取两地之间的路线规划",
    func=map_tool.get_route
)

async def main() -> None:
    model_client = OpenAIChatCompletionClient(
        model="Qwen3-30B-A3B",
        model_info= {
            "model": "Qwen3-30B-A3B",
            "api_key": "123",
            "api_base": "http://************:8013/v1",
            "vision": False,
            "family": "any", #ModelFamily.QWEN,  # 指定模型家族为 Qwen
            "function_calling": True,    # 如果你需要 function calling
            "json_output": True,         # 如果你需要 json 输出
            "structured_output": True,   # 如果你需要结构化输出
            # 其他自定义字段
        },
        api_key="123",
        base_url="http://************:8013/v1",seed=42, temperature=0)
    assistant = AssistantAgent(
        name="assistant",
        system_message="You are a helpful assistant. You can call tools to help user.",
        model_client=model_client,
        tools=[search_pois_tool, get_route_tool],
        reflect_on_tool_use=True, # Set to True to have the model reflect on the tool use, set to False to return the tool call result directly.
    )
    while True:
        user_input = input("User: ")
        if user_input == "exit":
            break
        response = await assistant.on_messages([TextMessage(content=user_input, source="user")], CancellationToken())
        print("Assistant:", response.chat_message.content)

asyncio.run(main())
