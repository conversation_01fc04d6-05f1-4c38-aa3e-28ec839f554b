#!/usr/bin/env python3
"""
测试LLM调用和JSON解析的调试脚本
"""
import requests
import json
import time

def test_travel_planner_api():
    """测试旅行规划API"""
    url = "http://localhost:8000/api/v3/travel-planner/plan"
    
    payload = {
        "user_id": "1",
        "user_query": "我想去北京玩3天，主要想拍摄古建筑和历史文化景点，预算5000元左右",
        "session_id": "test_debug_session",
        "execution_mode": "interactive",
        "task_id": "test_task_debug"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("发送测试请求...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("开始接收SSE流...")
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"SSE: {line}")
                    if "eos" in line:
                        print("流结束")
                        break
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {str(e)}")

if __name__ == "__main__":
    test_travel_planner_api()
