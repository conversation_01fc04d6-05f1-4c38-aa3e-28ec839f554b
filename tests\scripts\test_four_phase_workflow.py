#!/usr/bin/env python3
"""
四阶段工作流测试脚本

验证修复后的TravelPlannerAgent是否正确实现了四阶段工作流：
1. Phase 1: 意图理解与个性化融合
2. Phase 2: 动态工具规划与四层并行执行
3. Phase 3: 数据综合与智能决策
4. Phase 4: 结构化结果生成与记忆存储
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import asyncio
import json
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import TravelPlanRequest
from src.models.itinerary.itinerary_schemas import EventType

async def test_four_phase_workflow():
    """测试四阶段工作流"""
    print("=" * 60)
    print("测试四阶段工作流修复验证")
    print("=" * 60)
    
    agent = TravelPlannerAgent()
    
    # 测试请求：从北京亦庄去天津玩两天
    request = TravelPlanRequest(
        user_id="user_001",
        query="我在北京亦庄，周末去天津玩两天"
    )
    
    print(f"用户查询: {request.query}")
    print(f"用户ID: {request.user_id}")
    print()
    
    # 收集所有事件
    events = []
    phases_detected = {
        "phase1": False,
        "phase2": False, 
        "phase3": False,
        "phase4": False
    }
    
    tool_calls_count = 0
    tool_results_count = 0
    
    try:
        async for event in agent.plan_travel(request):
            events.append(event)
            
            # 检测阶段
            if event.event_type == EventType.THINKING_START:
                if "Phase 1" in str(event.payload):
                    phases_detected["phase1"] = True
                    print("✅ 检测到 Phase 1: 意图理解与个性化融合")
                    
            elif event.event_type == EventType.THINKING_STEP:
                content = event.payload.get('content', '') if isinstance(event.payload, dict) else str(event.payload)
                
                if "第一层：并行获取" in content:
                    phases_detected["phase2"] = True
                    print("✅ 检测到 Phase 2: 动态工具规划与四层并行执行")
                    
                elif "开始智能决策分析" in content:
                    phases_detected["phase3"] = True
                    print("✅ 检测到 Phase 3: 数据综合与智能决策")
                    
                elif "正在生成最终行程" in content:
                    phases_detected["phase4"] = True
                    print("✅ 检测到 Phase 4: 结构化结果生成与记忆存储")
                    
            elif event.event_type == EventType.TOOL_CALL:
                tool_calls_count += 1
                tool_name = event.payload.get('tool_name', '') if isinstance(event.payload, dict) else ''
                print(f"🔧 工具调用 #{tool_calls_count}: {tool_name}")
                
            elif event.event_type == EventType.TOOL_RESULT:
                tool_results_count += 1
                result_summary = event.payload.get('result_summary', '') if isinstance(event.payload, dict) else ''
                print(f"📊 工具结果 #{tool_results_count}: {result_summary}")
                
            elif event.event_type == EventType.FINAL_ITINERARY:
                print("🎯 收到最终行程事件")
                
        print()
        print("=" * 60)
        print("测试结果统计")
        print("=" * 60)
        
        # 验证四个阶段都被执行
        all_phases_detected = all(phases_detected.values())
        print(f"四阶段工作流完整性: {'✅ 通过' if all_phases_detected else '❌ 失败'}")
        
        for phase, detected in phases_detected.items():
            status = "✅" if detected else "❌"
            print(f"  - {phase}: {status}")
            
        print(f"\n工具调用统计:")
        print(f"  - 工具调用次数: {tool_calls_count}")
        print(f"  - 工具结果次数: {tool_results_count}")
        print(f"  - 总事件数量: {len(events)}")
        
        # 验证数据传递
        has_poi_data = tool_calls_count > 0 and tool_results_count > 0
        print(f"\n数据传递验证:")
        print(f"  - 工具调用与结果匹配: {'✅ 通过' if has_poi_data else '❌ 失败'}")
        
        # 验证最终输出
        final_events = [e for e in events if e.event_type == EventType.FINAL_ITINERARY]
        has_final_output = len(final_events) > 0
        print(f"  - 最终行程生成: {'✅ 通过' if has_final_output else '❌ 失败'}")
        
        return all_phases_detected and has_poi_data and has_final_output
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_data_flow_continuity():
    """测试数据流连续性"""
    print("\n" + "=" * 60)
    print("测试数据流连续性")
    print("=" * 60)
    
    agent = TravelPlannerAgent()
    
    request = TravelPlanRequest(
        user_id="user_002", 
        query="周末从福州自驾去厦门，带孩子玩两天"
    )
    
    events = []
    entity_extracted = False
    poi_searches_performed = False
    final_itinerary_generated = False
    
    try:
        async for event in agent.plan_travel(request):
            events.append(event)
            
            # 检查意图提取
            if event.event_type == EventType.THINKING_STEP:
                content = str(event.payload)
                if "已提取用户需求" in content:
                    entity_extracted = True
                    print("✅ 用户意图成功提取")
                    
            # 检查POI搜索
            elif event.event_type == EventType.TOOL_CALL:
                tool_name = event.payload.get('tool_name', '') if isinstance(event.payload, dict) else ''
                if tool_name == "maps_text_search":
                    poi_searches_performed = True
                    print("✅ POI搜索工具调用成功")
                    
            # 检查最终行程
            elif event.event_type == EventType.FINAL_ITINERARY:
                final_itinerary_generated = True
                print("✅ 最终行程生成成功")
                
        # 验证数据流连续性
        data_flow_complete = entity_extracted and poi_searches_performed and final_itinerary_generated
        
        print(f"\n数据流连续性验证:")
        print(f"  - 意图提取: {'✅' if entity_extracted else '❌'}")
        print(f"  - POI搜索: {'✅' if poi_searches_performed else '❌'}")
        print(f"  - 最终生成: {'✅' if final_itinerary_generated else '❌'}")
        print(f"  - 整体连续性: {'✅ 通过' if data_flow_complete else '❌ 失败'}")
        
        return data_flow_complete
        
    except Exception as e:
        print(f"❌ 数据流测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("四阶段工作流修复验证测试")
    print("测试目标: 验证Agent是否正确实现四阶段并行工作流并且数据正确传递")
    print()
    
    # 测试1: 四阶段工作流完整性
    test1_result = await test_four_phase_workflow()
    
    # 测试2: 数据流连续性
    test2_result = await test_data_flow_continuity()
    
    # 总结
    print("\n" + "=" * 60)
    print("最终测试结果")
    print("=" * 60)
    
    print(f"测试1 - 四阶段工作流: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"测试2 - 数据流连续性: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    overall_success = test1_result and test2_result
    print(f"\n总体测试结果: {'✅ 全部通过' if overall_success else '❌ 存在问题'}")
    
    if overall_success:
        print("\n🎉 修复成功！Agent现在正确实现了四阶段工作流并且数据传递正常。")
    else:
        print("\n⚠️  仍存在问题，需要进一步修复。")
    
    return overall_success

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1) 