import os
from typing import Optional

import yaml
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class LLMConfig(BaseModel):
    model: str = Field(..., description="模型名称")
    api_key: str = Field(..., description="API密钥")
    base_url: Optional[str] = Field(None, description="API基础URL")


class TestSettings(BaseSettings):
    reasoning_llm: LLMConfig
    basic_llm: LLMConfig

    model_config = SettingsConfigDict(
        env_file='.env',
        env_file_encoding='utf-8',
        extra='ignore',
        yaml_file='config/default.yaml',
        case_sensitive=False
    )


def run_test():
    print("开始测试 YAML 配置加载...")
    try:
        settings = TestSettings()
        print("配置加载成功！")
        print(f"Reasoning LLM Model: {settings.reasoning_llm.model}")
        print(f"Reasoning LLM API Key: {settings.reasoning_llm.api_key}")
        print(f"Basic LLM Model: {settings.basic_llm.model}")
        print(f"Basic LLM API Key: {settings.basic_llm.api_key}")
    except Exception as e:
        print(f"配置加载失败: {e}")


if __name__ == "__main__":
    # 确保config目录和default.yaml存在，为了测试的独立性，可以创建一个临时的yaml文件
    # 但为了复用现有配置，我们依赖外部的文件结构
    # os.makedirs('config', exist_ok=True)
    # with open('config/temp_default.yaml', 'w') as f:
    #     f.write("""
    # reasoning_llm:
    #   model: "test-reasoning-model"
    #   api_key: "test-reasoning-key"
    # basic_llm:
    #   model: "test-basic-model"
    #   api_key: "test-basic-key"
    # """)
    
    # 运行时，pydantic-settings 会自动找到这个路径，前提是当前工作目录是项目根目录
    run_test() 