"""
测试规划阶段实现
验证完整的旅行规划Agent后半部分流程
"""
import pytest
import asyncio
import json
from typing import Dict, Any, List

from src.agents.travel_planner_langgraph.stream_adapter import SSEStreamAdapter


class TestPlanningPhaseImplementation:
    """测试规划阶段实现"""
    
    @pytest.fixture
    def stream_adapter(self):
        """创建流适配器实例"""
        return SSEStreamAdapter()
    
    @pytest.fixture
    def sample_analysis_result(self):
        """示例分析结果"""
        return {
            "session_id": "test_session_123",
            "user_id": "test_user_001",
            "query": "我想去莆田玩两天",
            "core_intent": {
                "destinations": ["莆田"],
                "days": 2,
                "travel_dates": {
                    "start_date": "2025-07-06",
                    "end_date": "2025-07-07"
                }
            },
            "user_profile": {
                "travel_style": "ADVENTURE",
                "budget_preference": "中等"
            },
            "travel_preferences": {
                "accommodation_pref": '["精品酒店", "特色民宿"]',
                "transportation_pref": '["自驾"]'
            },
            "preference_profile": {
                "attraction_preferences": {
                    "cultural_historical": {"ancient_architecture": "8"},
                    "special_experiences": {"food_exploration": "9"}
                },
                "food_preferences": {
                    "cuisine_preferences": {"fujian": "9"},
                    "restaurant_type_preferences": {"street_food": "8"}
                }
            }
        }
    
    @pytest.fixture
    def sample_state_with_poi_results(self):
        """包含POI结果的状态"""
        return {
            "session_id": "test_session_123",
            "weather_info": {
                "weather": "晴朗",
                "temperature": "22-28°C",
                "humidity": "65%"
            },
            "poi_results": {
                "attractions": [
                    {
                        "id": "poi_001",
                        "name": "湄洲岛",
                        "address": "莆田市秀屿区湄洲镇",
                        "description": "妈祖文化发源地，海上仙境",
                        "images": ["https://example.com/image1.jpg"],
                        "price": {"type": "ticket", "value": "¥65", "currency": "CNY"},
                        "rating": 4.6,
                        "business_hours": "08:00-18:00",
                        "parking_info": "有免费停车场",
                        "location": {"latitude": 25.0478, "longitude": 119.1068},
                        "tags": ["文化", "海岛", "必游"]
                    },
                    {
                        "id": "poi_002", 
                        "name": "南少林寺",
                        "address": "莆田市荔城区西天尾镇",
                        "description": "千年古刹，武术圣地",
                        "images": ["https://example.com/image2.jpg"],
                        "price": {"type": "ticket", "value": "¥20", "currency": "CNY"},
                        "rating": 4.3,
                        "business_hours": "06:00-17:30",
                        "parking_info": "有停车场，¥5/小时",
                        "location": {"latitude": 25.4544, "longitude": 119.0077},
                        "tags": ["古建筑", "文化", "历史"]
                    }
                ],
                "restaurants": [
                    {
                        "id": "poi_003",
                        "name": "阿宗面线",
                        "address": "莆田市城厢区学园中街",
                        "description": "莆田特色小吃，传统手工面线",
                        "images": ["https://example.com/image3.jpg"],
                        "price": {"type": "average", "value": "¥25", "currency": "CNY"},
                        "rating": 4.5,
                        "business_hours": "07:00-21:00",
                        "parking_info": "路边停车",
                        "location": {"latitude": 25.4394, "longitude": 119.0075},
                        "tags": ["特色小吃", "传统", "实惠"]
                    }
                ],
                "hotels": [
                    {
                        "id": "poi_004",
                        "name": "莆田万达嘉华酒店",
                        "address": "莆田市城厢区学园中街万达广场",
                        "description": "豪华商务酒店，设施完善",
                        "images": ["https://example.com/image4.jpg"],
                        "price": {"type": "room", "value": "¥388", "currency": "CNY"},
                        "rating": 4.4,
                        "business_hours": "24小时",
                        "parking_info": "免费停车",
                        "location": {"latitude": 25.4350, "longitude": 119.0100},
                        "tags": ["商务", "豪华", "停车便利"]
                    }
                ]
            }
        }
    
    @pytest.fixture
    def sample_daily_itineraries(self):
        """示例每日行程"""
        return [
            {
                "day": 1,
                "theme": "文化探索日",
                "items": [
                    {
                        "type": "poi",
                        "poi": {
                            "id": "poi_002",
                            "name": "南少林寺",
                            "address": "莆田市荔城区西天尾镇"
                        },
                        "start_time": "09:00",
                        "end_time": "11:30",
                        "duration": "2.5小时",
                        "description": "参观千年古刹，了解武术文化"
                    },
                    {
                        "type": "meal",
                        "poi": {
                            "id": "poi_003",
                            "name": "阿宗面线",
                            "address": "莆田市城厢区学园中街"
                        },
                        "start_time": "12:00",
                        "end_time": "13:00",
                        "duration": "1小时",
                        "description": "品尝莆田特色面线"
                    }
                ],
                "total_distance": 15.2,
                "estimated_duration": "8小时",
                "notes": ["建议穿舒适的鞋子", "注意防晒"]
            },
            {
                "day": 2,
                "theme": "海岛风情日", 
                "items": [
                    {
                        "type": "poi",
                        "poi": {
                            "id": "poi_001",
                            "name": "湄洲岛",
                            "address": "莆田市秀屿区湄洲镇"
                        },
                        "start_time": "08:30",
                        "end_time": "16:00",
                        "duration": "7.5小时",
                        "description": "游览妈祖文化圣地，享受海岛风光"
                    }
                ],
                "total_distance": 45.8,
                "estimated_duration": "全天",
                "notes": ["需要乘船上岛", "建议带防晒用品"]
            }
        ]
    
    def test_format_poi_list(self, stream_adapter, sample_state_with_poi_results):
        """测试POI列表格式化"""
        attractions = sample_state_with_poi_results["poi_results"]["attractions"]
        formatted_pois = stream_adapter._format_poi_list(attractions)
        
        assert len(formatted_pois) == 2
        assert formatted_pois[0]["name"] == "湄洲岛"
        assert formatted_pois[0]["price"]["value"] == "¥65"
        assert formatted_pois[0]["rating"] == 4.6
        assert "文化" in formatted_pois[0]["tags"]
        
    def test_format_daily_itinerary(self, stream_adapter, sample_daily_itineraries):
        """测试每日行程格式化"""
        day1 = sample_daily_itineraries[0]
        formatted_day = stream_adapter._format_daily_itinerary(day1)
        
        assert formatted_day["day"] == 1
        assert formatted_day["theme"] == "文化探索日"
        assert len(formatted_day["items"]) == 2
        assert formatted_day["total_distance"] == 15.2
        
    def test_extract_itinerary_highlights(self, stream_adapter, sample_daily_itineraries):
        """测试行程亮点提取"""
        highlights = stream_adapter._extract_itinerary_highlights(sample_daily_itineraries)
        
        assert isinstance(highlights, list)
        # 由于测试数据中POI评分都不够高，可能没有亮点
        # 这里主要测试方法不会出错
        
    def test_handle_planning_start_events(self, stream_adapter, sample_analysis_result):
        """测试规划启动事件处理"""
        events = stream_adapter._handle_planning_start_events(
            sample_analysis_result, 
            "test_session_123"
        )
        
        assert len(events) >= 2  # 至少有启动事件和TTS事件
        
        # 检查启动事件
        start_event_data = json.loads(events[0].split("data: ")[1])
        assert start_event_data["event_type"] == "planning_started"
        assert start_event_data["payload"]["session_id"] == "test_session_123"
        assert "莆田" in start_event_data["payload"]["destinations"]
        
    def test_handle_weather_events(self, stream_adapter, sample_state_with_poi_results):
        """测试天气事件处理"""
        events = stream_adapter._handle_weather_events(
            sample_state_with_poi_results,
            "test_session_123"
        )
        
        assert len(events) >= 2  # 进度事件和天气信息事件
        
        # 检查天气信息事件
        weather_event_found = False
        for event in events:
            if "weather_info" in event:
                event_data = json.loads(event.split("data: ")[1])
                if event_data["event_type"] == "weather_info":
                    assert event_data["payload"]["weather"]["weather"] == "晴朗"
                    weather_event_found = True
                    break
        
        assert weather_event_found
        
    def test_handle_poi_search_events(self, stream_adapter, sample_state_with_poi_results):
        """测试POI搜索事件处理"""
        events = stream_adapter._handle_poi_search_events(
            sample_state_with_poi_results,
            "test_session_123"
        )
        
        assert len(events) >= 4  # 进度、搜索完成、各类别结果事件
        
        # 检查POI搜索完成事件
        completion_event_found = False
        for event in events:
            if "poi_search_completed" in event:
                event_data = json.loads(event.split("data: ")[1])
                if event_data["event_type"] == "poi_search_completed":
                    assert event_data["payload"]["total_pois"] == 4  # 2景点+1餐厅+1酒店
                    completion_event_found = True
                    break
        
        assert completion_event_found
        
    def test_generate_weather_tips(self, stream_adapter):
        """测试天气建议生成"""
        # 测试雨天
        rainy_weather = {"weather": "小雨", "temperature": "20-25°C"}
        tips = stream_adapter._generate_weather_tips(rainy_weather)
        assert any("雨具" in tip for tip in tips)
        
        # 测试晴天
        sunny_weather = {"weather": "晴朗", "temperature": "25-30°C"}
        tips = stream_adapter._generate_weather_tips(sunny_weather)
        assert any("防晒" in tip for tip in tips)
        
    def test_get_category_name(self, stream_adapter):
        """测试POI类别名称获取"""
        assert stream_adapter._get_category_name("attractions") == "景点"
        assert stream_adapter._get_category_name("restaurants") == "餐厅"
        assert stream_adapter._get_category_name("hotels") == "住宿"
        assert stream_adapter._get_category_name("unknown") == "unknown"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
