// =================================================================
// Digital Human - MongoDB Initialization Script (Final Corrected Version)
// =================================================================
// 数据库: dh_platform_data
// 描述: 本脚本为平台创建核心的集合，并配置必要的索引和验证规则。
//       (已修复所有字符串中的非法换行符问题)
// =================================================================

// 切换到目标数据库
use dh_platform_data;

// 先安全地删除旧集合，方便重复执行
db.conversations.drop();
db.ai_interaction_logs.drop();
print("✅ 旧集合 (conversations, ai_interaction_logs) 已被安全删除 (如果存在)。");

// -----------------------------------------------------
// 集合 1: `conversations` - 开放式对话流
// -----------------------------------------------------
// 职责: 存储用户与AI之间连续的、你来我往的闲聊或多轮问答。
db.createCollection("conversations", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["session_id", "user_id", "application_source", "created_at", "messages"],
      properties: {
        session_id: { bsonType: "string", description: "会话的唯一ID" },
        user_id: { bsonType: ["int", "double"], description: "逻辑外键, 关联到MySQL中的users.id" },
        application_source: { bsonType: "string", description: "来源App (如: dh_vpa, dh_tripplanner)" },
        created_at: { bsonType: "date", description: "会话创建时间" },
        messages: {
          bsonType: "array",
          description: "消息列表",
          items: {
            bsonType: "object",
            required: ["message_id", "role", "content"],
            properties: {
              message_id: { bsonType: "string", description: "单条消息的全局唯一ID" },
              role: { enum: ["user", "assistant", "system"], description: "消息的角色" },
              content: { bsonType: "string", description: "消息的文本内容" }
            }
          }
        }
      }
    }
  }
});

// 为 `conversations` 创建核心索引
db.conversations.createIndex({ user_id: 1, created_at: -1 }, { name: "idx_user_conversations" });
db.conversations.createIndex({ session_id: 1 }, { unique: true, name: "uidx_session_id" });
print("✅ 集合 'conversations' 已成功创建，并配置了验证规则和索引。");


// -----------------------------------------------------
// 集合 2: `ai_interaction_logs` - AI任务交互日志 (核心)
// -----------------------------------------------------
// 职责: 作为AI后台任务的“作战后总结报告/黑匣子”，永久归档所有细节。
db.createCollection("ai_interaction_logs", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["interaction_id", "user_id", "application_source", "status", "timestamp"],
      properties: {
        interaction_id: { bsonType: "string", description: "全局唯一的交互ID, 与MySQL和Redis关联的关键" },
        user_id: { bsonType: ["int", "double"], description: "逻辑外键, 关联到MySQL中的users.id" },
        application_source: { bsonType: "string", description: "来源App" },
        status: { enum: ["PROCESSING", "SUCCESS", "FAILED"], description: "任务的最终状态" },
        timestamp: { bsonType: "date", description: "任务开始时间" },
        business_steps_log: {
          bsonType: "array",
          description: "对业务有意义的规划步骤日志",
          items: {
            bsonType: "object",
            required: ["step_name", "status"],
            properties: {
              step_name: { bsonType: "string" },
              status: { enum: ["SUCCESS", "FAILED"] },
              summary: { bsonType: "string" }
            }
          }
        },
        raw_model_trace: {
          bsonType: "array",
          description: "包含<think>内容的模型原始痕迹",
           items: {
            bsonType: "object",
            required: ["content", "timestamp"],
            properties: {
              content: { bsonType: "string" },
              timestamp: { bsonType: "date" }
            }
          }
        },
        final_output: { bsonType: "object", description: "AI生成的最终完整结果" }
      }
    }
  }
});

// 为 `ai_interaction_logs` 创建性能和关联性至关重要的索引
db.ai_interaction_logs.createIndex({ interaction_id: 1 }, { unique: true, name: "uidx_interaction_id" });
db.ai_interaction_logs.createIndex({ user_id: 1, timestamp: -1 }, { name: "idx_user_interactions" });
db.ai_interaction_logs.createIndex({ application_source: 1, status: 1, timestamp: -1 }, { name: "idx_app_status_time" });
print("✅ 集合 'ai_interaction_logs' 已成功创建，并配置了验证规则和索引。");

print("\n🎉 MongoDB 初始化完成。您的平台已准备就绪，可以进行高性能的AI交互！");