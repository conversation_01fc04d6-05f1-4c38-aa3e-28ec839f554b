#!/usr/bin/env python3
"""
真实LLM完整流程测试脚本

使用真实的LLM和MySQL数据库进行完整的意图识别和旅行规划测试
- 调用真实的智谱AI LLM进行意图分析
- 使用MySQL中user_id=1的真实用户数据
- 完整的端到端测试流程

使用方法:
python test_real_llm_complete_flow.py
"""

import asyncio
import sys
import os
import json
import yaml
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.tools.unified_registry import unified_registry

# 确保所有工具被正确注册
import src.tools.travel_planner.consolidated_tools
import src.tools.travel_planner.icp_tools
import src.tools.travel_planner.amap_poi_tools  # 高德地图POI工具

# 确保AmapService被正确初始化和注册
from src.agents.services.amap_service import AmapService


class RealLLMTester:
    """真实LLM完整流程测试器"""
    
    def __init__(self):
        """初始化测试器"""
        print("🚀 初始化真实LLM完整流程测试器")
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化数据库连接
        self.mysql_client = self._create_mysql_client()
        
        # 创建事件总线（使用真实Redis配置）
        self.event_bus = self._create_event_bus()
        
        # 初始化AmapService
        self._init_amap_service()

        # 创建图实例（将使用真实的LLM服务）
        self.graph = TravelPlannerGraphV3(event_bus=self.event_bus)

        print("✅ 测试器初始化完成，将使用真实LLM和MySQL数据")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = Path("config/default.yaml")
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _create_mysql_client(self):
        """创建MySQL客户端 - 强制使用真实连接"""
        try:
            import aiomysql
            print("📊 创建真实MySQL连接...")

            class RealMySQLClient:
                def __init__(self, config):
                    self.config = config
                    self.pool = None

                async def _get_pool(self):
                    if self.pool is None:
                        print(f"🔗 连接MySQL: {self.config['mysql']['host']}:{self.config['mysql']['port']}")
                        self.pool = await aiomysql.create_pool(
                            host=self.config['mysql']['host'],
                            port=self.config['mysql']['port'],
                            user=self.config['mysql']['user'],
                            password=self.config['mysql']['password'],
                            charset='utf8mb4',
                            autocommit=True,
                            minsize=1,
                            maxsize=5
                        )
                        print("✅ MySQL连接池创建成功")
                    return self.pool

                async def fetch_one(self, query, params=None):
                    pool = await self._get_pool()
                    async with pool.acquire() as conn:
                        async with conn.cursor(aiomysql.DictCursor) as cursor:
                            await cursor.execute(query, params)
                            return await cursor.fetchone()

                async def fetch_all(self, query, params=None):
                    pool = await self._get_pool()
                    async with pool.acquire() as conn:
                        async with conn.cursor(aiomysql.DictCursor) as cursor:
                            await cursor.execute(query, params)
                            return await cursor.fetchall()

                async def close(self):
                    if self.pool:
                        self.pool.close()
                        await self.pool.wait_closed()
                        print("🔒 MySQL连接池已关闭")

            return RealMySQLClient(self.config)

        except ImportError as e:
            print(f"❌ aiomysql未安装: {str(e)}")
            print("❌ 请安装aiomysql: pip install aiomysql")
            raise RuntimeError("缺少aiomysql依赖，无法连接真实MySQL数据库")
        except Exception as e:
            print(f"❌ MySQL连接创建失败: {str(e)}")
            raise RuntimeError(f"无法创建真实MySQL连接: {str(e)}")



    def _create_event_bus(self):
        """创建事件总线 - 强制使用真实Redis"""
        try:
            print("📊 创建真实Redis事件总线...")
            from src.services.unified_event_bus import UnifiedEventBus
            import redis.asyncio as redis

            redis_client = redis.Redis(
                host=self.config['redis']['host'],
                port=self.config['redis']['port'],
                password=self.config['redis']['password'],
                decode_responses=True
            )

            print(f"🔗 连接Redis: {self.config['redis']['host']}:{self.config['redis']['port']}")

            # 注意：这里不能使用await，因为这不是async函数
            # Redis连接测试将在实际使用时进行
            print("✅ Redis客户端创建成功")

            return UnifiedEventBus(redis_client, task_ttl=3600)

        except ImportError as e:
            print(f"❌ Redis依赖未安装: {str(e)}")
            print("❌ 请安装redis: pip install redis")
            raise RuntimeError("缺少redis依赖，无法创建真实事件总线")
        except Exception as e:
            print(f"❌ Redis连接失败: {str(e)}")
            print("❌ 测试要求使用真实Redis，不允许使用模拟")
            raise RuntimeError(f"无法创建真实Redis连接: {str(e)}")

    def _init_amap_service(self):
        """初始化AmapService"""
        try:
            print("🗺️ 初始化AmapService...")

            # 从配置中获取高德地图API密钥
            amap_api_key = self.config.get('amap', {}).get('api_key', 'your_amap_api_key')

            # 创建AmapService实例
            self.amap_service = AmapService(api_key=amap_api_key)

            print(f"✅ AmapService初始化完成，API密钥: {amap_api_key[:10]}...")

        except Exception as e:
            print(f"⚠️ AmapService初始化失败: {str(e)}")
            print("⚠️ 将使用后备POI数据")
    
    async def get_user_data(self, user_id: int) -> Dict[str, Any]:
        """从MySQL获取用户数据 - 强制使用真实数据库，不允许模拟"""
        print(f"📊 从真实MySQL获取用户 {user_id} 的数据...")

        try:
            # 获取用户基本信息（从dh_user_profile数据库）
            user_query = """
            SELECT u.id, u.nickname, u.avatar_url, u.status
            FROM dh_user_profile.users u
            WHERE u.id = %s
            """
            user_result = await self.mysql_client.fetch_one(user_query, (user_id,))

            if not user_result:
                raise ValueError(f"用户 {user_id} 在dh_user_profile.users表中不存在")

            # 获取用户记忆（从dh_user_profile数据库）
            memory_query = """
            SELECT memory_content
            FROM dh_user_profile.user_memories
            WHERE user_id = %s
            """
            memory_results = await self.mysql_client.fetch_all(memory_query, (user_id,))

            # 获取用户画像摘要（从dh_user_profile数据库）
            summary_query = """
            SELECT summary, keywords
            FROM dh_user_profile.user_summaries
            WHERE user_id = %s
            """
            summary_result = await self.mysql_client.fetch_one(summary_query, (user_id,))

            # 获取旅行偏好（从dh_tripplanner数据库）
            travel_profile_query = """
            SELECT travel_style, accommodation_pref, transportation_pref,
                   travel_summary, travel_keywords
            FROM dh_tripplanner.user_travel_profiles
            WHERE user_id = %s
            """
            travel_profile = await self.mysql_client.fetch_one(travel_profile_query, (user_id,))

            # 获取历史行程（从dh_tripplanner数据库）
            itinerary_query = """
            SELECT i.title, i.city_name, i.total_days,
                   GROUP_CONCAT(t.name) as tags
            FROM dh_tripplanner.itineraries i
            LEFT JOIN dh_tripplanner.itinerary_tags it ON i.id = it.itinerary_id
            LEFT JOIN dh_tripplanner.tags t ON it.tag_id = t.id
            WHERE i.user_id = %s
            GROUP BY i.id
            """
            itinerary_results = await self.mysql_client.fetch_all(itinerary_query, (user_id,))

            # 组装用户数据
            user_data = {
                "user_info": dict(user_result),
                "memories": [row['memory_content'] for row in memory_results],
                "summary": dict(summary_result) if summary_result else None,
                "travel_profile": dict(travel_profile) if travel_profile else None,
                "historical_trips": [dict(row) for row in itinerary_results]
            }

            print(f"✅ 成功获取真实MySQL用户数据: {user_data['user_info']['nickname']}")
            print(f"   - 记忆数量: {len(user_data['memories'])}")
            print(f"   - 历史行程: {len(user_data['historical_trips'])}")
            print(f"   - 旅行风格: {user_data['travel_profile']['travel_style'] if user_data['travel_profile'] else 'None'}")

            return user_data

        except Exception as e:
            print(f"❌ 真实MySQL数据获取失败: {str(e)}")
            print("❌ 测试要求使用真实数据，不允许使用模拟数据")
            raise RuntimeError(f"无法获取真实MySQL数据: {str(e)}。请检查数据库连接和数据完整性。")


    
    def create_test_scenarios(self, user_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于真实用户数据创建测试场景"""
        user_info = user_data['user_info']
        travel_profile = user_data.get('travel_profile', {})
        
        scenarios = [
            {
                "name": f"{user_info['nickname']}的北京深度摄影游",
                "input": {
                    "user_query": "我想去北京玩3天，主要想拍摄古建筑和历史文化景点，预算5000元左右",
                    "user_id": str(user_info['id']),
                    "execution_mode": "automatic",
                    "user_profile": {
                        "nickname": user_info['nickname'],
                        "travel_style": travel_profile.get('travel_style', 'ADVENTURE'),
                        "accommodation_pref": travel_profile.get('accommodation_pref', '["精品酒店"]'),
                        "transportation_pref": travel_profile.get('transportation_pref', '["自驾"]'),
                        "interests": ["摄影", "历史文化", "古建筑"],
                        "budget_level": "中高",
                        "memories": user_data['memories'][:3]  # 只传递前3条记忆
                    }
                }
            },
            {
                "name": f"{user_info['nickname']}的上海家庭游",
                "input": {
                    "user_query": "计划带家人去上海玩2天，有老人和孩子，希望行程轻松一些",
                    "user_id": str(user_info['id']),
                    "execution_mode": "automatic",
                    "user_profile": {
                        "nickname": user_info['nickname'],
                        "travel_style": "FAMILY",
                        "group_type": "家庭出行",
                        "group_size": 4,
                        "special_needs": ["老人友好", "亲子适宜"],
                        "interests": ["家庭游", "轻松游览"],
                        "budget_level": "中等",
                        "memories": user_data['memories']
                    }
                }
            }
        ]
        
        return scenarios
    
    async def test_single_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个场景"""
        print(f"\n📋 测试场景: {scenario['name']}")
        print(f"📝 用户查询: {scenario['input']['user_query']}")
        print(f"👤 用户ID: {scenario['input']['user_id']}")
        
        try:
            print(f"🔄 开始执行真实LLM完整流程...")
            start_time = datetime.now()
            
            # 执行完整流程（使用真实LLM）
            result = await self.graph.invoke(scenario['input'])
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"✅ 流程执行完成，耗时: {duration:.2f}秒")
            print(f"📊 当前阶段: {result.get('current_phase', 'Unknown')}")
            print(f"🎯 是否完成: {result.get('is_completed', False)}")
            
            # 分析结果
            analysis_result = {
                "scenario_name": scenario['name'],
                "success": not result.get("has_error", False),
                "duration": duration,
                "current_phase": result.get("current_phase"),
                "is_completed": result.get("is_completed", False),
                "framework_analysis": result.get("framework_analysis"),
                "preference_analysis": result.get("preference_analysis"),
                "consolidated_intent": result.get("consolidated_intent"),
                "icp_context": result.get("icp_context"),
                "daily_plans": result.get("daily_plans", {}),
                "planning_log": result.get("planning_log", []),
                "error_message": result.get("error_message")
            }
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            return {
                "scenario_name": scenario['name'],
                "success": False,
                "error": str(e)
            }
    
    def _print_detailed_results(self, result: Dict[str, Any]):
        """打印详细结果"""
        print(f"\n📊 详细分析结果:")
        print(f"执行时长: {result.get('duration', 0):.2f}秒")
        print(f"当前阶段: {result.get('current_phase', 'Unknown')}")
        print(f"完成状态: {result.get('is_completed', False)}")
        
        # 框架分析结果
        framework = result.get("framework_analysis", {})
        if framework:
            print("\n🏗️ LLM框架分析结果:")
            core_intent = framework.get("core_intent", {})
            for key, value in core_intent.items():
                print(f"  {key}: {value}")
        
        # 偏好分析结果
        preference = result.get("preference_analysis", {})
        if preference:
            print("\n❤️ LLM偏好分析结果:")
            for key, value in preference.items():
                if isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"    {sub_key}: {sub_value}")
                else:
                    print(f"  {key}: {value}")
        
        # 每日计划
        daily_plans = result.get("daily_plans", {})
        if daily_plans:
            print(f"\n📅 生成的行程计划:")
            for day, pois in daily_plans.items():
                print(f"  第{day}天 ({len(pois)}个POI):")
                for i, poi in enumerate(pois, 1):
                    name = poi.get("name", "Unknown")
                    poi_type = poi.get("poi_type", "unknown")
                    print(f"    {i}. {name} ({poi_type})")
        
        # 规划思考过程
        planning_log = result.get("planning_log", [])
        if planning_log:
            print(f"\n🧠 LLM规划思考过程 (最后3步):")
            for i, log_entry in enumerate(planning_log[-3:], 1):
                print(f"  {i}. {log_entry}")
    
    async def run_complete_test(self):
        """运行完整测试"""
        print("🧪 开始真实LLM完整流程测试")
        print("=" * 60)
        
        try:
            # 获取用户数据
            user_data = await self.get_user_data(user_id=1)
            
            # 创建测试场景
            scenarios = self.create_test_scenarios(user_data)
            results = []
            
            for scenario in scenarios:
                result = await self.test_single_scenario(scenario)
                results.append(result)
                
                if result.get("success"):
                    print("✅ 测试通过")
                    self._print_detailed_results(result)
                else:
                    print("❌ 测试失败")
                    print(f"错误: {result.get('error', 'Unknown error')}")
                
                print("-" * 40)
            
            # 总结报告
            self._print_summary_report(results)
            
        except Exception as e:
            print(f"❌ 测试执行失败: {str(e)}")
        finally:
            # 清理资源
            if hasattr(self, 'mysql_client'):
                await self.mysql_client.close()
    
    def _print_summary_report(self, results: List[Dict[str, Any]]):
        """打印总结报告"""
        print("\n📈 真实LLM测试总结报告")
        print("=" * 60)
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get("success"))
        total_duration = sum(r.get("duration", 0) for r in results if r.get("success"))
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"失败测试: {total_tests - successful_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        print(f"总耗时: {total_duration:.2f}秒")
        print(f"平均耗时: {total_duration/successful_tests:.2f}秒" if successful_tests > 0 else "平均耗时: N/A")
        
        print("\n📋 各场景结果:")
        for result in results:
            status = "✅" if result.get("success") else "❌"
            duration = f"({result.get('duration', 0):.2f}s)" if result.get("success") else ""
            print(f"{status} {result['scenario_name']} {duration}")
            if not result.get("success"):
                print(f"   错误: {result.get('error', 'Unknown error')}")
        
        print("\n🎯 真实LLM完整流程测试完成!")


async def main():
    """主函数"""
    tester = RealLLMTester()
    await tester.run_complete_test()


if __name__ == "__main__":
    asyncio.run(main())
