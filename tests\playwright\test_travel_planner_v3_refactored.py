"""
旅行规划系统V3重构版端到端测试

使用Playwright进行完整的两阶段工作流程测试：
1. Stage A: 意图分析（A.1-A.5步骤）
2. Stage B: ICP迭代规划
3. 前端界面交互测试
4. V3 API集成测试
"""

import asyncio
import json
import pytest
import time
from playwright.async_api import async_playwright, Page, Browser, BrowserContext


class TestTravelPlannerV3Refactored:
    """旅行规划系统V3重构版测试类"""
    
    @pytest.fixture(scope="class")
    async def browser_setup(self):
        """设置浏览器环境"""
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        yield page, browser, context, playwright
        
        await context.close()
        await browser.close()
        await playwright.stop()
    
    async def test_v3_api_health_check(self, browser_setup):
        """测试V3 API健康检查"""
        page, browser, context, playwright = browser_setup
        
        # 访问健康检查端点
        response = await page.goto("http://localhost:8000/api/v3/travel-planner/health")
        assert response.status == 200
        
        # 检查响应内容
        content = await page.content()
        health_data = json.loads(await page.evaluate("() => document.body.textContent"))
        
        assert health_data["status"] == "healthy"
        assert health_data["version"] == "3.0"
        assert health_data["architecture"] == "unified"
        assert "components" in health_data
        
        print("✅ V3 API健康检查通过")
    
    async def test_v3_api_tools_endpoint(self, browser_setup):
        """测试V3 API工具端点"""
        page, browser, context, playwright = browser_setup
        
        # 访问工具列表端点
        response = await page.goto("http://localhost:8000/api/v3/travel-planner/tools")
        assert response.status == 200
        
        # 检查工具注册情况
        content = await page.content()
        tools_data = json.loads(await page.evaluate("() => document.body.textContent"))
        
        assert "tool_registry" in tools_data
        assert "action_schemas" in tools_data
        
        tool_registry = tools_data["tool_registry"]
        assert tool_registry["action_tools_count"] > 0
        assert tool_registry["planner_tools_count"] > 0
        
        # 验证关键工具已注册
        action_tools = tool_registry["action_tools"]
        planner_tools = tool_registry["planner_tools"]
        
        # 检查Action Tools
        expected_action_tools = ["search_poi", "geocode", "get_driving_route"]
        for tool in expected_action_tools:
            assert tool in action_tools, f"Action tool {tool} not registered"
        
        # 检查Planner Tools
        expected_planner_tools = [
            "format_framework_analysis_prompt",
            "format_preference_analysis_prompt",
            "generate_planning_thought",
            "select_next_action",
            "observe_action_result"
        ]
        for tool in expected_planner_tools:
            assert tool in planner_tools, f"Planner tool {tool} not registered"
        
        print("✅ V3 API工具端点测试通过")
    
    async def test_frontend_page_load(self, browser_setup):
        """测试前端页面加载"""
        page, browser, context, playwright = browser_setup
        
        # 访问重构版前端页面
        await page.goto("http://localhost:8000/static/travel_planner_v3_refactored.html")
        
        # 等待页面加载完成
        await page.wait_for_load_state("networkidle")
        
        # 检查关键元素是否存在
        assert await page.is_visible("#userQuery"), "用户查询输入框未找到"
        assert await page.is_visible("#startAnalysisBtn"), "开始分析按钮未找到"
        assert await page.is_visible("#startPlanningBtn"), "立即规划按钮未找到"
        assert await page.is_visible("#analysisSteps"), "分析步骤容器未找到"
        assert await page.is_visible("#vehicleInfoPanel"), "车辆信息面板未找到"
        
        # 检查分析阶段面板
        analysis_phases = ["core_intent", "attraction_preferences", "food_preferences", "accommodation_preferences"]
        for phase in analysis_phases:
            phase_element = f"#step-{phase}"
            # 注意：这些元素可能在分析开始后才会出现，所以这里只检查容器存在
        
        print("✅ 前端页面加载测试通过")
    
    async def test_two_stage_workflow_ui(self, browser_setup):
        """测试两阶段工作流程UI交互"""
        page, browser, context, playwright = browser_setup
        
        # 访问重构版前端页面
        await page.goto("http://localhost:8000/static/travel_planner_v3_refactored.html")
        await page.wait_for_load_state("networkidle")
        
        # 填写用户查询
        test_query = "我想去北京玩3天，喜欢历史文化景点，需要自驾出行"
        await page.fill("#userQuery", test_query)
        
        # 填写车辆信息
        await page.select_option("#vehicleModel", "特斯拉Model 3")
        await page.fill("#vehicleRange", "500")
        await page.select_option("#chargingType", "快充")
        
        # 开始分析（Stage A）
        await page.click("#startAnalysisBtn")
        
        # 等待分析开始
        await page.wait_for_selector("#analysisSteps .analysis-step", timeout=10000)
        
        # 检查分析步骤是否出现
        await page.wait_for_timeout(3000)  # 等待一些分析步骤完成
        
        # 验证分析步骤的显示
        analysis_steps = await page.query_selector_all("#analysisSteps .analysis-step")
        assert len(analysis_steps) > 0, "分析步骤未显示"
        
        # 等待分析完成和立即规划按钮出现
        try:
            await page.wait_for_selector("#startPlanningBtn:visible", timeout=30000)
            print("✅ 分析阶段完成，立即规划按钮已显示")
            
            # 点击立即规划按钮（Stage B）
            await page.click("#startPlanningBtn")
            
            # 等待规划开始
            await page.wait_for_timeout(5000)
            
            # 检查是否切换到规划视图
            planning_view = await page.is_visible("#planningView")
            if planning_view:
                print("✅ 成功切换到规划视图")
            
        except Exception as e:
            print(f"⚠️ 分析阶段可能未完成或立即规划按钮未出现: {str(e)}")
            # 继续测试其他功能
        
        print("✅ 两阶段工作流程UI测试完成")
    
    async def test_voice_feedback_integration(self, browser_setup):
        """测试语音反馈集成"""
        page, browser, context, playwright = browser_setup
        
        # 访问重构版前端页面
        await page.goto("http://localhost:8000/static/travel_planner_v3_refactored.html")
        await page.wait_for_load_state("networkidle")
        
        # 检查语音控制按钮
        assert await page.is_visible("#ttsToggleAnalysis"), "语音开关按钮未找到"
        assert await page.is_visible("#pauseTTS"), "暂停语音按钮未找到"
        assert await page.is_visible("#stopTTS"), "停止语音按钮未找到"
        
        # 测试语音开关
        await page.click("#ttsToggleAnalysis")
        
        # 检查语音状态变化
        tts_button_text = await page.text_content("#ttsToggleAnalysis")
        print(f"语音按钮状态: {tts_button_text}")
        
        print("✅ 语音反馈集成测试通过")
    
    async def test_error_handling(self, browser_setup):
        """测试错误处理"""
        page, browser, context, playwright = browser_setup
        
        # 访问重构版前端页面
        await page.goto("http://localhost:8000/static/travel_planner_v3_refactored.html")
        await page.wait_for_load_state("networkidle")
        
        # 测试空查询提交
        await page.fill("#userQuery", "")
        await page.click("#startAnalysisBtn")
        
        # 检查是否显示错误提示
        await page.wait_for_timeout(2000)
        
        # 测试无效查询
        await page.fill("#userQuery", "a")  # 极短查询
        await page.click("#startAnalysisBtn")
        
        await page.wait_for_timeout(2000)
        
        print("✅ 错误处理测试完成")


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始旅行规划系统V3重构版端到端测试")
    
    test_instance = TestTravelPlannerV3Refactored()
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        browser_setup = (page, browser, context, playwright)
        
        try:
            # 运行所有测试
            await test_instance.test_v3_api_health_check(browser_setup)
            await test_instance.test_v3_api_tools_endpoint(browser_setup)
            await test_instance.test_frontend_page_load(browser_setup)
            await test_instance.test_two_stage_workflow_ui(browser_setup)
            await test_instance.test_voice_feedback_integration(browser_setup)
            await test_instance.test_error_handling(browser_setup)
            
            print("🎉 所有测试完成！")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            raise
        finally:
            await context.close()
            await browser.close()


if __name__ == "__main__":
    asyncio.run(run_all_tests())
