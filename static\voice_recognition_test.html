<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            font-size: 16px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .start-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .stop-btn {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
        }

        .stop-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
        }

        .clear-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .clear-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .status {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .connected {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 2px solid #28a745;
        }

        .disconnected {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 2px solid #dc3545;
        }

        .recording {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 2px solid #ffc107;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .result-area {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            min-height: 120px;
            position: relative;
        }

        .result-area h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .result-text {
            font-size: 18px;
            line-height: 1.8;
            color: #333;
            min-height: 60px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .log-area {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-area h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .log-text {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            color: #495057;
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-timestamp {
            color: #6c757d;
            font-weight: bold;
        }

        .log-message {
            color: #495057;
        }

        .error {
            color: #dc3545;
        }

        .success {
            color: #28a745;
        }

        .info {
            color: #17a2b8;
        }

        .recording-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background-color: #dc3545;
            border-radius: 50%;
            margin-right: 8px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }

            .stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 语音识别测试</h1>
            <p>基于WebSocket的实时语音识别系统</p>
        </div>
        
        <div class="controls">
            <button id="startBtn" class="btn start-btn" onclick="startRecording()">
                🎙️ 开始录音
            </button>
            <button id="stopBtn" class="btn stop-btn" onclick="stopRecording()" disabled>
                ⏹️ 停止录音
            </button>
            <button class="btn clear-btn" onclick="clearResults()">
                🗑️ 清空结果
            </button>
            <button id="playBtn" class="btn" onclick="playRecording()" disabled>
                ▶️ 播放录音
            </button>
            <input id="audioFileInput" type="file" accept="audio/*" style="display:none" onchange="uploadAudioFile(event)">
            <button id="uploadBtn" class="btn" onclick="document.getElementById('audioFileInput').click()">
                ⬆️ 上传音频
            </button>
        </div>
        
        <div id="status" class="status disconnected">
            <span id="statusText">未连接</span>
            <span id="recordingIndicator" class="recording-indicator" style="display: none;"></span>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div id="sessionCount" class="stat-value">0</div>
                <div class="stat-label">会话次数</div>
            </div>
            <div class="stat-item">
                <div id="audioChunks" class="stat-value">0</div>
                <div class="stat-label">音频块数</div>
            </div>
            <div class="stat-item">
                <div id="recognitionTime" class="stat-value">0s</div>
                <div class="stat-label">识别时间</div>
            </div>
        </div>
        
        <div class="result-area">
            <h3>📝 识别结果</h3>
            <div id="resultText" class="result-text">等待语音输入...</div>
        </div>
        
        <div class="log-area">
            <h3>📋 系统日志</h3>
            <div id="logText" class="log-text"></div>
        </div>
        <audio id="audioPlayer" controls style="width:100%; margin-top:10px; display:none;"></audio>
    </div>

    <script src="js/recorder.js"></script>
    <script>
        let ws = null;
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        let sessionId = null;
        let sessionCount = 0;
        let audioChunkCount = 0;
        let startTime = null;
        let lastAudioBlob = null;
        let recorder = null;
        let wavBlob = null;
        let audioContext = null;
        let input = null;
        let recordingStream = null;
        let uploadWs = null;
        let uploadSessionId = null;
        let uploadStartTime = null;
        let uploadAudioChunkCount = 0;

        function log(message, type = 'info') {
            const logText = document.getElementById('logText');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-message ${type}">${message}</span>
            `;
            logText.appendChild(logEntry);
            logText.scrollTop = logText.scrollHeight;
        }

        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            const statusText = document.getElementById('statusText');
            const recordingIndicator = document.getElementById('recordingIndicator');
            
            statusText.textContent = status;
            statusDiv.className = `status ${className}`;
            
            if (className === 'recording') {
                recordingIndicator.style.display = 'inline-block';
            } else {
                recordingIndicator.style.display = 'none';
            }
        }

        function updateResult(text) {
            document.getElementById('resultText').textContent = text;
        }

        function clearResults() {
            document.getElementById('resultText').textContent = '等待语音输入...';
            document.getElementById('logText').innerHTML = '';
            audioChunkCount = 0;
            document.getElementById('audioChunks').textContent = '0';
            document.getElementById('recognitionTime').textContent = '0s';
            document.getElementById('playBtn').disabled = true;
            document.getElementById('audioPlayer').style.display = 'none';
        }

        function updateStats() {
            document.getElementById('sessionCount').textContent = sessionCount;
            document.getElementById('audioChunks').textContent = audioChunkCount;
            
            if (startTime) {
                const elapsed = Math.round((Date.now() - startTime) / 1000);
                document.getElementById('recognitionTime').textContent = elapsed + 's';
            }
        }

        async function startRecording() {
            try {
                sessionCount++;
                startTime = Date.now();
                sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                // 先初始化audioContext以获取实际采样率
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const actualSampleRate = audioContext.sampleRate;
                // 连接WebSocket
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/voice/ws/${sessionId}`;
                log(`正在连接WebSocket: ${wsUrl}`, 'info');
                ws = new WebSocket(wsUrl);
                ws.onopen = function() {
                    log('✅ WebSocket连接已建立', 'success');
                    updateStatus('已连接', 'connected');
                    // 发送开始识别请求，采样率用动态获取的actualSampleRate
                    const startMessage = {
                        option: {
                            sample_rate: actualSampleRate,
                            enable_punctuation: true,
                            enable_inverse_text_normalization: true,
                            enable_emendation: false,
                            enable_words: false,
                            hotwords: [],
                            max_end_silence: 500
                        },
                        req_id: sessionId,
                        rec_status: 0
                    };
                    ws.send(JSON.stringify(startMessage));
                    log('📤 发送开始识别请求', 'info');
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    log(`📥 收到消息: ${JSON.stringify(message, null, 2)}`, 'info');
                    
                    if (message.code === 10000) {
                        if (message.res_status === 0) {
                            log('🎯 识别就绪，开始录音', 'success');
                            startAudioRecording();
                        } else if (message.res_status === 2 || message.res_status === 3) {
                            // 中间结果
                            if (message.data && message.data.results && message.data.results.length > 0) {
                                const text = message.data.results[0].text;
                                updateResult(text);
                                log(`🎤 中间识别结果: ${text}`, 'success');
                            }
                        } else if (message.res_status === 4) {
                            // 最终结果
                            if (message.data && message.data.results && message.data.results.length > 0) {
                                const text = message.data.results[0].text;
                                updateResult(text);
                                log(`🎉 最终识别结果: ${text}`, 'success');
                            }
                            stopRecording();
                        }
                    } else {
                        log(`❌ 识别错误: ${message.message}`, 'error');
                    }
                };
                
                ws.onclose = function() {
                    log('🔌 WebSocket连接已关闭', 'info');
                    updateStatus('连接已断开', 'disconnected');
                    stopAudioRecording();
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket错误: ${error}`, 'error');
                    updateStatus('连接错误', 'disconnected');
                };
                
            } catch (error) {
                log(`❌ 启动录音失败: ${error}`, 'error');
                alert('启动录音失败，请检查浏览器权限设置');
            }
        }

        async function startAudioRecording() {
            try {
                recordingStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });
                audioContext = audioContext || new (window.AudioContext || window.webkitAudioContext)();
                input = audioContext.createMediaStreamSource(recordingStream);
                recorder = new Recorder(input, { numChannels: 1 });
                recorder.record();
                isRecording = true;
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                updateStatus('录音中...', 'recording');
                log('🎙️ 开始录音', 'success');
                // 实时推流：每250ms导出一次裸PCM分片并推送
                window._recorderInterval = setInterval(function() {
                    if (recorder && isRecording) {
                        recorder.getBuffer(function(buffers) {
                            // buffers[0] 是 Float32Array
                            const pcm = floatTo16BitPCM(buffers[0]);
                            const uint8Arr = new Uint8Array(pcm.buffer);
                            const chunkSize = 6400;
                            let offset = 0;
                            while (offset < uint8Arr.length) {
                                const chunk = uint8Arr.slice(offset, offset + chunkSize);
                                let binary = '';
                                for (let i = 0; i < chunk.length; i++) {
                                    binary += String.fromCharCode(chunk[i]);
                                }
                                const base64Audio = btoa(binary);
                                if (ws && ws.readyState === WebSocket.OPEN) {
                                    ws.send(JSON.stringify({
                                        rec_status: 1,
                                        audio_stream: base64Audio
                                    }));
                                    audioChunkCount++;
                                    updateStats();
                                }
                                offset += chunkSize;
                            }
                            recorder.clear();
                        });
                    }
                }, 250); // 250ms一推
            } catch (error) {
                log(`❌ 获取麦克风权限失败: ${error}`, 'error');
                alert('无法访问麦克风，请检查浏览器权限设置');
            }
        }

        function stopRecording() {
            if (recorder && isRecording) {
                isRecording = false;
                recorder.stop();
                clearInterval(window._recorderInterval);
                // 导出最终wav
                recorder.exportWAV(function(blob) {
                    wavBlob = blob;
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    updateStatus('已连接', 'connected');
                    log('⏹️ 停止录音', 'info');
                });
                // 关闭音频流
                if (recordingStream) {
                    recordingStream.getTracks().forEach(track => track.stop());
                }
                // 发送rec_status: 2
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({ rec_status: 2 }));
                }
            }
            updateStats();
        }

        function stopAudioRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                isRecording = false;
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                updateStatus('已连接', 'connected');
                
                log('⏹️ 停止录音', 'info');
            }
        }

        function playRecording() {
            if (lastAudioBlob) {
                const audioUrl = URL.createObjectURL(lastAudioBlob);
                const audioPlayer = document.getElementById('audioPlayer');
                audioPlayer.src = audioUrl;
                audioPlayer.style.display = 'block';
                audioPlayer.play();
            }
        }

        function uploadAudioFile(event) {
            const file = event.target.files[0];
            if (!file) return;
            // 读取前44字节，解析采样率和data chunk起始位置
            const headerReader = new FileReader();
            headerReader.onload = function(e) {
                const header = new Uint8Array(e.target.result);
                // 采样率在24-27字节（little-endian）
                const sampleRate = header[24] | (header[25] << 8) | (header[26] << 16) | (header[27] << 24);
                // 查找data chunk起始位置
                let dataOffset = 12;
                while (dataOffset < header.length - 8) {
                    if (header[dataOffset] === 0x64 && header[dataOffset+1] === 0x61 && header[dataOffset+2] === 0x74 && header[dataOffset+3] === 0x61) {
                        // 找到"data"
                        dataOffset += 8; // 跳过"data"和长度
                        break;
                    }
                    dataOffset++;
                }
                if (dataOffset >= header.length) dataOffset = 44; // fallback
                sessionCount++;
                uploadStartTime = Date.now();
                uploadSessionId = 'upload_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/voice/ws/${uploadSessionId}`;
                log(`正在连接WebSocket: ${wsUrl}`, 'info');
                uploadWs = new WebSocket(wsUrl);
                uploadAudioChunkCount = 0;
                updateStats();
                uploadWs.onopen = function() {
                    log('✅ WebSocket连接已建立 (上传音频)', 'success');
                    updateStatus('已连接', 'connected');
                    // 只传sample_rate
                    const startMessage = {
                        option: {
                            sample_rate: sampleRate
                        },
                        req_id: uploadSessionId,
                        rec_status: 0
                    };
                    uploadWs.send(JSON.stringify(startMessage));
                    log('📤 发送开始识别请求 (上传音频)', 'info');
                };
                uploadWs.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    log(`📥 收到消息: ${JSON.stringify(message, null, 2)}`, 'info');
                    if (message.code === 10000) {
                        if (message.res_status === 0) {
                            log('🎯 识别就绪，开始推送音频', 'success');
                            sendAudioFileChunks(file, dataOffset);
                        } else if (message.res_status === 2 || message.res_status === 3) {
                            if (message.data && message.data.results && message.data.results.length > 0) {
                                const text = message.data.results[0].text;
                                updateResult(text);
                                log(`🎤 中间识别结果: ${text}`, 'success');
                            }
                        } else if (message.res_status === 4) {
                            if (message.data && message.data.results && message.data.results.length > 0) {
                                const text = message.data.results[0].text;
                                updateResult(text);
                                log(`🎉 最终识别结果: ${text}`, 'success');
                            }
                            uploadWs.close();
                        }
                    } else {
                        log(`❌ 识别错误: ${message.message}`, 'error');
                    }
                };
                uploadWs.onclose = function() {
                    log('🔌 WebSocket连接已关闭 (上传音频)', 'info');
                    updateStatus('连接已断开', 'disconnected');
                };
                uploadWs.onerror = function(error) {
                    log(`❌ WebSocket错误: ${error}`, 'error');
                    updateStatus('连接错误', 'disconnected');
                };
            };
            // 读取前128字节，足够覆盖大部分wav头
            headerReader.readAsArrayBuffer(file.slice(0, 128));
        }

        function sendAudioFileChunks(file, dataOffset) {
            const chunkSize = 6400; // 与voice_test.py一致
            let offset = dataOffset;
            const fileSize = file.size;
            function readNextChunk() {
                if (offset >= fileSize) {
                    // 发送rec_status: 2 表示结束
                    if (uploadWs && uploadWs.readyState === WebSocket.OPEN) {
                        uploadWs.send(JSON.stringify({ rec_status: 2 }));
                        log('📤 已发送rec_status:2 (上传音频)', 'info');
                    }
                    updateStats();
                    return;
                }
                const slice = file.slice(offset, offset + chunkSize);
                const reader = new FileReader();
                reader.onload = function(e) {
                    // 直接base64编码裸PCM数据
                    const arrayBuffer = e.target.result;
                    const uint8Arr = new Uint8Array(arrayBuffer);
                    let binary = '';
                    for (let i = 0; i < uint8Arr.length; i++) {
                        binary += String.fromCharCode(uint8Arr[i]);
                    }
                    const base64Audio = btoa(binary);
                    if (uploadWs && uploadWs.readyState === WebSocket.OPEN) {
                        uploadWs.send(JSON.stringify({
                            rec_status: 1,
                            audio_stream: base64Audio
                        }));
                        uploadAudioChunkCount++;
                        audioChunkCount++;
                        updateStats();
                    }
                    offset += chunkSize;
                    setTimeout(readNextChunk, 250); // 模拟推流节奏
                };
                reader.readAsArrayBuffer(slice);
            }
            readNextChunk();
        }

        function floatTo16BitPCM(float32Array) {
            const len = float32Array.length;
            const pcm = new Int16Array(len);
            for (let i = 0; i < len; i++) {
                let s = Math.max(-1, Math.min(1, float32Array[i]));
                pcm[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
            }
            return pcm;
        }

        // 定期更新统计信息
        setInterval(updateStats, 1000);

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close();
            }
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
        });

        // 初始化日志
        log('🚀 语音识别测试页面已加载', 'success');
        log('💡 点击"开始录音"按钮开始测试', 'info');
    </script>
</body>
</html> 