"""
统一架构基础组件单元测试

测试UnifiedToolRegistry、UnifiedEventBus、StandardAgentState等核心组件的功能
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# 导入要测试的组件
from src.tools.unified_registry import UnifiedToolRegistry, unified_registry
from src.services.unified_event_bus import UnifiedEventBus
from src.agents.travel_planner_lg.state import StandardAgentState


class TestUnifiedToolRegistry:
    """测试统一工具注册表"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.registry = UnifiedToolRegistry()
    
    def test_register_action_tool(self):
        """测试Action Tool注册"""
        @self.registry.register_action_tool
        async def test_action_tool(param1: str, param2: int = 10) -> str:
            """测试Action Tool
            
            Args:
                param1: 第一个参数
                param2: 第二个参数，默认为10
            """
            return f"result: {param1}, {param2}"
        
        # 验证工具已注册
        assert "test_action_tool" in self.registry._action_tools
        assert "test_action_tool" in self.registry._action_schemas
        
        # 验证Schema生成
        schema = self.registry._action_schemas["test_action_tool"]
        assert schema["type"] == "function"
        assert schema["function"]["name"] == "test_action_tool"
        assert "param1" in schema["function"]["parameters"]["properties"]
        assert "param2" in schema["function"]["parameters"]["properties"]
        assert "param1" in schema["function"]["parameters"]["required"]
        assert "param2" not in schema["function"]["parameters"]["required"]
    
    def test_register_planner_tool(self):
        """测试Planner Tool注册"""
        @self.registry.register_planner_tool
        def test_planner_tool(data: Dict[str, Any]) -> str:
            """测试Planner Tool"""
            return json.dumps(data)
        
        # 验证工具已注册
        assert "test_planner_tool" in self.registry._planner_tools
        
        # 验证可以获取工具
        tool = self.registry.get_planner_tool("test_planner_tool")
        assert tool is not None
        assert callable(tool)
    
    @pytest.mark.asyncio
    async def test_execute_action_tool(self):
        """测试Action Tool执行"""
        @self.registry.register_action_tool
        async def test_async_tool(message: str) -> str:
            """异步测试工具"""
            return f"processed: {message}"
        
        # 测试执行
        result = await self.registry.execute_action_tool("test_async_tool", message="hello")
        assert result == "processed: hello"
    
    @pytest.mark.asyncio
    async def test_execute_action_tool_with_event_bus(self):
        """测试带事件总线的Action Tool执行"""
        # 创建模拟事件总线
        mock_event_bus = AsyncMock()
        self.registry.set_event_bus(mock_event_bus)
        
        @self.registry.register_action_tool
        async def test_tool_with_events(data: str) -> str:
            """带事件的测试工具"""
            return f"result: {data}"
        
        # 执行工具
        result = await self.registry.execute_action_tool(
            "test_tool_with_events", 
            task_id="test_task", 
            data="test_data"
        )
        
        # 验证结果
        assert result == "result: test_data"
        
        # 验证事件发布
        assert mock_event_bus.notify_tool_execution.call_count == 2  # start + end
        
        # 验证start事件
        start_call = mock_event_bus.notify_tool_execution.call_args_list[0]
        assert start_call[0][1] == "test_tool_with_events"  # tool_name
        assert start_call[0][2] == "tool_start"  # event_type
        
        # 验证end事件
        end_call = mock_event_bus.notify_tool_execution.call_args_list[1]
        assert end_call[0][2] == "tool_end"  # event_type
        assert end_call[1]["status"] == "success"
    
    def test_get_tool_info(self):
        """测试获取工具信息"""
        @self.registry.register_action_tool
        async def action_tool():
            pass
        
        @self.registry.register_planner_tool
        def planner_tool():
            pass
        
        info = self.registry.get_tool_info()
        assert info["action_tools_count"] == 1
        assert info["planner_tools_count"] == 1
        assert "action_tool" in info["action_tools"]
        assert "planner_tool" in info["planner_tools"]


class TestUnifiedEventBus:
    """测试统一事件总线"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建模拟Redis客户端
        self.mock_redis = AsyncMock()
        # 模拟Redis客户端对象，UnifiedEventBus期望的是有client属性的对象
        self.mock_redis_client = Mock()
        self.mock_redis_client.client = self.mock_redis
        self.event_bus = UnifiedEventBus(self.mock_redis_client, task_ttl=3600)
    
    @pytest.mark.asyncio
    async def test_initialize_task(self):
        """测试任务初始化"""
        initial_state = {
            "task_id": "test_task",
            "current_phase": "initialization",
            "is_completed": False,
            "has_error": False
        }
        
        await self.event_bus.initialize_task("test_task", initial_state)
        
        # 验证Redis操作
        assert self.mock_redis.hset.called
        assert self.mock_redis.expire.called
    
    @pytest.mark.asyncio
    async def test_notify_phase_start(self):
        """测试阶段开始通知"""
        await self.event_bus.notify_phase_start(
            "test_task", 
            "framework_analysis", 
            "核心框架分析", 
            "正在分析..."
        )
        
        # 验证发布事件
        assert self.mock_redis.publish.called
        
        # 验证事件内容
        call_args = self.mock_redis.publish.call_args
        channel = call_args[0][0]
        message = call_args[0][1]
        
        assert channel == "task_channel:test_task"
        event_data = json.loads(message)
        assert event_data["event"] == "phase_start"
        assert event_data["data"]["phase_name"] == "framework_analysis"
    
    @pytest.mark.asyncio
    async def test_notify_phase_end(self):
        """测试阶段结束通知"""
        result = {"destinations": ["北京"], "days": 3}
        
        await self.event_bus.notify_phase_end(
            "test_task", 
            "framework_analysis", 
            "success", 
            result
        )
        
        # 验证发布事件
        assert self.mock_redis.publish.called
        
        # 验证事件内容
        call_args = self.mock_redis.publish.call_args
        message = call_args[0][1]
        event_data = json.loads(message)
        
        assert event_data["event"] == "phase_end"
        assert event_data["data"]["status"] == "success"
        assert event_data["data"]["result"] == result
    
    @pytest.mark.asyncio
    async def test_sync_from_agent_state(self):
        """测试从Agent状态同步"""
        agent_state = {
            "current_phase": "planning",
            "current_action": {"tool_name": "search_poi"},
            "daily_plans": {1: [{"name": "故宫"}]},
            "planning_log": ["开始规划"],
            "is_completed": False,
            "has_error": False
        }
        
        await self.event_bus.sync_from_agent_state("test_task", agent_state)
        
        # 验证Redis更新
        assert self.mock_redis.hset.called
        assert self.mock_redis.expire.called
    
    @pytest.mark.asyncio
    async def test_notify_error(self):
        """测试错误通知"""
        await self.event_bus.notify_error(
            "test_task", 
            "API调用失败", 
            "framework_analysis", 
            "search_poi"
        )
        
        # 验证发布了error和eos事件
        assert self.mock_redis.publish.call_count == 2
        
        # 验证error事件
        first_call = self.mock_redis.publish.call_args_list[0]
        error_message = json.loads(first_call[0][1])
        assert error_message["event"] == "error"
        
        # 验证eos事件
        second_call = self.mock_redis.publish.call_args_list[1]
        eos_message = json.loads(second_call[0][1])
        assert eos_message["event"] == "eos"


class TestStandardAgentState:
    """测试标准化Agent状态"""
    
    def test_state_structure(self):
        """测试状态结构"""
        # 创建一个基本的状态实例
        state: StandardAgentState = {
            "messages": [],
            "task_id": "test_task",
            "user_id": "test_user",
            "original_query": "去北京玩三天",
            "current_phase": "initialization",
            "execution_mode": "automatic",
            "framework_analysis": None,
            "preference_analysis": None,
            "consolidated_intent": None,
            "planning_log": [],
            "current_action": None,
            "daily_plans": {},
            "daily_time_tracker": {},
            "total_budget_tracker": 0.0,
            "tool_results": {},
            "notification_service": None,
            "user_profile": None,
            "user_memories": None,
            "vehicle_info": None,
            "final_itinerary": None,
            "is_completed": False,
            "has_error": False,
            "error_message": None,
            "destinations": None,
            "core_intent": None,
            "multi_city_strategy": None,
            "driving_context": None
        }
        
        # 验证基本字段
        assert state["task_id"] == "test_task"
        assert state["user_id"] == "test_user"
        assert state["current_phase"] == "initialization"
        assert state["execution_mode"] == "automatic"
        assert state["is_completed"] is False
        assert state["has_error"] is False
        
        # 验证集合类型字段
        assert isinstance(state["messages"], list)
        assert isinstance(state["planning_log"], list)
        assert isinstance(state["daily_plans"], dict)
        assert isinstance(state["tool_results"], dict)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
