#!/usr/bin/env python3
"""
POI修复验证测试脚本
验证POI重复显示和错误城市问题的修复效果
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.unified_registry import unified_registry
from src.tools.travel_planner.icp_tools import update_planning_state
import logging

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_poi_selection_fix():
    """测试POI选择修复"""
    print("🔍 测试POI选择修复...")
    
    try:
        # 初始化工具
        from src.agents.services.amap_service import AmapService
        from src.tools.travel_planner.amap_poi_tools import search_poi
        
        print("✅ 工具模块已导入")
        
        # 模拟搜索上海景点
        print(f"\n🏛️ 搜索上海景点...")
        search_results = await unified_registry.execute_action_tool(
            "search_poi",
            keywords="景点",
            city="上海",
            page_size=10
        )
        
        print(f"✅ 搜索到 {len(search_results)} 个POI")
        for i, poi in enumerate(search_results):
            print(f"  {i+1}. {poi.get('name', '未知')} - {poi.get('address', '未知地址')}")
        
        # 模拟不同天数的POI选择
        print(f"\n🗓️ 测试不同天数的POI选择...")
        
        for day in range(1, 4):  # 测试3天
            print(f"\n第{day}天POI选择:")
            
            # 计算POI选择的起始索引
            start_index = (day - 1) * 2
            end_index = start_index + 4
            
            if start_index >= len(search_results):
                start_index = 0
                end_index = min(4, len(search_results))
            elif end_index > len(search_results):
                end_index = len(search_results)
            
            selected_pois = search_results[start_index:end_index]
            print(f"  选择索引 {start_index}-{end_index-1}，共{len(selected_pois)}个POI:")
            
            for i, poi in enumerate(selected_pois):
                print(f"    {i+1}. {poi.get('name', '未知')}")
        
        # 测试update_planning_state函数
        print(f"\n🔧 测试update_planning_state函数...")
        
        # 模拟规划状态
        current_state = {
            "daily_plans": {},
            "daily_time_tracker": {},
            "tool_results": {},
            "planning_log": []
        }
        
        # 模拟第1天的POI搜索动作
        action_day1 = {
            "tool_name": "search_poi",
            "target_day": 1,
            "keywords": "景点",
            "city": "上海"
        }
        
        observation_day1 = {
            "success": True,
            "observation": "成功搜索到POI"
        }
        
        updated_state_day1 = await update_planning_state(
            current_state, action_day1, search_results, observation_day1
        )
        
        print(f"✅ 第1天规划状态更新完成")
        day1_activities = updated_state_day1.get("daily_plans", {}).get(1, [])
        print(f"  第1天活动数量: {len(day1_activities)}")
        for i, activity in enumerate(day1_activities):
            if isinstance(activity, dict):
                print(f"    {i+1}. {activity.get('name', '未知活动')}")
        
        # 模拟第2天的POI搜索动作
        action_day2 = {
            "tool_name": "search_poi",
            "target_day": 2,
            "keywords": "景点",
            "city": "上海"
        }
        
        observation_day2 = {
            "success": True,
            "observation": "成功搜索到POI"
        }
        
        updated_state_day2 = await update_planning_state(
            updated_state_day1, action_day2, search_results, observation_day2
        )
        
        print(f"✅ 第2天规划状态更新完成")
        day2_activities = updated_state_day2.get("daily_plans", {}).get(2, [])
        print(f"  第2天活动数量: {len(day2_activities)}")
        for i, activity in enumerate(day2_activities):
            if isinstance(activity, dict):
                print(f"    {i+1}. {activity.get('name', '未知活动')}")
        
        # 检查是否有重复POI
        print(f"\n🔄 检查POI重复情况...")
        day1_names = set()
        day2_names = set()
        
        for activity in day1_activities:
            if isinstance(activity, dict):
                day1_names.add(activity.get('name', ''))
        
        for activity in day2_activities:
            if isinstance(activity, dict):
                day2_names.add(activity.get('name', ''))
        
        common_names = day1_names & day2_names
        if common_names:
            print(f"⚠️ 发现重复POI: {list(common_names)}")
        else:
            print(f"✅ 两天的POI没有重复")
        
        print(f"\n📊 测试总结:")
        print(f"  - 搜索结果数量: {len(search_results)}")
        print(f"  - 第1天POI数量: {len(day1_activities)}")
        print(f"  - 第2天POI数量: {len(day2_activities)}")
        print(f"  - 重复POI数量: {len(common_names)}")
        
        return len(common_names) == 0  # 返回是否修复成功
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_poi_selection_fix())
    if success:
        print(f"\n🎉 POI重复问题修复成功！")
    else:
        print(f"\n❌ POI重复问题仍然存在")
