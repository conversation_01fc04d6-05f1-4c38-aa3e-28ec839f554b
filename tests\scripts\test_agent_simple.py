#!/usr/bin/env python3
"""
简单测试TravelPlannerAgent改造功能
"""
import asyncio
import sys

# 添加项目路径
sys.path.append('.')

from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import TravelPlanRequest, UserProfile

def test_preference_extraction():
    """测试偏好提取功能"""
    print("🧠 测试偏好提取功能")
    print("-" * 40)
    
    agent = TravelPlannerAgent()
    
    # 模拟记忆数据
    class MockMemory:
        def __init__(self, content, keywords, confidence=0.8):
            self.content = content
            self.keywords = keywords
            self.confidence_score = confidence
            self.memory_type = "preference"
    
    mock_memories = [
        MockMemory("用户特别喜欢博物馆和历史文化景点", ["历史", "文化"], 0.9),
        MockMemory("用户经常选择经济实惠的餐厅", ["经济", "餐厅"], 0.8),
        MockMemory("用户对自然风景很感兴趣", ["自然", "风景"], 0.7),
    ]
    
    # 测试记忆偏好提取
    preferences = agent._extract_preferences_from_memories(mock_memories)
    print(f"✅ 从记忆中提取的偏好: {preferences}")
    
    # 测试行程偏好提取
    mock_itineraries = [
        type('MockItinerary', (), {'destination_city': '北京', 'days': 3}),
        type('MockItinerary', (), {'destination_city': '上海', 'days': 2}),
        type('MockItinerary', (), {'destination_city': '杭州', 'days': 4}),
    ]
    
    itinerary_prefs = agent._extract_preferences_from_itineraries(mock_itineraries)
    print(f"✅ 从历史行程中提取的偏好: {itinerary_prefs}")
    
    return True

def test_entity_validation():
    """测试实体验证功能"""
    print("\n🔍 测试实体验证功能")
    print("-" * 40)
    
    agent = TravelPlannerAgent()
    
    # 测试不同格式的实体数据
    test_entities = [
        {
            "destination": "西安",
            "days": 3,
            "preferences": "历史文化"
        },
        {
            "destination_city": "北京",
            "travel_days": "2",
            "兴趣偏好": ["美食", "购物"]
        },
        {
            "目的地城市": "上海",
            "天数": 4,
            "特殊需求": None
        }
    ]
    
    for i, entities in enumerate(test_entities):
        print(f"测试实体 {i+1}: {entities}")
        validated = agent._validate_extracted_entities(entities)
        print(f"验证结果: {validated}")
        print()
    
    return True

def test_poi_scoring():
    """测试POI评分功能"""
    print("\n📊 测试POI评分功能")
    print("-" * 40)
    
    agent = TravelPlannerAgent()
    
    # 创建模拟状态
    from src.models.travel_planner import AgentState
    
    state = AgentState(
        trace_id="test-123",
        user_id="test-user",
        original_query="测试查询"
    )
    
    # 设置用户画像
    state.user_profile = UserProfile(
        user_id="test-user",
        preferences={"favorite_activities": ["文化历史", "美食"]},
        tags=["历史", "文化", "美食"],
        budget_preference="中等",
        travel_style="文化深度游"
    )
    
    # 设置用户记忆
    class MockMemory:
        def __init__(self, content, keywords, confidence=0.8):
            self.content = content
            self.keywords = keywords
            self.confidence_score = confidence
    
    state.user_memories = [
        MockMemory("用户喜欢博物馆", ["博物馆", "历史"], 0.9),
        MockMemory("用户偏好川菜", ["川菜", "美食"], 0.8),
    ]
    
    # 测试POI评分
    test_pois = [
        {
            "name": "兵马俑博物馆",
            "category": "景点",
            "type": "历史文化景点",
            "rating": "4.8"
        },
        {
            "name": "西安美食街",
            "category": "美食",
            "type": "美食街",
            "rating": "4.2"
        },
        {
            "name": "某购物中心",
            "category": "购物",
            "type": "商场",
            "rating": "4.0"
        }
    ]
    
    for poi in test_pois:
        score = agent._calculate_poi_score(poi, state)
        print(f"POI: {poi['name']} -> 评分: {score:.2f}")
    
    return True

def main():
    """主测试函数"""
    print("🧪 AutoPilot AI Agent改造功能简单测试")
    print("=" * 60)
    
    try:
        # 测试各个功能模块
        test1 = test_preference_extraction()
        test2 = test_entity_validation()
        test3 = test_poi_scoring()
        
        success = test1 and test2 and test3
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 Agent改造功能测试通过！")
            print("✅ 偏好提取功能正常")
            print("✅ 实体验证功能正常") 
            print("✅ POI评分功能正常")
            return 0
        else:
            print("❌ Agent改造功能测试失败")
            return 1
            
    except Exception as e:
        print(f"💥 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⛔ 测试被用户中断")
        sys.exit(1) 