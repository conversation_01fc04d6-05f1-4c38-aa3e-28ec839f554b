"""
SimpleAssistant Agent的单元测试

测试 src.agents.simple_assistant 模块的功能。
"""
import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import asyncio
from typing import Dict, Any


class TestSimpleAssistant:
    """测试SimpleAssistant类"""
    
    def test_simple_assistant_creation(self):
        """测试创建SimpleAssistant实例"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant(
            name="test_assistant",
            llm_role="basic",
            system_message="测试系统提示词"
        )
        
        assert assistant.name == "test_assistant"
        assert assistant.llm_role == "basic"
        assert assistant.system_message == "测试系统提示词"
        assert assistant.llm_manager is None
        assert len(assistant.conversation_history) == 0
        assert assistant.logger is not None
        assert assistant.perf_logger is not None
        assert assistant.trace_logger is not None
    
    def test_simple_assistant_creation_with_defaults(self):
        """测试使用默认参数创建SimpleAssistant"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        
        assert assistant.name == "assistant"
        assert assistant.llm_role == "basic"
        assert "有用的AI助手" in assistant.system_message
        assert len(assistant.conversation_history) == 0
    
    @pytest.mark.asyncio
    async def test_initialize_success(self):
        """测试成功初始化"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        
        # Mock get_default_manager
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_manager = MagicMock()
            mock_get_manager.return_value = mock_manager
            
            await assistant.initialize()
            
            assert assistant.llm_manager == mock_manager
            mock_get_manager.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_failure(self):
        """测试初始化失败"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.side_effect = Exception("初始化失败")
            
            with pytest.raises(Exception, match="初始化失败"):
                await assistant.initialize()
    
    @pytest.mark.asyncio
    async def test_chat_with_history(self):
        """测试带历史记录的对话"""
        from src.agents.simple_assistant import SimpleAssistant
        from src.core.llm_manager import ChatMessage
        
        assistant = SimpleAssistant()
        
        # Mock LLM响应
        mock_response = {
            "content": "这是测试回复",
            "model": "glm-4-flash",
            "usage": {"total_tokens": 50},
            "request_id": "test_req_123"
        }
        
        # Mock LLM管理器
        mock_manager = AsyncMock()
        mock_manager.chat.return_value = mock_response
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.return_value = mock_manager
            
            result = await assistant.chat("你好", use_history=True)
            
            # 验证返回结果
            assert result["agent_name"] == "assistant"
            assert result["user_message"] == "你好"
            assert result["assistant_reply"] == "这是测试回复"
            assert result["model_used"] == "glm-4-flash"
            assert result["tokens_used"]["total_tokens"] == 50
            assert result["request_id"] == "test_req_123"
            assert result["conversation_length"] == 2  # 用户+助手消息
            
            # 验证对话历史更新
            assert len(assistant.conversation_history) == 2
            assert assistant.conversation_history[0].role == "user"
            assert assistant.conversation_history[0].content == "你好"
            assert assistant.conversation_history[1].role == "assistant"
            assert assistant.conversation_history[1].content == "这是测试回复"
            
            # 验证LLM调用参数
            mock_manager.chat.assert_called_once()
            call_args = mock_manager.chat.call_args
            assert call_args.kwargs["message"] == "你好"
            assert call_args.kwargs["role"] == "basic"
            assert call_args.kwargs["system_message"] == assistant.system_message
    
    @pytest.mark.asyncio
    async def test_chat_without_history(self):
        """测试不使用历史记录的对话"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        
        mock_response = {
            "content": "无历史回复",
            "model": "glm-4-flash",
            "usage": {"total_tokens": 30},
            "request_id": "test_req_456"
        }
        
        mock_manager = AsyncMock()
        mock_manager.chat.return_value = mock_response
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.return_value = mock_manager
            
            result = await assistant.chat("测试消息", use_history=False)
            
            assert result["assistant_reply"] == "无历史回复"
            assert result["conversation_length"] == 0  # 不保存历史
            assert len(assistant.conversation_history) == 0
            
            # 验证LLM调用参数
            mock_manager.chat.assert_called_once_with(
                message="测试消息",
                role="basic",
                system_message=assistant.system_message,
                history=None
            )
    
    @pytest.mark.asyncio
    async def test_chat_auto_initialize(self):
        """测试对话时自动初始化"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        assert assistant.llm_manager is None
        
        mock_response = {
            "content": "自动初始化回复",
            "model": "glm-4-flash",
            "usage": {"total_tokens": 40},
            "request_id": "test_req_789"
        }
        
        mock_manager = AsyncMock()
        mock_manager.chat.return_value = mock_response
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.return_value = mock_manager
            
            result = await assistant.chat("自动初始化测试")
            
            assert assistant.llm_manager is not None
            assert result["assistant_reply"] == "自动初始化回复"
    
    @pytest.mark.asyncio
    async def test_chat_error_handling(self):
        """测试对话错误处理"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        
        mock_manager = AsyncMock()
        mock_manager.chat.side_effect = Exception("LLM调用失败")
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.return_value = mock_manager
            
            with pytest.raises(Exception, match="LLM调用失败"):
                await assistant.chat("错误测试")
    
    @pytest.mark.asyncio
    async def test_reset_conversation(self):
        """测试重置对话历史"""
        from src.agents.simple_assistant import SimpleAssistant
        from src.core.llm_manager import ChatMessage
        
        assistant = SimpleAssistant()
        
        # 添加一些历史记录
        assistant.conversation_history = [
            ChatMessage(role="user", content="消息1"),
            ChatMessage(role="assistant", content="回复1"),
            ChatMessage(role="user", content="消息2"),
            ChatMessage(role="assistant", content="回复2")
        ]
        
        assert len(assistant.conversation_history) == 4
        
        await assistant.reset_conversation()
        
        assert len(assistant.conversation_history) == 0
    
    def test_get_conversation_summary_empty(self):
        """测试空对话的摘要"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant(name="test_agent")
        summary = assistant.get_conversation_summary()
        
        assert summary["agent_name"] == "test_agent"
        assert summary["total_exchanges"] == 0
        assert summary["total_messages"] == 0
        assert summary["user_message_count"] == 0
        assert summary["assistant_message_count"] == 0
        assert summary["total_user_characters"] == 0
        assert summary["total_assistant_characters"] == 0
        assert summary["average_user_message_length"] == 0
        assert summary["average_assistant_message_length"] == 0
    
    def test_get_conversation_summary_with_data(self):
        """测试有数据的对话摘要"""
        from src.agents.simple_assistant import SimpleAssistant
        from src.core.llm_manager import ChatMessage
        
        assistant = SimpleAssistant(name="summary_test")
        
        # 添加测试对话历史
        assistant.conversation_history = [
            ChatMessage(role="user", content="你好"),      # 2字符
            ChatMessage(role="assistant", content="你好，很高兴见到你！"),  # 10字符
            ChatMessage(role="user", content="今天天气怎么样？"),  # 8字符
            ChatMessage(role="assistant", content="今天天气很好"),  # 6字符
        ]
        
        summary = assistant.get_conversation_summary()
        
        assert summary["agent_name"] == "summary_test"
        assert summary["total_exchanges"] == 2
        assert summary["total_messages"] == 4
        assert summary["user_message_count"] == 2
        assert summary["assistant_message_count"] == 2
        assert summary["total_user_characters"] == 10  # 2 + 8
        assert summary["total_assistant_characters"] == 16  # 10 + 6
        assert summary["average_user_message_length"] == 5.0  # 10 / 2
        assert summary["average_assistant_message_length"] == 8.0  # 16 / 2


class TestSimpleAssistantFactory:
    """测试SimpleAssistant工厂函数"""
    
    @pytest.mark.asyncio
    async def test_create_simple_assistant_with_defaults(self):
        """测试使用默认参数创建助手"""
        from src.agents.simple_assistant import create_simple_assistant
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_manager = MagicMock()
            mock_get_manager.return_value = mock_manager
            
            assistant = await create_simple_assistant()
            
            assert assistant.name == "assistant"
            assert assistant.llm_role == "basic"
            assert assistant.llm_manager == mock_manager
    
    @pytest.mark.asyncio
    async def test_create_simple_assistant_with_custom_params(self):
        """测试使用自定义参数创建助手"""
        from src.agents.simple_assistant import create_simple_assistant
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_manager = MagicMock()
            mock_get_manager.return_value = mock_manager
            
            assistant = await create_simple_assistant(
                name="custom_assistant",
                llm_role="reasoning",
                system_message="自定义系统提示"
            )
            
            assert assistant.name == "custom_assistant"
            assert assistant.llm_role == "reasoning"
            assert assistant.system_message == "自定义系统提示"
            assert assistant.llm_manager == mock_manager
    
    @pytest.mark.asyncio
    async def test_quick_chat_function(self):
        """测试快速对话函数"""
        from src.agents.simple_assistant import quick_chat
        
        mock_response = {
            "content": "快速回复",
            "model": "glm-4-flash",
            "usage": {"total_tokens": 25},
            "request_id": "quick_test"
        }
        
        mock_manager = AsyncMock()
        mock_manager.chat.return_value = mock_response
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.return_value = mock_manager
            
            reply = await quick_chat("快速测试消息", "quick_agent")
            
            assert reply == "快速回复"
            
            # 验证调用参数
            mock_manager.chat.assert_called_once()
            call_args = mock_manager.chat.call_args
            assert call_args.kwargs["message"] == "快速测试消息"
            assert call_args.kwargs["history"] is None  # 快速对话不使用历史


class TestSimpleAssistantIntegration:
    """SimpleAssistant集成测试"""
    
    @pytest.mark.asyncio
    async def test_multiple_conversation_rounds(self):
        """测试多轮对话"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        
        # Mock多个LLM响应
        responses = [
            {
                "content": "第一轮回复",
                "model": "glm-4-flash",
                "usage": {"total_tokens": 20},
                "request_id": "round_1"
            },
            {
                "content": "第二轮回复",
                "model": "glm-4-flash", 
                "usage": {"total_tokens": 25},
                "request_id": "round_2"
            },
            {
                "content": "第三轮回复",
                "model": "glm-4-flash",
                "usage": {"total_tokens": 30},
                "request_id": "round_3"
            }
        ]
        
        mock_manager = AsyncMock()
        mock_manager.chat.side_effect = responses
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.return_value = mock_manager
            
            # 第一轮对话
            result1 = await assistant.chat("第一个问题")
            assert result1["assistant_reply"] == "第一轮回复"
            assert result1["conversation_length"] == 2
            
            # 第二轮对话
            result2 = await assistant.chat("第二个问题")
            assert result2["assistant_reply"] == "第二轮回复"
            assert result2["conversation_length"] == 4
            
            # 第三轮对话
            result3 = await assistant.chat("第三个问题")
            assert result3["assistant_reply"] == "第三轮回复"
            assert result3["conversation_length"] == 6
            
            # 验证对话历史
            assert len(assistant.conversation_history) == 6
            
            # 验证摘要
            summary = assistant.get_conversation_summary()
            assert summary["total_exchanges"] == 3
            assert summary["user_message_count"] == 3
            assert summary["assistant_message_count"] == 3
    
    @pytest.mark.asyncio
    async def test_conversation_with_reset(self):
        """测试带重置的对话流程"""
        from src.agents.simple_assistant import SimpleAssistant
        
        assistant = SimpleAssistant()
        
        mock_response = {
            "content": "测试回复",
            "model": "glm-4-flash",
            "usage": {"total_tokens": 20},
            "request_id": "test_with_reset"
        }
        
        mock_manager = AsyncMock()
        mock_manager.chat.return_value = mock_response
        
        with patch('src.agents.simple_assistant.get_default_manager') as mock_get_manager:
            mock_get_manager.return_value = mock_manager
            
            # 进行几轮对话
            await assistant.chat("问题1")
            await assistant.chat("问题2")
            assert len(assistant.conversation_history) == 4
            
            # 重置对话
            await assistant.reset_conversation()
            assert len(assistant.conversation_history) == 0
            
            # 重置后继续对话
            result = await assistant.chat("重置后的问题")
            assert result["conversation_length"] == 2
            assert len(assistant.conversation_history) == 2 