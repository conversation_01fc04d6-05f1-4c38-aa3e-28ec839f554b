# 开发顺序与里程碑规划

**版本**: 1.0

## 核心开发理念：测试驱动与迭代演进

本项目严格遵循**测试驱动开发（TDD）**的核心理念。每一个功能模块的开发都必须遵循"**红-绿-重构**"的循环，确保代码质量和功能正确性。

**开发流程的强制约束**:
1.  **编写测试 (红)**: 针对新功能或Bug修复，在 `tests/` 目录下先编写一个或多个失败的单元测试。
2.  **编写实现 (绿)**: 编写最精简的功能代码，刚好让测试通过。
3.  **重构**: 在测试保护下，优化代码结构和性能。

**验收标准**: 任何代码的合并请求（Pull Request）都**必须**包含其对应的单元测试。没有测试的功能代码将被拒绝合并。

---

## 开发阶段与顺序规划

以下规划的每一个"任务"，都代表一个完整的TDD循环。其"产出物"不仅包括功能代码，也必然包括了对应的、通过的单元测试代码。此顺序与《Agent 技术实现规范.md》中定义的开发优先级完全对应。

### 阶段一：核心框架与基础服务 (Milestone 1)

**目标**: 搭建项目的骨架，确保核心服务能够跑通，为后续的功能开发提供一个稳定、可扩展且经过充分测试的基础。

| 序号 | 任务名称 (TDD) | 产出物 | 核心测试点 |
| :--- | :--- | :--- | :--- |
| 1.1 | **项目初始化** | 创建完整的目录结构，配置`pyproject.toml` | - |
| 1.2 | **配置管理系统** | `src/core/config.py` 及 `tests/core/test_config.py` | 能够从`.env`和YAML文件正确加载配置；支持按角色(`reasoning`, `basic`)区分的LLM配置；对缺失的配置能抛出异常。 |
| 1.3 | **API服务骨架** | `src/main.py`, `src/api/` 及 `tests/api/test_health.py`| FastAPI应用能启动，`/health`健康检查端点返回200 OK。 |
| 1.4 | **基础日志集成** | 在`src/main.py`中配置`structlog` | (集成测试)验证API端点能输出符合规范的结构化日志，用于应用层监控。 |
| 1.5 | **Pydantic模型定义** | `src/models/`下的文件及对应测试 | `Plan`, `AgentState`等核心模型能被正确创建、验证和序列化/反序列化。 |
| 1.6 | **工具注册表实现** | `src/tools/registry.py`及`tests/tools/test_registry.py` | `@register_tool`装饰器可用；`get_tools`能返回正确的`FunctionTool`列表；对未注册的工具请求能抛出异常。 |

### 阶段二：核心工具与单Agent工作流 (Milestone 2)

**目标**: 实现最简单的"单Agent + 工具"的任务闭环，并将所有核心I/O及状态操作"工具化"，为Agent提供标准化的接口。

| 序号 | 任务名称 (TDD) | 产出物 | 核心测试点 |
| :--- | :--- | :--- | :--- |
| 2.1 | **实现一个本地工具** | `src/tools/web_search.py`及对应测试 (可模拟) | 工具函数逻辑正确，符合"本地函数调用"工具的规范。 |
| 2.2 | **实现`ExecutorAgent`** | `src/agents/researcher.py`及对应测试 | 在模拟LLM返回`tool_calls`后，Agent能用正确的参数调用被模拟的工具。 |
| 2.3 | **实现日志工具** | `src/tools/logger.py`及对应测试 | Agent可以通过调用`log_info(...)`等工具函数，输出带有任务上下文的结构化日志。 |
| 2.4 | **实现状态管理工具** | `src/tools/state_manager.py`及对应测试 | 实现`get_state`, `update_state`等工具，封装与Redis的交互（测试中应模拟Redis）。 |
| 2.5 | **实现响应输出工具** | `src/tools/output.py`及对应测试 | 实现`send_sse_event`工具，封装SSE通信逻辑，供`ResponseAgent`等调用。 |
| 2.6 | **(集成测试)单Agent流程** | `tests/integration/test_single_agent_flow.py` | 模拟一个API请求，验证`ResearcherAgent`能完成一次工具调用并使用`OutputTool`返回结果。 |

### 阶段三：多Agent协作与规划 (Milestone 3)

**目标**: 实现多Agent协同的核心——规划与图编排能力，打通完整的复杂任务处理链路。

| 序号 | 任务名称 (TDD) | 产出物 | 核心测试点 |
| :--- | :--- | :--- | :--- |
| 3.1 | **提示词工程** | `src/prompts/`下的文件及`PromptManager`的测试 | `PromptManager`能正确加载并渲染出包含`CURRENT_DATETIME`等动态变量的完整提示词。 |
| 3.2 | **实现`PlannerAgent`** | `src/agents/planner.py`及对应测试 | 接收用户请求后，Agent能调用LLM并生成一个符合`Plan`模型结构的JSON。 |
| 3.3 | **图编排逻辑** | 在`GroupChat`中实现`selector_func`及对应测试 | `selector_func`能根据从**状态管理工具**中获取的`Plan`状态，正确决定下一个应发言的Agent。 |
| 3.4 | **实现`ResponseAgent`** | `src/agents/response.py`及对应测试 | Agent能将从**状态管理工具**获取的`execution_history`汇总，并调用**响应输出工具**生成最终响应。 |

### 阶段四及以后：记忆、安全与优化

**目标**: 为系统增加长期记忆、安全加固和性能优化，使其达到生产可用标准。

*   **实现分层记忆系统**: 遵循《Agent技术实现规范》，实现`MemoryManager`并将其作为工具集提供给Agent。特别地，实现L2长期记忆时，将集成`Embedding`模型进行记忆向量化与召回，并引入`Reranker`模型进行结果精排。
*   **接入MCP工具**: 按照规范封装第一个外部MCP工具，并进行集成测试。
*   **完善安全机制**: 完整实现JWT鉴权、API限流和所有在安全规范中定义的策略。
*   **性能与监控**: 完整实现缓存策略、接入Prometheus和分布式追踪系统。
*   **部署与运维**: 编写Dockerfile，完善CI/CD流程。 