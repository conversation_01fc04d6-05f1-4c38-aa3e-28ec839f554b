"""
Map Tool implementation using AutoNavi (Gaode) Map Web API
"""
import json
import requests
from typing import Dict, List, Optional, Union
from dataclasses import dataclass

@dataclass
class Location:
    """Location data structure"""
    latitude: float
    longitude: float
    name: Optional[str] = None
    address: Optional[str] = None

@dataclass
class POIResult:
    """POI search result data structure"""
    id: str
    name: str
    type: str
    address: str
    location: Location
    distance: Optional[float] = None
    rating: Optional[float] = None
    price: Optional[float] = None
    photos: Optional[List[str]] = None
    image: Optional[str] = None
    phone: Optional[str] = None
    business_hours: Optional[str] = None
    price_range: Optional[str] = None

class MapTool:
    """
    Map tool implementation using AutoNavi (Gaode) Map API
    Can be wrapped as an AutoGen FunctionTool
    """
    def __init__(self, api_key: str = "cd978c562fe54dd9a11117bfd4a2a3f1", base_url: str = "https://restapi.amap.com/v3"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

    def search_pois(
        self,
        keywords: str,
        city: str,
        type_: Optional[str] = None,
        location: Optional[Location] = None,
        radius: Optional[int] = 3000,
        sort_by: str = "distance",
        price_range: Optional[tuple[float, float]] = None,
        page: int = 1,
        page_size: int = 10
    ) -> List[POIResult]:
        """
        Search POIs (Points of Interest) by keywords and other criteria
        
        Args:
            keywords: Search keywords
            city: City name or code
            type_: POI type category
            location: Center location for nearby search
            radius: Search radius in meters
            sort_by: Sort criteria ("distance" or "rating")
            price_range: Tuple of (min_price, max_price)
            page: Page number
            page_size: Results per page
            
        Returns:
            List of POIResult objects
        """
        url = f"{self.base_url}/place/text"
        
        params = {
            "key": self.api_key,
            "keywords": keywords,
            "city": city,
            "output": "json",
            "offset": page_size,
            "page": page,
            "extensions": "all"
        }
        
        if type_:
            params["types"] = type_
            
        if location:
            params["location"] = f"{location.longitude},{location.latitude}"
            if radius:
                params["radius"] = radius

        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        
        data = response.json()
        if data["status"] != "1":
            raise Exception(f"API Error: {data.get('info', 'Unknown error')}")
            
        pois = []
        for poi in data["pois"]:
            location_parts = poi["location"].split(",")
            poi_location = Location(
                longitude=float(location_parts[0]),
                latitude=float(location_parts[1]),
                name=poi["name"],
                address=poi["address"]
            )
            # Extract price if available
            price = None
            if "biz_ext" in poi and "cost" in poi["biz_ext"]:
                try:
                    if poi["biz_ext"]["cost"] not in (None, "", []):
                        price = float(poi["biz_ext"]["cost"])
                except (ValueError, TypeError):
                    pass

            # Extract photos if available
            photos = []
            image = None
            if "photos" in poi and poi["photos"]:
                for photo in poi["photos"]:
                    if isinstance(photo, dict) and "url" in photo:
                        photos.append(photo["url"])
                    elif isinstance(photo, str):
                        photos.append(photo)
                if photos:
                    image = photos[0]  # Use first photo as main image

            # Extract other details
            phone = poi.get("tel", "")
            business_hours = ""
            if "business" in poi and "opentime" in poi["business"]:
                business_hours = poi["business"]["opentime"]

            # Extract rating
            rating = 0
            if "biz_ext" in poi and "rating" in poi["biz_ext"]:
                try:
                    rating = float(poi["biz_ext"]["rating"])
                except (ValueError, TypeError):
                    pass

            # Extract distance
            distance = 0
            if "distance" in poi:
                try:
                    distance = float(poi["distance"])
                except (ValueError, TypeError):
                    pass

            # Filter by price range if specified
            # if price_range and price:
            #     min_price, max_price = price_range
            #     if price < min_price or price > max_price:
            #         continue

            result = POIResult(
                id=poi["id"],
                name=poi["name"],
                type=poi["type"],
                address=poi["address"],
                location=poi_location,
                distance=distance,
                rating=rating,
                price=price,
                photos=photos,
                image=image,
                phone=phone,
                business_hours=business_hours,
                price_range=poi.get("biz_ext", {}).get("cost", "") if poi.get("biz_ext") else ""
            )
            pois.append(result)
            
        # Sort results if needed
        if sort_by == "rating":
            pois.sort(key=lambda x: x.rating or 0, reverse=True)
        elif sort_by == "distance":
            pois.sort(key=lambda x: x.distance or float('inf'))
            
        return pois
        
    def get_route(
        self,
        origin: Location,
        destination: Location,
        waypoints: Optional[List[Location]] = None,
        transport_mode: str = "driving",
        city: str = "010",
        cityd: str = "010"
    ) -> Dict:
        """
        Get route between locations
        
        Args:
            origin: Starting location
            destination: Ending location
            waypoints: Optional list of waypoints
            transport_mode: Mode of transport ("driving", "walking", "transit", "cycling")
            
        Returns:
            Dict containing route information
        """
        url = f"{self.base_url}/direction/{transport_mode}"
        if transport_mode == "transit":
            url = f"{self.base_url}/direction/{transport_mode}/integrated"

        params = {
            "key": self.api_key,
            "origin": f"{origin.longitude},{origin.latitude}",
            "destination": f"{destination.longitude},{destination.latitude}",
            "output": "json",
            "extensions": "all",
            "city": city,
            "cityd": cityd,
        }

        if waypoints:
            waypoints_str = ";".join(
                [f"{point.longitude},{point.latitude}" for point in waypoints]
            )
            params["waypoints"] = waypoints_str
            
        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        
        data = response.json()
        if data["status"] != "1":
            raise Exception(f"API Error: {data.get('info', 'Unknown error')}")
            
        return data["route"]

    def search_poi_around(
        self,
        location: str,
        key: Optional[str] = None,
        keywords: Optional[str] = None,
        types: Optional[str] = None,
        radius: Optional[int] = None,
        offset: Optional[int] = None,
        page: Optional[int] = None,
        extensions: str = "base",
        sig: Optional[str] = None
    ) -> dict:
        """
        Search for POIs around a specified location (Gaode /v3/place/around)
        Args:
            location: Center point coordinates (longitude,latitude)
            key: API key (default to self.api_key)
            keywords: Search keywords
            types: POI type
            radius: Search radius in meters
            offset: Results per page
            page: Page number
            extensions: Level of detail ('base' or 'all')
            sig: Digital signature
        Returns:
            dict: API response
        """
        url = f"{self.base_url}/place/around"
        params = {
            "key": key or self.api_key,
            "location": location,
            "extensions": extensions
        }
        if keywords:
            params["keywords"] = keywords
        if types:
            params["types"] = types
        if radius:
            params["radius"] = radius
        if offset:
            params["offset"] = offset
        if page:
            params["page"] = page
        if sig:
            params["sig"] = sig
        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def regeo(
        self,
        location: str,
        key: Optional[str] = None,
        radius: Optional[int] = None,
        extensions: str = "base",
        sig: Optional[str] = None
    ) -> dict:
        """
        Convert geographic coordinates into a human-readable address (Gaode /v3/geocode/regeo)
        Args:
            location: Geographic coordinates (longitude,latitude)
            key: API key (default to self.api_key)
            radius: Search radius in meters
            extensions: Level of detail ('base' or 'all')
            sig: Digital signature
        Returns:
            dict: API response
        """
        url = f"{self.base_url}/geocode/regeo"
        params = {
            "key": key or self.api_key,
            "location": location,
            "extensions": extensions
        }
        if radius:
            params["radius"] = radius
        if sig:
            params["sig"] = sig
        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def get_weather_info(
        self,
        city: str,
        key: Optional[str] = None,
        extensions: str = "base",
        output: str = "JSON",
        sig: Optional[str] = None
    ) -> dict:
        """
        Get weather information for a specified city (Gaode /v3/weather/weatherInfo)
        Args:
            city: City code (adcode)
            key: API key (default to self.api_key)
            extensions: Level of detail ('base' or 'all')
            output: Response format ('JSON' or 'XML')
            sig: Digital signature
        Returns:
            dict: API response
        """
        url = f"{self.base_url}/weather/weatherInfo"
        params = {
            "key": key or self.api_key,
            "city": city,
            "extensions": extensions,
            "output": output
        }
        if sig:
            params["sig"] = sig
        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def place_detail_by_id(
        self,
        id: str,
        key: Optional[str] = None,
        sig: Optional[str] = None,
        callback: Optional[str] = None
    ) -> dict:
        """
        Query POI detail by ID (Gaode /v3/place/detail)
        Args:
            id: AOI unique identifier (poiid)
            key: API key (default to self.api_key)
            sig: Digital signature
            callback: Callback function name (for JSONP)
        Returns:
            dict: API response
        """
        url = f"{self.base_url}/place/detail"
        params = {
            "key": key or self.api_key,
            "id": id
        }
        if sig:
            params["sig"] = sig
        if callback:
            params["callback"] = callback
        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def geocode_address(
        self,
        address: str,
        key: Optional[str] = None,
        city: Optional[str] = None,
        sig: Optional[str] = None,
        output: str = "JSON",
        callback: Optional[str] = None
    ) -> dict:
        """
        Geocode structured address to coordinates (Gaode /v3/geocode/geo)
        Args:
            address: Structured address information
            key: API key (default to self.api_key)
            city: City name/code/adcode (optional)
            sig: Digital signature (optional)
            output: Response format ('JSON' or 'XML')
            callback: Callback function name (for JSONP)
        Returns:
            dict: API response
        """
        url = f"{self.base_url}/geocode/geo"
        params = {
            "key": key or self.api_key,
            "address": address,
            "output": output
        }
        if city:
            params["city"] = city
        if sig:
            params["sig"] = sig
        if callback:
            params["callback"] = callback
        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        return response.json()

# Example of how to wrap this as an AutoGen FunctionTool:
"""
from autogen import FunctionTool

map_tool = MapTool(api_key="your_api_key")

search_pois_tool = FunctionTool(
    name="search_pois",
    description="Search for points of interest (POIs) like restaurants, hotels, attractions etc.",
    func=map_tool.search_pois,
    async_fn=False
)

get_route_tool = FunctionTool(
    name="get_route",
    description="Get route directions between two locations",
    func=map_tool.get_route,
    async_fn=False
)
"""
