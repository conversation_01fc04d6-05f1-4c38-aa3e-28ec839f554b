#!/usr/bin/env python3
"""
测试完整的旅行规划工作流程
包括分析阶段和规划阶段的端到端测试
"""

import asyncio
import aiohttp
import json
import sys
import time

async def test_analysis_phase(session, trace_id, user_id, query):
    """测试分析阶段"""
    print("🔍 开始测试分析阶段...")
    
    url = f"http://localhost:8000/api/travel/plan/{trace_id}/stream"
    params = {
        "user_id": user_id,
        "query": query,
        "phase": "analysis"
    }
    
    analysis_steps_received = []
    
    async with session.get(url, params=params) as response:
        if response.status != 200:
            print(f"❌ 分析阶段连接失败: {response.status}")
            return False
            
        print("✅ 分析阶段SSE连接建立成功")
        
        async for line in response.content:
            line = line.decode('utf-8').strip()
            
            if line.startswith('data: '):
                data_str = line[6:]
                try:
                    event_data = json.loads(data_str)
                    event_type = event_data.get('event_type')
                    payload = event_data.get('payload', {})
                    
                    if event_type == 'analysis_step':
                        step_type = payload.get('step_type')
                        title = payload.get('title')
                        content = payload.get('content')
                        print(f"   📊 {title}: {content}")
                        analysis_steps_received.append(step_type)
                        
                    elif event_type == 'complete':
                        print("✅ 分析阶段完成")
                        break
                        
                except json.JSONDecodeError:
                    continue
    
    expected_steps = ['user_intent', 'poi_preference', 'food_preference', 'accommodation_preference']
    missing_steps = [step for step in expected_steps if step not in analysis_steps_received]
    
    if missing_steps:
        print(f"❌ 分析阶段缺失步骤: {missing_steps}")
        return False
    else:
        print(f"✅ 分析阶段所有步骤完成: {analysis_steps_received}")
        return True

async def test_planning_phase(session, trace_id, user_id, query):
    """测试规划阶段"""
    print("\n🗺️  开始测试规划阶段...")
    
    url = f"http://localhost:8000/api/travel/plan/{trace_id}/stream"
    params = {
        "user_id": user_id,
        "query": query,
        "phase": "planning"
    }
    
    planning_events_received = []
    
    async with session.get(url, params=params) as response:
        if response.status != 200:
            print(f"❌ 规划阶段连接失败: {response.status}")
            return False
            
        print("✅ 规划阶段SSE连接建立成功")
        
        async for line in response.content:
            line = line.decode('utf-8').strip()
            
            if line.startswith('data: '):
                data_str = line[6:]
                try:
                    event_data = json.loads(data_str)
                    event_type = event_data.get('event_type')
                    payload = event_data.get('payload', {})
                    
                    if event_type == 'itinerary_generated':
                        daily_count = payload.get('daily_count')
                        print(f"   📅 行程生成: {daily_count}天")
                        planning_events_received.append('itinerary_generated')
                        
                    elif event_type == 'planning_completed':
                        message = payload.get('message')
                        total_days = payload.get('total_days')
                        print(f"   ✅ {message} (总计{total_days}天)")
                        planning_events_received.append('planning_completed')
                        
                    elif event_type == 'complete':
                        print("✅ 规划阶段完成")
                        break
                        
                except json.JSONDecodeError:
                    continue
    
    expected_events = ['itinerary_generated', 'planning_completed']
    success = all(event in planning_events_received for event in expected_events)
    
    if success:
        print(f"✅ 规划阶段所有事件完成: {planning_events_received}")
        return True
    else:
        print(f"❌ 规划阶段缺失事件: {planning_events_received}")
        return False

async def test_complete_workflow():
    """测试完整的工作流程"""
    
    # 测试参数
    user_id = "1"
    query = "上海3天亲子慢节奏趣味性儿童友好"
    trace_id = f"test_complete_{int(time.time())}"
    
    print("🚀 开始完整工作流程测试")
    print(f"📝 查询: {query}")
    print(f"👤 用户ID: {user_id}")
    print(f"🆔 Trace ID: {trace_id}")
    print("=" * 60)
    
    try:
        async with aiohttp.ClientSession() as session:
            # 第一阶段：分析
            analysis_success = await test_analysis_phase(session, trace_id, user_id, query)
            
            if not analysis_success:
                print("❌ 分析阶段失败，终止测试")
                return False
            
            # 等待一下再进行规划阶段
            await asyncio.sleep(2)
            
            # 第二阶段：规划
            planning_success = await test_planning_phase(session, trace_id, user_id, query)
            
            if not planning_success:
                print("❌ 规划阶段失败")
                return False
            
            print("\n🎉 完整工作流程测试成功！")
            print("✅ 分析阶段: 通过")
            print("✅ 规划阶段: 通过")
            print("✅ 端到端流程: 通过")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 启动完整工作流程测试...")
    success = asyncio.run(test_complete_workflow())
    
    if success:
        print("\n🎯 测试结论: 应用已完整可用！")
        sys.exit(0)
    else:
        print("\n💥 测试结论: 应用存在问题，需要修复")
        sys.exit(1)
