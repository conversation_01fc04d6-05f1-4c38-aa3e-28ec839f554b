"""
天气API接口

提供天气查询的RESTful API接口。
"""
from typing import Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from tools.Amap.map_tool import MapTool
from src.core.logger import get_logger

logger = get_logger("weather_api")
router = APIRouter(prefix="/api/weather", tags=["天气"])


class WeatherRequest(BaseModel):
    """天气查询请求模型"""
    city: str
    days: Optional[int] = 3


@router.post("/forecast")
async def get_weather_forecast(request: WeatherRequest):
    """
    获取天气预报信息

    Args:
        request: 包含city和days的请求数据
    """
    try:
        city = request.city
        days = request.days or 3

        logger.info(f"查询天气信息: 城市={city}, 天数={days}")

        # 初始化高德地图工具（使用默认API密钥）
        map_tool = MapTool()

        # 首先通过地理编码获取城市的adcode
        geo_result = map_tool.geocode_address(city)

        if not geo_result.get("geocodes"):
            return {
                "status": "error",
                "weather": [],
                "description": f"无法找到城市：{city}"
            }

        # 获取城市的adcode
        adcode = geo_result["geocodes"][0].get("adcode")
        if not adcode:
            return {
                "status": "error",
                "weather": [],
                "description": f"无法获取{city}的城市代码"
            }

        # 使用adcode查询天气信息（获取详细预报）
        weather_result = map_tool.get_weather_info(city=adcode, extensions="all")

        if weather_result.get("status") == "1":
            # 处理天气数据
            weather_data = []

            # 优先使用预报天气数据
            if weather_result.get("forecasts"):
                forecasts = weather_result["forecasts"][0].get("casts", [])
                for i, forecast in enumerate(forecasts[:days]):
                    # 格式化日期
                    date_str = forecast.get("date", "")
                    if date_str:
                        # 转换日期格式，如 "2025-07-07" -> "7月7日"
                        try:
                            from datetime import datetime
                            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                            formatted_date = f"{date_obj.month}月{date_obj.day}日"
                            if i == 0:
                                formatted_date = f"今天({formatted_date})"
                            elif i == 1:
                                formatted_date = f"明天({formatted_date})"
                            else:
                                formatted_date = f"{formatted_date}"
                        except:
                            formatted_date = f"第{i+1}天"
                    else:
                        formatted_date = f"第{i+1}天"

                    weather_data.append({
                        "date": formatted_date,
                        "temperature": f"{forecast.get('nighttemp', '--')}°C~{forecast.get('daytemp', '--')}°C",
                        "weather": f"{forecast.get('dayweather', '')}转{forecast.get('nightweather', '')}".replace("转", " 转 "),
                        "wind": forecast.get("daywind", ""),
                        "humidity": forecast.get("humidity", "")
                    })

            # 如果没有预报数据，使用实时天气
            elif weather_result.get("lives"):
                live_weather = weather_result["lives"][0]
                weather_data.append({
                    "date": "今天",
                    "temperature": f"{live_weather.get('temperature', '--')}°C",
                    "weather": live_weather.get("weather", "未知"),
                    "humidity": live_weather.get("humidity", "--"),
                    "wind": live_weather.get("winddirection", "") + live_weather.get("windpower", "")
                })

            return {
                "status": "success",
                "city": city,
                "weather": weather_data,
                "description": f"已获取{city}的天气信息"
            }
        else:
            return {
                "status": "error",
                "weather": [],
                "description": f"无法获取{city}的天气信息"
            }

    except Exception as e:
        logger.error(f"天气查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"天气查询失败: {str(e)}")


@router.get("/current/{city}")
async def get_current_weather(city: str):
    """
    获取当前天气信息

    Args:
        city: 城市名称
    """
    try:
        logger.info(f"查询当前天气: 城市={city}")

        # 初始化高德地图工具（使用默认API密钥）
        map_tool = MapTool()

        # 首先通过地理编码获取城市的adcode
        geo_result = map_tool.geocode_address(city)

        if not geo_result.get("geocodes"):
            return {
                "status": "error",
                "weather": {},
                "description": f"无法找到城市：{city}"
            }

        # 获取城市的adcode
        adcode = geo_result["geocodes"][0].get("adcode")
        if not adcode:
            return {
                "status": "error",
                "weather": {},
                "description": f"无法获取{city}的城市代码"
            }

        # 使用adcode查询天气信息
        weather_result = map_tool.get_weather_info(city=adcode)

        if weather_result.get("status") == "1" and weather_result.get("lives"):
            live_weather = weather_result["lives"][0]
            return {
                "status": "success",
                "city": city,
                "weather": {
                    "temperature": f"{live_weather.get('temperature', '--')}°C",
                    "weather": live_weather.get("weather", "未知"),
                    "humidity": live_weather.get("humidity", "--"),
                    "wind": live_weather.get("winddirection", "") + live_weather.get("windpower", ""),
                    "reporttime": live_weather.get("reporttime", "")
                },
                "description": f"已获取{city}的当前天气信息"
            }
        else:
            return {
                "status": "error",
                "weather": {},
                "description": f"无法获取{city}的天气信息"
            }

    except Exception as e:
        logger.error(f"当前天气查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"当前天气查询失败: {str(e)}")
