#!/usr/bin/env python3
"""
Python专家Agent集成测试

测试垂域Agent的专业功能，包括代码分析、优化建议和专业咨询。
这是集成测试，测试完整的Agent工作流程。

运行方式：
python tests/integration/test_python_expert_integration.py
"""
import os
import asyncio
import sys
from pathlib import Path

# 确保能正确导入src模块 (从integration目录需要向上两级)
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 设置环境变量
# 只在环境变量未设置时才设置，避免覆盖用户配置
if not os.environ.get("REASONING_LLM_MODEL"):
    # 思考模型：用于Python专家的复杂代码分析和推理
    os.environ["REASONING_LLM_MODEL"] = "glm-z1-flash"
    os.environ["REASONING_LLM_API_KEY"] = "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
    os.environ["REASONING_LLM_BASE_URL"] = "https://open.bigmodel.cn/api/paas/v4/"

if not os.environ.get("BASIC_LLM_MODEL"):
    # 基础模型：用于简单对话和基础功能
    os.environ["BASIC_LLM_MODEL"] = "glm-4-flash"
    os.environ["BASIC_LLM_API_KEY"] = "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
    os.environ["BASIC_LLM_BASE_URL"] = "https://open.bigmodel.cn/api/paas/v4/"

if not os.environ.get("LOG_LEVEL"):
    os.environ["LOG_LEVEL"] = "INFO"
if not os.environ.get("LOG_FORMAT"):
    os.environ["LOG_FORMAT"] = "text"


async def test_code_analysis():
    """测试代码分析功能"""
    print("🔍 测试代码分析功能")
    print("=" * 50)
    
    from src.agents.python_expert import create_python_expert
    
    expert = await create_python_expert("code_analyzer")
    
    # 测试代码
    test_code = '''
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def process_data(data):
    result = []
    for i in range(len(data)):
        if data[i] == True:
            result.append(data[i])
    return result
'''
    
    analysis = await expert.analyze_code(test_code)
    
    print(f"语法有效: {analysis['syntax_valid']}")
    print(f"代码复杂度: {analysis['complexity']}")
    print(f"发现的模式: {analysis['patterns_found']}")
    print(f"问题数量: {len(analysis['issues'])}")
    
    if analysis['issues']:
        print("发现的问题:")
        for issue in analysis['issues']:
            print(f"  - {issue}")
    
    return True


async def test_optimization_suggestions():
    """测试优化建议功能"""
    print("\n⚡ 测试优化建议功能")
    print("=" * 50)
    
    from src.agents.python_expert import create_python_expert
    
    expert = await create_python_expert("optimizer")
    
    # 有问题的代码
    bad_code = '''
def process_items(items):
    result = []
    for i in range(len(items)):
        if items[i] == True:
            result.append(items[i])
    return result

def check_empty(my_list):
    if len(my_list) > 0:
        return True
    return False
'''
    
    suggestions = await expert.suggest_optimization(bad_code)
    
    print("优化建议:")
    for suggestion in suggestions:
        print(f"  - {suggestion}")
    
    return True


async def test_library_recommendations():
    """测试库推荐功能"""
    print("\n📚 测试库推荐功能")
    print("=" * 50)
    
    from src.agents.python_expert import create_python_expert
    
    expert = await create_python_expert("librarian")
    
    tasks = [
        "创建一个Web API服务",
        "处理CSV数据分析",
        "训练机器学习模型",
        "实现异步网络请求"
    ]
    
    for task in tasks:
        print(f"\n任务: {task}")
        recommendations = await expert.recommend_libraries(task)
        
        if recommendations:
            print("推荐的库:")
            for rec in recommendations:
                print(f"  - {rec['name']}: {rec['purpose']}")
        else:
            print("  没有找到相关推荐")
    
    return True


async def test_expert_chat():
    """测试专家对话功能"""
    print("\n💬 测试专家对话功能")
    print("=" * 50)
    
    from src.agents.python_expert import create_python_expert
    
    expert = await create_python_expert("consultant")
    
    # 测试1: 普通Python问题
    print("问题1: 如何实现Python单例模式？")
    result1 = await expert.python_chat("如何实现Python单例模式？")
    print(f"专家回答: {result1['assistant_reply'][:200]}...")
    
    # 测试2: 代码审查
    print("\n问题2: 代码审查")
    inefficient_code = '''
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
'''
    
    result2 = await expert.python_chat("请帮我优化这个斐波那契函数，它太慢了", inefficient_code)
    print(f"专家回答: {result2['assistant_reply'][:300]}...")
    
    if result2['expert_analysis']['code_analysis']:
        print("\n代码分析:")
        analysis = result2['expert_analysis']['code_analysis']
        print(f"  - 语法有效: {analysis['syntax_valid']}")
        print(f"  - 复杂度: {analysis['complexity']}")
        print(f"  - 发现模式: {analysis['patterns_found']}")
    
    return True


async def test_error_explanation():
    """测试错误解释功能"""
    print("\n🐛 测试错误解释功能")
    print("=" * 50)
    
    from src.agents.python_expert import create_python_expert
    
    expert = await create_python_expert("debugger")
    
    errors = [
        "NameError: name 'undefined_var' is not defined",
        "TypeError: unsupported operand type(s) for +: 'int' and 'str'",
        "IndexError: list index out of range",
        "KeyError: 'missing_key'"
    ]
    
    for error in errors:
        explanation = await expert.explain_error(error)
        print(f"\n错误: {error}")
        print(f"类型: {explanation['error_type']}")
        print(f"解释: {explanation['explanation']}")
        print(f"建议: {explanation['suggestion']}")
    
    return True


async def test_performance_tips():
    """测试性能优化建议"""
    print("\n🚀 测试性能优化建议")
    print("=" * 50)
    
    from src.agents.python_expert import create_python_expert
    
    expert = await create_python_expert("performance_expert")
    
    categories = ["loop", "data", "io", "general"]
    
    for category in categories:
        tips = await expert.performance_tips(category)
        print(f"\n{category.upper()}优化建议:")
        for tip in tips:
            print(f"  - {tip}")
    
    return True


async def test_quick_help():
    """测试快速帮助功能"""
    print("\n⚡ 测试快速帮助功能")
    print("=" * 50)
    
    from src.agents.python_expert import quick_python_help
    
    questions = [
        "Python中如何处理异常？",
        "什么是Python装饰器？",
        "如何使用list comprehension？"
    ]
    
    for question in questions:
        print(f"\n问题: {question}")
        answer = await quick_python_help(question)
        print(f"回答: {answer[:150]}...")
    
    return True


async def test_model_usage():
    """测试Python专家使用不同模型的情况"""
    print("\n🔄 测试模型使用情况")
    print("=" * 50)
    
    from src.agents.python_expert import create_python_expert
    
    try:
        # Python专家默认使用思考模型
        expert = await create_python_expert("model_test_expert")
        
        # 测试简单问题（应该使用思考模型）
        simple_question = "Python中如何定义一个函数？"
        print(f"问题: {simple_question}")
        
        result = await expert.python_chat(simple_question)
        print(f"使用模型: {result.get('model_used', '未知')}")
        print(f"回复长度: {len(result['assistant_reply'])} 字符")
        
        if result.get('tokens_used'):
            print(f"Token使用: {result['tokens_used']['total_tokens']}")
        
        # 验证Python专家使用的是思考模型
        if result.get('model_used') == 'glm-z1-flash':
            print("✅ Python专家正确使用思考模型 (glm-z1-flash)")
        else:
            print(f"⚠️ 预期使用思考模型，实际使用: {result.get('model_used')}")
        
        print("\n✅ 模型使用测试完成！")
        
    except Exception as e:
        print(f"❌ 模型使用测试失败: {e}")
        return False
    
    return True


async def main():
    """主测试函数"""
    print("🐍 Python专家Agent 垂域功能测试")
    print("=" * 60)
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    try:
        # 运行各项测试
        tests = [
            ("代码分析", test_code_analysis),
            ("优化建议", test_optimization_suggestions),
            ("库推荐", test_library_recommendations),
            ("专家对话", test_expert_chat),
            ("错误解释", test_error_explanation),
            ("性能建议", test_performance_tips),
            ("快速帮助", test_quick_help),
            ("模型使用", test_model_usage)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                success = await test_func()
                results[test_name] = "✅ 成功" if success else "❌ 失败"
            except Exception as e:
                print(f"❌ {test_name}测试失败: {e}")
                results[test_name] = f"❌ 失败: {e}"
        
        # 输出测试总结
        print(f"\n{'='*60}")
        print("🎯 Python专家Agent测试总结")
        print(f"{'='*60}")
        
        for test_name, result in results.items():
            print(f"{test_name}: {result}")
        
        success_count = len([r for r in results.values() if "✅" in r])
        total_count = len(results)
        
        if success_count == total_count:
            print(f"\n🌟 所有测试通过！({success_count}/{total_count})")
            print("Python专家Agent已经准备就绪，可以为用户提供专业的Python编程服务！")
        else:
            print(f"\n⚠️  部分测试失败 ({success_count}/{total_count})")
            print("需要进一步调试失败的功能。")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n{'='*60}")


if __name__ == "__main__":
    asyncio.run(main()) 