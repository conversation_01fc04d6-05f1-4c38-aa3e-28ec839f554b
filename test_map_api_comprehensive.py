"""
地图API综合测试

验证地图API的所有端点功能，包括POI搜索、地理编码、POI详情等。
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_map_api_health():
    """测试地图API健康检查"""
    print("=" * 60)
    print("测试地图API健康检查")
    print("=" * 60)
    
    url = "http://localhost:8000/api/map/health"
    
    try:
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 健康检查成功")
            print(f"状态: {result.get('status')}")
            print(f"服务: {result.get('service')}")
            print(f"描述: {result.get('description')}")
            return True
        else:
            print(f"❌ 健康检查失败")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"🔌 连接错误 - 请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def test_poi_search_api():
    """测试POI搜索API"""
    print("\n" + "=" * 60)
    print("测试POI搜索API")
    print("=" * 60)
    
    url = "http://localhost:8000/api/map/search_pois"
    
    # 测试数据
    test_cases = [
        {"keywords": "湄洲岛", "city": "莆田", "page_size": 5},
        {"keywords": "妈祖庙", "city": "莆田", "page_size": 3},
        {"keywords": "南少林", "city": "莆田", "page_size": 3},
        {"keywords": "莆田博物馆", "city": "莆田", "page_size": 2},
    ]
    
    for i, test_data in enumerate(test_cases):
        print(f"\n{i+1}. 测试搜索: {test_data['keywords']}")
        
        try:
            response = requests.post(url, json=test_data, timeout=30)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 搜索成功")
                print(f"   状态: {result.get('status')}")
                print(f"   POI数量: {result.get('total')}")
                print(f"   描述: {result.get('description')}")
                
                pois = result.get('pois', [])
                for j, poi in enumerate(pois[:2]):  # 只显示前2个
                    print(f"     {j+1}. {poi.get('name')}")
                    print(f"        地址: {poi.get('address')}")
                    print(f"        类型: {poi.get('type')}")
                    print(f"        评分: {poi.get('rating')}")
                    print(f"        图片: {len(poi.get('photos', []))}张")
                    if poi.get('image'):
                        print(f"        主图: {poi.get('image')[:50]}...")
                    
            else:
                print(f"   ❌ 搜索失败")
                print(f"   错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时")
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")


def test_geocode_api():
    """测试地理编码API"""
    print("\n" + "=" * 60)
    print("测试地理编码API")
    print("=" * 60)
    
    url = "http://localhost:8000/api/map/geocode"
    
    # 测试地址
    test_addresses = ["莆田", "湄洲岛", "福建省莆田市", "莆田市博物馆"]
    
    for i, address in enumerate(test_addresses):
        print(f"\n{i+1}. 测试地址: {address}")
        
        try:
            response = requests.post(url, json={"address": address}, timeout=30)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 地理编码成功")
                print(f"   状态: {result.get('status')}")
                print(f"   描述: {result.get('description')}")
                
                geocode = result.get('geocode', {})
                if geocode:
                    print(f"   格式化地址: {geocode.get('formatted_address')}")
                    print(f"   省份: {geocode.get('province')}")
                    print(f"   城市: {geocode.get('city')}")
                    print(f"   区县: {geocode.get('district')}")
                    print(f"   行政代码: {geocode.get('adcode')}")
                    print(f"   坐标: {geocode.get('location')}")
                    
            else:
                print(f"   ❌ 地理编码失败")
                print(f"   错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时")
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")


def test_poi_detail_api():
    """测试POI详情API"""
    print("\n" + "=" * 60)
    print("测试POI详情API")
    print("=" * 60)
    
    # 先搜索一个POI获取ID
    search_url = "http://localhost:8000/api/map/search_pois"
    search_data = {"keywords": "湄洲岛", "city": "莆田", "page_size": 1}
    
    try:
        search_response = requests.post(search_url, json=search_data, timeout=30)
        
        if search_response.status_code == 200:
            search_result = search_response.json()
            pois = search_result.get('pois', [])
            
            if pois:
                poi_id = pois[0].get('id')
                print(f"获取到POI ID: {poi_id}")
                
                # 测试POI详情
                detail_url = f"http://localhost:8000/api/map/poi_detail/{poi_id}"
                
                detail_response = requests.get(detail_url, timeout=30)
                
                print(f"状态码: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    result = detail_response.json()
                    print(f"✅ POI详情获取成功")
                    print(f"状态: {result.get('status')}")
                    print(f"描述: {result.get('description')}")
                    
                    poi = result.get('poi', {})
                    if poi:
                        print(f"名称: {poi.get('name')}")
                        print(f"地址: {poi.get('address')}")
                        print(f"类型: {poi.get('type')}")
                        print(f"电话: {poi.get('tel')}")
                        print(f"评分: {poi.get('rating')}")
                        print(f"费用: {poi.get('cost')}")
                        print(f"推荐: {poi.get('recommend')}")
                        print(f"图片数量: {len(poi.get('photos', []))}")
                        
                else:
                    print(f"❌ POI详情获取失败")
                    print(f"错误信息: {detail_response.text}")
            else:
                print("❌ 没有找到POI，无法测试详情API")
        else:
            print("❌ POI搜索失败，无法测试详情API")
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")


def main():
    """主测试函数"""
    print("AutoPilot AI - 地图API综合测试")
    print("=" * 60)
    print("本测试将验证:")
    print("1. 地图API健康检查 (/api/map/health)")
    print("2. POI搜索API (/api/map/search_pois)")
    print("3. 地理编码API (/api/map/geocode)")
    print("4. POI详情API (/api/map/poi_detail/{poi_id})")
    print("=" * 60)
    
    try:
        # 测试健康检查
        health_ok = test_map_api_health()
        
        if health_ok:
            # 测试POI搜索
            test_poi_search_api()
            
            # 测试地理编码
            test_geocode_api()
            
            # 测试POI详情
            test_poi_detail_api()
        else:
            print("\n❌ 健康检查失败，跳过其他测试")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        print("✅ 地图API功能测试完成！")
        print("💡 如果所有测试都通过，说明地图API正常工作。")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    main()
