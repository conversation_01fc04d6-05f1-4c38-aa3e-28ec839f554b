#!/usr/bin/env python3
"""
旅行规划系统V3重构版快速验证脚本

快速验证重构后的核心功能是否正常工作：
1. 工具注册表状态
2. 两阶段工作流程节点
3. V3 API端点
4. 前端页面可访问性
"""

import asyncio
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def verify_tool_registry():
    """验证工具注册表"""
    logger.info("🔧 验证工具注册表...")

    try:
        # 导入工具注册表并触发工具注册
        from src.tools.unified_registry import unified_registry

        # 导入工具模块以触发注册
        import src.tools.travel_planner.consolidated_tools
        import src.tools.travel_planner.icp_tools
        from src.agents.services.amap_service import AmapService
        
        # 检查Planner Tools
        planner_tools = unified_registry.get_all_planner_tools()
        expected_planner_tools = [
            "format_framework_analysis_prompt",
            "format_preference_analysis_prompt", 
            "create_consolidated_intent",
            "prepare_icp_context",
            "extract_key_insights",
            "generate_planning_thought",
            "select_next_action",
            "observe_action_result",
            "update_planning_state",
            "check_planning_completion"
        ]
        
        missing_planner_tools = []
        for tool in expected_planner_tools:
            if tool not in planner_tools:
                missing_planner_tools.append(tool)
        
        if missing_planner_tools:
            logger.error(f"❌ 缺少Planner Tools: {missing_planner_tools}")
            return False
        else:
            logger.info(f"✅ Planner Tools注册完整 ({len(planner_tools)}个)")
        
        # 检查Action Tools
        action_tools = unified_registry.get_action_tool_names()
        expected_action_tools = ["search_poi", "geocode", "get_driving_route"]
        
        missing_action_tools = []
        for tool in expected_action_tools:
            if tool not in action_tools:
                missing_action_tools.append(tool)
        
        if missing_action_tools:
            logger.warning(f"⚠️ 缺少Action Tools: {missing_action_tools} (可能需要启动服务)")
        else:
            logger.info(f"✅ Action Tools注册完整 ({len(action_tools)}个)")
        
        # 获取工具信息
        tool_info = unified_registry.get_tool_info()
        logger.info(f"📊 工具统计: Action Tools={tool_info['action_tools_count']}, Planner Tools={tool_info['planner_tools_count']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 工具注册表验证失败: {str(e)}")
        return False


async def verify_workflow_nodes():
    """验证工作流程节点"""
    logger.info("🔄 验证工作流程节点...")
    
    try:
        # 导入节点函数
        from src.agents.travel_planner_lg.nodes import (
            run_framework_analysis,
            run_preference_analysis,
            prepare_planning_context,
            planner_agent_node,
            tool_executor_node,
            route_icp_action
        )
        
        # 检查节点函数是否可调用
        nodes = {
            "run_framework_analysis": run_framework_analysis,
            "run_preference_analysis": run_preference_analysis,
            "prepare_planning_context": prepare_planning_context,
            "planner_agent_node": planner_agent_node,
            "tool_executor_node": tool_executor_node,
            "route_icp_action": route_icp_action
        }
        
        for node_name, node_func in nodes.items():
            if not callable(node_func):
                logger.error(f"❌ 节点 {node_name} 不可调用")
                return False
        
        logger.info(f"✅ 所有工作流程节点验证通过 ({len(nodes)}个)")
        return True
        
    except Exception as e:
        logger.error(f"❌ 工作流程节点验证失败: {str(e)}")
        return False


async def verify_graph_structure():
    """验证图形结构"""
    logger.info("📊 验证图形结构...")
    
    try:
        # 导入图形类
        from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
        
        # 创建图形实例
        graph = TravelPlannerGraphV3()
        compiled_graph = graph.compile()
        
        if compiled_graph is None:
            logger.error("❌ 图形编译失败")
            return False
        
        logger.info("✅ 图形结构验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 图形结构验证失败: {str(e)}")
        return False


async def verify_api_structure():
    """验证API结构"""
    logger.info("🌐 验证API结构...")
    
    try:
        # 导入API路由
        from src.api.v3.travel_planner import router, PlanRequest
        
        # 检查路由配置
        if router.prefix != "/api/v3/travel-planner":
            logger.error(f"❌ API路由前缀错误: {router.prefix}")
            return False
        
        # 检查请求模型
        test_request = PlanRequest(
            user_query="测试查询",
            user_id="test_user",
            execution_mode="automatic"
        )
        
        if test_request.user_query != "测试查询":
            logger.error("❌ 请求模型验证失败")
            return False
        
        logger.info("✅ API结构验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ API结构验证失败: {str(e)}")
        return False


def verify_frontend_files():
    """验证前端文件"""
    logger.info("🎨 验证前端文件...")
    
    try:
        # 检查关键前端文件
        frontend_files = [
            "static/travel_planner_v3_refactored.html",
            "static/js/app-v3-refactored.js",
            "static/css/travel_planner_v3.css"
        ]
        
        missing_files = []
        for file_path in frontend_files:
            full_path = project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.warning(f"⚠️ 缺少前端文件: {missing_files}")
        else:
            logger.info("✅ 前端文件验证通过")
        
        return len(missing_files) == 0
        
    except Exception as e:
        logger.error(f"❌ 前端文件验证失败: {str(e)}")
        return False


def verify_test_files():
    """验证测试文件"""
    logger.info("🧪 验证测试文件...")
    
    try:
        # 检查测试文件
        test_files = [
            "tests/playwright/test_travel_planner_v3_refactored.py",
            "tests/scripts/run_v3_refactored_tests.py",
            "tests/unit/test_v3_refactored_components.py"
        ]
        
        missing_files = []
        for file_path in test_files:
            full_path = project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.warning(f"⚠️ 缺少测试文件: {missing_files}")
        else:
            logger.info("✅ 测试文件验证通过")
        
        return len(missing_files) == 0
        
    except Exception as e:
        logger.error(f"❌ 测试文件验证失败: {str(e)}")
        return False


async def main():
    """主验证函数"""
    logger.info("🚀 开始V3重构版快速验证")
    
    verification_results = []
    
    # 执行各项验证
    verification_results.append(await verify_tool_registry())
    verification_results.append(await verify_workflow_nodes())
    verification_results.append(await verify_graph_structure())
    verification_results.append(await verify_api_structure())
    verification_results.append(verify_frontend_files())
    verification_results.append(verify_test_files())
    
    # 统计结果
    passed = sum(verification_results)
    total = len(verification_results)
    
    logger.info(f"📊 验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        logger.info("🎉 所有验证项目通过！V3重构版准备就绪")
        print("\n" + "="*50)
        print("✅ V3重构版验证完成")
        print("📋 验证项目:")
        print("  ✅ 工具注册表")
        print("  ✅ 工作流程节点")
        print("  ✅ 图形结构")
        print("  ✅ API结构")
        print("  ✅ 前端文件")
        print("  ✅ 测试文件")
        print("\n🚀 可以启动服务进行完整测试:")
        print("  uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload")
        print("="*50)
        return True
    else:
        failed = total - passed
        logger.error(f"❌ {failed} 项验证失败，请检查相关组件")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
