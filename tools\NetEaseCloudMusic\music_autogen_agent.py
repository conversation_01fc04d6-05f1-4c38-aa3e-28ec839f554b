"""
NetEase Cloud Music Agent using AutoGen v0.4+ best practices.
- 所有 music_tool.py 中公开方法均封装为 FunctionTool
- 提供 AssistantAgent、UserProxyAgent 创建方法
- 提供测试函数 test_music_agent
"""
from typing import List, Any, Tuple
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_core.tools import FunctionTool
from .music_tool import MusicTool
import asyncio
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelFamily

def get_music_tools() -> List[FunctionTool]:
    """
    封装 music_tool.py 中所有公开方法为 FunctionTool。
    Returns:
        List[FunctionTool]
    """
    music_tool = MusicTool()
    # 只封装非下划线开头的可调用方法
    method_names = [
        name for name in dir(music_tool)
        if not name.startswith('_') and callable(getattr(music_tool, name))
    ]
    tools = []
    for name in method_names:
        func = getattr(music_tool, name)
        tool = FunctionTool(func, description=func.__doc__ or name)
        tools.append(tool)
    return tools


def create_music_agents(
    model_client: Any,
    system_message: str = None,
    user_proxy_name: str = "user_proxy",
    assistant_name: str = "music_assistant",
) -> Tuple[UserProxyAgent, AssistantAgent]:
    """
    创建音乐助手和用户代理。
    Args:
        model_client: 语言模型客户端（如 OpenAIChatCompletionClient 实例）
        system_message: 智能体的 system message
        user_proxy_name: 用户代理名称
        assistant_name: 智能体名称
    Returns:
        (user_proxy, assistant)
    """
    if system_message is None:
        system_message = (
            """            
操作步骤
你是一个专业的音乐助手，必须严格按照以下步骤执行：
提取关键词：从用户的描述中提取与歌曲、艺人、风格、专辑、歌单或歌曲详细信息相关的关键词。
判断用户需求：根据用户描述，明确用户是想听歌、找歌单、找专辑、找艺人，还是想了解某首歌的详细信息。
工具调用要求：
每次对话必须至少调用两次工具。
第一次调用：必须调用搜索相关工具（如search_songs、search_artists等）。
第二次调用：根据第一次搜索结果，调用第二个工具获取更多信息（如get_song_detail、get_batch_song_urls等）。
如果第一次搜索结果不理想，尝试使用不同关键词或不同工具重新搜索。
返回结果：优先提供音乐播放地址。
工具选择：
用户想听某首歌：调用search_songs_by_artist_and_name或search_songs。
用户想听某个艺人的歌：调用search_songs_by_artist。
用户想听某个风格的歌：调用search_songs或get_suggest_keywords。
用户想找歌单：调用search_playlists。
用户想找专辑：调用search_albums或search_albums_by_artist。
用户想了解艺人：调用search_artists。
用户想了解某首歌的详细信息：调用get_song_detail。
可选操作：
如果第一次搜索结果为空，尝试精简关键词，再次调用相关工具（同一个工具不超过2次）。
如果工具执行完毕仍未找到合适信息，调用tavily_search进行网络搜索。
必选操作：最后调用get_batch_song_urls获取歌曲播放地址。
结果呈现：将歌曲播放地址返回给用户。
请确保你的回答简洁明了，专注于用户的音乐需求。
            """
        )    
    tools = get_music_tools()
    print(f"创建音乐助手 {assistant_name}，包含工具: {[tool.name for tool in tools]}")      
    assistant = AssistantAgent(
        name=assistant_name,
        model_client=model_client,
        tools=tools,
        system_message=system_message,
    )
    user_proxy = UserProxyAgent(
        name=user_proxy_name,
        description="A proxy for the human user to interact with the music assistant."
    )
    return user_proxy, assistant


async def test_music_agent(prompt: str, n_rounds: int = 3):
    """
    测试音乐智能体，输入一句自然语言，输出 agent 回复。
    Args:
        prompt: 自然语言输入
        model_client: 语言模型客户端
    """
    model_client = OpenAIChatCompletionClient(
        # model="Qwen2.5-32B-Instruct-AWQ",
        # base_url="http://************:8007/v1",
        model="Qwen3-30B-A3B",
        base_url="http://************:8013/v1",
            #    'chat_template_kwargs':{"enable_thinking": False},
        api_key="EMPTY",          # vllm 默认不校验，可以随便填
        model_info={
            "family": "any", #ModelFamily.QWEN,  # 指定模型家族为 Qwen
            "function_calling": True,    # 如果你需要 function calling
            "json_output": True,         # 如果你需要 json 输出
            "structured_output": True,   # 如果你需要结构化输出
            "vision": False,      
            # 其他参数可选
        },
        # model_capabilities 可选，通常不用加
    )    
    user_proxy, assistant = create_music_agents(model_client)
    print(f"[测试] 用户输入: {prompt}")
    
    rounds = []
    user_message = prompt
    for i in range(n_rounds):
        # 用户发起任务，assistant 回复
        result = await assistant.run(task=user_message)
        # 获取 assistant 的回复
        assistant_reply = None
        for msg in result.messages:
            if getattr(msg, 'source', '') == assistant.name:
                content = getattr(msg, 'content', str(msg))
                assistant_reply = content
        rounds.append({
            "round": i + 1,
            "role": "user",
            "content": user_message,
        })
        rounds.append({
            "round": i + 1,
            "role": "assistant",
            "content": assistant_reply or "No assistant reply.",
        })
        # 下一轮用户输入可以根据 assistant 回复自动生成，或继续用初始 prompt
        user_message = "继续调用合适的工具，或者总结回复内容"  # 这里简单处理，如需更智能可自定义
    return rounds

    # # 获取最后一个 assistant 消息的内容
    # final_message = None
    # for msg in result.messages:
    #     if getattr(msg, 'source', '') == 'music_assistant':
    #         content = getattr(msg, 'content', str(msg))
    #         # 处理 FunctionExecutionResult 类型
    #         if hasattr(content, 'return_value'):
    #             content = str(content.return_value)
    #         elif hasattr(content, 'args'):
    #             content = f"Function call: {content.name} with args: {content.args}"
    #         else:
    #             content = str(content)
    #         final_message = content
    
    # return {
    #     "input": prompt,
    #     "content": final_message or "No assistant message found."
    # }


async def test_music_agent_multiround(prompt: str, n_rounds: int = 3):
    """
    用 user_proxy 自动模拟用户和助手的多轮对话。
    Args:
        prompt: 用户初始输入
        n_rounds: 对话轮数
    Returns:
        List[dict]: 每轮对话内容
    """
    model_client = OpenAIChatCompletionClient(
        model="Qwen3-30B-A3B",
        base_url="http://************:8013/v1",
        api_key="EMPTY",
        model_info={
            "family": "any",
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "vision": False,
        },
    )
    user_proxy, assistant = create_music_agents(model_client)
    print(f"[多轮测试] 用户输入: {prompt}")
    # initiate_chat 支持多轮自动对话
    chat_result = await user_proxy.initiate_chat(
        assistant,
        message=prompt,
        n_round=n_rounds,
        summary_method=None,  # 不自动总结
        # enable_clear_history=True,  # 可选，是否每轮清空历史
    )
    # 整理每轮对话内容
    rounds = []
    for i, msg in enumerate(chat_result.chat_history):
        rounds.append({
            "round": i + 1,
            "role": getattr(msg, 'role', getattr(msg, 'source', '')),
            "content": getattr(msg, 'content', str(msg)),
        })
    return rounds