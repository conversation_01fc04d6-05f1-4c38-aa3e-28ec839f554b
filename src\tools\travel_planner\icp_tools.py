"""
ICP (迭代式上下文规划) 工具集 (V3.0 - 统一架构版)

实现"思考-行动-观察"循环所需的Planner Tools，
支持AI驱动的迭代式旅行规划。
"""

import json
import logging
import math
from typing import Dict, Any, List, Optional
from datetime import datetime
from src.tools.unified_registry import unified_registry
from src.models.poi import EnrichedPOI, BasicPOI, POIType, convert_basic_to_enriched

logger = logging.getLogger(__name__)


def _minutes_to_time(minutes: int) -> str:
    """将分钟数转换为时间字符串 (HH:MM)"""
    hours = minutes // 60
    mins = minutes % 60
    return f"{hours:02d}:{mins:02d}"


def _time_to_minutes(time_str: str) -> int:
    """将时间字符串转换为分钟数"""
    try:
        hours, mins = map(int, time_str.split(':'))
        return hours * 60 + mins
    except:
        return 0


@unified_registry.register_planner_tool
def generate_planning_thought(
    current_state: Dict[str, Any], 
    planning_context: Dict[str, Any],
    step_number: int
) -> Dict[str, Any]:
    """
    生成规划思考
    
    基于当前状态和规划上下文，生成下一步的思考内容
    
    Args:
        current_state: 当前规划状态
        planning_context: 规划上下文
        step_number: 当前步骤编号
    
    Returns:
        思考结果
    """
    try:
        # 分析当前进度
        daily_plans = current_state.get("daily_plans", {})
        completed_days = len([day for day, plans in daily_plans.items() if plans])
        total_days = planning_context.get("constraints", {}).get("max_days", 3)
        
        # 分析预算使用情况
        budget_used = current_state.get("total_budget_tracker", 0)
        budget_limit = planning_context.get("constraints", {}).get("budget_limit", 1000)
        
        # 生成思考内容
        if completed_days == 0:
            thought = f"开始第{step_number}步规划。需要为{total_days}天行程制定计划。首先分析目的地和用户偏好，确定第一天的核心景点。"
        elif completed_days < total_days:
            current_day = completed_days + 1
            thought = f"第{step_number}步：继续规划第{current_day}天行程。已完成{completed_days}天规划，预算已用{budget_used}元。需要考虑与前几天的连贯性和交通便利性。"
        else:
            thought = f"第{step_number}步：所有天数规划已完成，进行最终检查和优化。总预算使用{budget_used}元，需要验证行程的合理性和完整性。"
        
        # 确定下一步行动
        if completed_days < total_days:
            next_action_type = "search_poi"
            action_reason = f"需要为第{completed_days + 1}天搜索合适的景点"
        elif not current_state.get("accommodation_planned", False):
            next_action_type = "search_accommodation"
            action_reason = "需要安排住宿"
        else:
            next_action_type = "finalize_itinerary"
            action_reason = "完成最终行程整理"
        
        return {
            "thought_content": thought,
            "reasoning_step": step_number,
            "progress_analysis": {
                "completed_days": completed_days,
                "total_days": total_days,
                "budget_used": budget_used,
                "budget_remaining": budget_limit - budget_used
            },
            "next_action_suggestion": {
                "action_type": next_action_type,
                "reason": action_reason
            },
            "confidence": 0.8
        }
        
    except Exception as e:
        logger.error(f"Failed to generate planning thought: {str(e)}")
        return {
            "thought_content": f"规划思考生成失败: {str(e)}",
            "reasoning_step": step_number,
            "confidence": 0.0,
            "error": str(e)
        }


@unified_registry.register_planner_tool
async def create_daily_schedule(
    day_number: int,
    activities: List[Dict[str, Any]],
    constraints: Dict[str, Any] = None,
    current_state: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    创建合理的每日时间安排

    确保时空一致性和合理性，包括：
    - 合理的时间分配
    - 中午安排午餐
    - 晚上安排晚餐
    - 景点间的交通时间
    - 休息时间

    Args:
        day_number: 天数
        activities: 活动列表
        constraints: 约束条件

    Returns:
        带时间安排的每日行程
    """
    try:
        constraints = constraints or {}
        start_time = constraints.get("start_time", "09:00")  # 默认9点开始
        end_time = constraints.get("end_time", "21:00")      # 默认9点结束
        lunch_time = constraints.get("lunch_time", "12:00")  # 午餐时间
        dinner_time = constraints.get("dinner_time", "18:00") # 晚餐时间

        # 转换为分钟
        current_time = _time_to_minutes(start_time)
        end_time_minutes = _time_to_minutes(end_time)
        lunch_time_minutes = _time_to_minutes(lunch_time)
        dinner_time_minutes = _time_to_minutes(dinner_time)

        scheduled_activities = []

        # 分类活动
        attractions = [act for act in activities if act.get("type") not in ["dining", "meal", "restaurant"]]
        restaurants = [act for act in activities if act.get("type") in ["dining", "meal", "restaurant"]]

        # 景点去重：移除已在其他天安排的景点
        if current_state:
            used_attractions = _get_used_attractions(current_state, day_number)
            attractions = [act for act in attractions if act.get("name") not in used_attractions]
            logger.info(f"去重后剩余{len(attractions)}个景点")

        # 按地理位置优化景点顺序，提高路线合理性
        if len(attractions) > 1:
            attractions = await _optimize_attractions_by_location(attractions)
            logger.info(f"已按地理位置优化景点顺序")

        # 如果没有餐厅信息，尝试搜索餐厅
        if not restaurants:
            city = _extract_city_from_activities(activities)
            logger.info(f"提取到的城市: {city}")
            if city:
                logger.info(f"未找到餐厅信息，尝试搜索{city}的餐厅")
                try:
                    restaurants = await _search_restaurants_for_city(city)
                    logger.info(f"搜索到{len(restaurants)}个餐厅")
                    for i, restaurant in enumerate(restaurants):
                        logger.info(f"餐厅{i+1}: {restaurant.get('name', '未知')}")
                except Exception as e:
                    logger.error(f"搜索餐厅失败: {str(e)}")
                    restaurants = []
            else:
                # 从consolidated_intent获取目的地信息
                target_city = "北京"  # 默认城市
                if current_state:
                    consolidated_intent = current_state.get("consolidated_intent", {})
                    destinations = consolidated_intent.get("destinations", ["北京"])
                    target_city = destinations[0] if destinations and destinations[0] else "北京"

                logger.warning(f"无法从活动中提取城市信息，使用目的地城市: {target_city}")
                restaurants = _get_default_restaurants(target_city)

        # 安排上午活动
        morning_activities = []
        while current_time < lunch_time_minutes and attractions:
            activity = attractions.pop(0)
            duration = _get_activity_duration(activity)

            # 检查是否有足够时间
            if current_time + duration > lunch_time_minutes:
                break

            scheduled_activity = {
                **activity,
                "start_time": _minutes_to_time(current_time),
                "end_time": _minutes_to_time(current_time + duration),
                "duration": _format_duration(duration)
            }

            # 计算与前一个活动的距离
            if morning_activities:
                prev_activity = morning_activities[-1]
                distance_info = await _calculate_distance_between_activities(prev_activity, scheduled_activity)
                if distance_info:
                    scheduled_activity["distance_from_previous"] = distance_info
            morning_activities.append(scheduled_activity)
            current_time += duration + 30  # 加30分钟交通/休息时间

        # 安排午餐
        lunch_restaurant = None
        if restaurants:
            lunch_restaurant = restaurants.pop(0)
        else:
            lunch_restaurant = {
                "name": "午餐时间",
                "type": "dining",
                "description": "午餐休息",
                "cuisine_type": "中餐"
            }

        lunch_activity = {
            **lunch_restaurant,
            "start_time": _minutes_to_time(lunch_time_minutes),
            "end_time": _minutes_to_time(lunch_time_minutes + 60),
            "duration": "1小时",
            "meal_type": "lunch"
        }

        # 计算午餐与上一个活动的距离
        if morning_activities:
            distance_info = await _calculate_distance_between_activities(morning_activities[-1], lunch_activity)
            if distance_info:
                lunch_activity["distance_from_previous"] = distance_info

        # 安排下午活动
        current_time = lunch_time_minutes + 60  # 午餐后
        afternoon_activities = []

        while current_time < dinner_time_minutes and attractions:
            activity = attractions.pop(0)
            duration = _get_activity_duration(activity)

            # 检查是否有足够时间
            if current_time + duration > dinner_time_minutes:
                break

            scheduled_activity = {
                **activity,
                "start_time": _minutes_to_time(current_time),
                "end_time": _minutes_to_time(current_time + duration),
                "duration": _format_duration(duration)
            }

            # 计算与前一个活动的距离
            prev_activity = lunch_activity if not afternoon_activities else afternoon_activities[-1]
            distance_info = await _calculate_distance_between_activities(prev_activity, scheduled_activity)
            if distance_info:
                scheduled_activity["distance_from_previous"] = distance_info

            afternoon_activities.append(scheduled_activity)
            current_time += duration + 30  # 加30分钟交通/休息时间

        # 安排晚餐
        dinner_restaurant = None
        if restaurants:
            dinner_restaurant = restaurants.pop(0)
        else:
            dinner_restaurant = {
                "name": "晚餐时间",
                "type": "dining",
                "description": "晚餐时间",
                "cuisine_type": "中餐"
            }

        dinner_activity = {
            **dinner_restaurant,
            "start_time": _minutes_to_time(dinner_time_minutes),
            "end_time": _minutes_to_time(dinner_time_minutes + 90),
            "duration": "1.5小时",
            "meal_type": "dinner"
        }

        # 安排晚上活动（如果有时间）
        current_time = dinner_time_minutes + 90  # 晚餐后
        evening_activities = []

        while current_time < end_time_minutes and attractions:
            activity = attractions.pop(0)
            duration = _get_activity_duration(activity)

            # 检查是否有足够时间
            if current_time + duration > end_time_minutes:
                break

            scheduled_activity = {
                **activity,
                "start_time": _minutes_to_time(current_time),
                "end_time": _minutes_to_time(current_time + duration),
                "duration": _format_duration(duration)
            }
            evening_activities.append(scheduled_activity)
            current_time += duration + 30

        # 合并所有活动
        all_activities = morning_activities + [lunch_activity] + afternoon_activities + [dinner_activity] + evening_activities

        return {
            "day": day_number,
            "activities": all_activities,
            "total_activities": len(all_activities),
            "start_time": start_time,
            "end_time": _minutes_to_time(current_time),
            "unscheduled_activities": attractions,  # 未安排的活动
            "schedule_quality": _calculate_schedule_quality(all_activities)
        }

    except Exception as e:
        logger.error(f"Failed to create daily schedule: {str(e)}")
        return {
            "day": day_number,
            "activities": activities,
            "error": str(e),
            "schedule_quality": 0.0
        }


def _get_activity_duration(activity: Dict[str, Any]) -> int:
    """获取活动所需时间（分钟）"""
    activity_type = activity.get("type", "attraction")

    if activity_type in ["dining", "meal", "restaurant"]:
        return 60  # 餐饮1小时
    elif activity_type in ["museum", "博物馆"]:
        return 150  # 博物馆2.5小时
    elif activity_type in ["park", "公园"]:
        return 90   # 公园1.5小时
    elif activity_type in ["temple", "寺庙", "church", "教堂"]:
        return 60   # 宗教场所1小时
    elif activity_type in ["shopping", "购物"]:
        return 120  # 购物2小时
    else:
        return 120  # 默认2小时


def _format_duration(minutes: int) -> str:
    """格式化时长"""
    if minutes >= 60:
        hours = minutes // 60
        mins = minutes % 60
        if mins == 0:
            return f"{hours}小时"
        else:
            return f"{hours}小时{mins}分钟"
    else:
        return f"{minutes}分钟"


def _calculate_schedule_quality(activities: List[Dict[str, Any]]) -> float:
    """计算行程质量分数"""
    if not activities:
        return 0.0

    quality_score = 0.8  # 基础分数

    # 检查是否有午餐
    has_lunch = any(act.get("meal_type") == "lunch" for act in activities)
    if has_lunch:
        quality_score += 0.1

    # 检查是否有晚餐
    has_dinner = any(act.get("meal_type") == "dinner" for act in activities)
    if has_dinner:
        quality_score += 0.1

    # 检查活动数量是否合理（3-6个活动）
    activity_count = len(activities)
    if 3 <= activity_count <= 6:
        quality_score += 0.1
    elif activity_count > 6:
        quality_score -= 0.1

    return min(1.0, quality_score)


@unified_registry.register_planner_tool
def select_next_action(
    thought_result: Dict[str, Any],
    available_tools: List[str],
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    选择下一步行动
    
    基于思考结果和可用工具，选择最合适的下一步行动
    
    Args:
        thought_result: 思考结果
        available_tools: 可用工具列表
        current_state: 当前状态
    
    Returns:
        行动选择结果
    """
    try:
        suggested_action = thought_result.get("next_action_suggestion", {})
        action_type = suggested_action.get("action_type", "search_poi")
        
        # 根据行动类型选择具体工具和参数
        if action_type == "search_poi":
            # 确定搜索参数
            daily_plans = current_state.get("daily_plans", {})
            current_day = len([day for day, plans in daily_plans.items() if plans]) + 1

            # 从consolidated_intent获取目的地信息
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            current_destination = destinations[0] if destinations else "北京"

            # 从偏好分析获取景点类型
            preferences = consolidated_intent.get("preferences", {})
            attraction_prefs = preferences.get("attractions", {})
            preferred_types = attraction_prefs.get("preferred_types", ["历史文化"])

            # 为不同天数选择不同的搜索关键词，增加多样性
            search_keywords = _get_diverse_search_keywords(
                current_day,
                preferred_types,
                current_destination,
                daily_plans
            )

            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": search_keywords,
                    "city": current_destination,
                    "types": "景点",
                    "page_size": 10
                },
                "target_day": current_day,
                "expected_result": f"为第{current_day}天获取{search_keywords}列表"
            }
            
        elif action_type == "search_accommodation":
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "酒店",
                    "city": destinations[0] if destinations else "北京",
                    "types": "住宿",
                    "page_size": 5
                },
                "expected_result": "获取住宿选项列表"
            }
            
        elif action_type == "finalize_itinerary":
            action = {
                "tool_name": "format_final_itinerary",
                "parameters": {
                    "daily_plans": current_state.get("daily_plans", {}),
                    "consolidated_intent": current_state.get("consolidated_intent", {})
                },
                "expected_result": "生成最终完整行程"
            }
            
        else:
            # 默认行动
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "景点",
                    "city": "北京",
                    "page_size": 5
                },
                "expected_result": "获取景点信息"
            }
        
        return {
            "selected_action": action,
            "action_reasoning": suggested_action.get("reason", "基于当前状态选择的行动"),
            "confidence": thought_result.get("confidence", 0.8),
            "alternatives": []  # 可以添加备选行动
        }
        
    except Exception as e:
        logger.error(f"Failed to select next action: {str(e)}")
        return {
            "selected_action": {
                "tool_name": "search_poi",
                "parameters": {"keywords": "景点", "city": "北京"},
                "expected_result": "获取基础景点信息"
            },
            "action_reasoning": f"行动选择失败，使用默认行动: {str(e)}",
            "confidence": 0.1,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def observe_action_result(
    action: Dict[str, Any],
    action_result: Any,
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    观察行动结果
    
    分析工具执行结果，评估是否达到预期目标
    
    Args:
        action: 执行的行动
        action_result: 行动执行结果
        current_state: 当前状态
    
    Returns:
        观察结果
    """
    try:
        tool_name = action.get("tool_name", "unknown")
        expected_result = action.get("expected_result", "")
        
        # 分析结果质量
        if action_result is None:
            observation = {
                "success": False,
                "quality_score": 0.0,
                "observation": "工具执行失败，未获得结果",
                "next_step_suggestion": "重试或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) == 0:
            observation = {
                "success": False,
                "quality_score": 0.2,
                "observation": "工具执行成功但未找到相关结果",
                "next_step_suggestion": "调整搜索参数或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) > 0:
            # 分析POI搜索结果
            quality_score = min(1.0, len(action_result) / 5.0)  # 5个结果为满分
            
            observation = {
                "success": True,
                "quality_score": quality_score,
                "observation": f"成功获得{len(action_result)}个结果，质量评分{quality_score:.2f}",
                "result_summary": {
                    "count": len(action_result),
                    "sample_items": [item.get("name", "未知") for item in action_result[:3]]
                },
                "next_step_suggestion": "将结果添加到行程规划中" if quality_score > 0.6 else "考虑调整搜索条件"
            }
        else:
            # 其他类型结果
            observation = {
                "success": True,
                "quality_score": 0.7,
                "observation": f"获得{tool_name}执行结果",
                "result_summary": str(action_result)[:200],
                "next_step_suggestion": "继续下一步规划"
            }
        
        # 评估是否需要继续规划
        daily_plans = current_state.get("daily_plans", {})
        total_days = current_state.get("consolidated_intent", {}).get("travel_days", 3)
        completed_days = len([day for day, plans in daily_plans.items() if plans])
        
        should_continue = completed_days < total_days or not observation["success"]
        
        observation.update({
            "should_continue_planning": should_continue,
            "planning_progress": {
                "completed_days": completed_days,
                "total_days": total_days,
                "completion_rate": completed_days / total_days if total_days > 0 else 0
            }
        })
        
        return observation
        
    except Exception as e:
        logger.error(f"Failed to observe action result: {str(e)}")
        return {
            "success": False,
            "quality_score": 0.0,
            "observation": f"结果观察失败: {str(e)}",
            "should_continue_planning": True,
            "error": str(e)
        }


@unified_registry.register_planner_tool
async def update_planning_state(
    current_state: Dict[str, Any],
    action: Dict[str, Any],
    action_result: Any,
    observation: Dict[str, Any]
) -> Dict[str, Any]:
    """
    更新规划状态
    
    基于行动结果和观察，更新规划状态
    
    Args:
        current_state: 当前状态
        action: 执行的行动
        action_result: 行动结果
        observation: 观察结果
    
    Returns:
        更新后的状态
    """
    try:
        updated_state = current_state.copy()
        
        # 更新工具结果缓存
        tool_results = updated_state.get("tool_results", {})
        tool_name = action.get("tool_name", "unknown")
        tool_results[tool_name] = action_result
        updated_state["tool_results"] = tool_results
        
        # 如果是POI搜索且成功，更新daily_plans
        if tool_name == "search_poi" and observation.get("success", False):
            target_day = action.get("target_day")
            if target_day and isinstance(action_result, list):
                daily_plans = updated_state.get("daily_plans", {})
                daily_time_tracker = updated_state.get("daily_time_tracker", {})

                # 准备活动列表
                activities = []
                for poi in action_result[:4]:  # 选择前4个POI
                    activity = {
                        "name": poi.get("name", "未知景点"),
                        "address": poi.get("address", ""),
                        "type": poi.get("type", "attraction"),
                        "rating": poi.get("rating", 0),
                        "description": poi.get("description", "")
                    }
                    activities.append(activity)

                # 使用时间规划工具创建合理的每日行程
                schedule_result = await create_daily_schedule(
                    day_number=target_day,
                    activities=activities,
                    constraints={
                        "start_time": "09:00",
                        "end_time": "21:00",
                        "lunch_time": "12:00",
                        "dinner_time": "18:00"
                    },
                    current_state=current_state
                )

                if schedule_result and not schedule_result.get("error"):
                    daily_plans[target_day] = schedule_result["activities"]
                    # 更新时间跟踪器
                    end_time_str = schedule_result.get("end_time", "21:00")
                    daily_time_tracker[target_day] = _time_to_minutes(end_time_str)

                    updated_state["daily_plans"] = daily_plans
                    updated_state["daily_time_tracker"] = daily_time_tracker

                    logger.info(f"成功创建第{target_day}天行程，包含{len(schedule_result['activities'])}个活动")
                else:
                    logger.warning(f"时间规划失败: {schedule_result.get('error', '未知错误')}")
                    # 回退到简单的POI列表
                    daily_plans[target_day] = activities
                    updated_state["daily_plans"] = daily_plans
        
        # 更新规划日志
        planning_log = updated_state.get("planning_log", [])
        log_entry = f"执行{tool_name}，结果：{observation.get('observation', '未知')}"
        planning_log.append(log_entry)
        updated_state["planning_log"] = planning_log
        
        # 更新当前行动
        updated_state["current_action"] = action
        
        return updated_state
        
    except Exception as e:
        logger.error(f"Failed to update planning state: {str(e)}")
        return current_state


@unified_registry.register_planner_tool
def check_planning_completion(
    current_state: Dict[str, Any],
    planning_context: Dict[str, Any]
) -> Dict[str, Any]:
    """
    检查规划完成情况
    
    评估当前规划是否已完成或需要继续
    
    Args:
        current_state: 当前状态
        planning_context: 规划上下文
    
    Returns:
        完成情况检查结果
    """
    try:
        daily_plans = current_state.get("daily_plans", {})

        # 优先从consolidated_intent获取天数，然后从planning_context获取
        consolidated_intent = current_state.get("consolidated_intent", {})
        total_days = consolidated_intent.get("days", planning_context.get("constraints", {}).get("max_days", 3))
        
        # 检查每天是否都有规划
        completed_days = 0
        for day in range(1, total_days + 1):
            if day in daily_plans and daily_plans[day]:
                completed_days += 1
        
        completion_rate = completed_days / total_days if total_days > 0 else 0
        is_complete = completion_rate >= 1.0
        
        # 检查规划质量
        quality_issues = []
        if completed_days < total_days:
            quality_issues.append(f"还有{total_days - completed_days}天未规划")
        
        # 检查预算
        budget_used = current_state.get("total_budget_tracker", 0)
        budget_limit = planning_context.get("constraints", {}).get("budget_limit", 1000)
        if budget_used > budget_limit:
            quality_issues.append(f"预算超支{budget_used - budget_limit}元")
        
        return {
            "is_complete": is_complete,
            "completion_rate": completion_rate,
            "completed_days": completed_days,
            "total_days": total_days,
            "quality_score": 1.0 - len(quality_issues) * 0.2,
            "quality_issues": quality_issues,
            "recommendation": "规划完成" if is_complete and not quality_issues else "需要继续规划或优化"
        }
        
    except Exception as e:
        logger.error(f"Failed to check planning completion: {str(e)}")
        return {
            "is_complete": False,
            "completion_rate": 0.0,
            "quality_score": 0.0,
            "error": str(e),
            "recommendation": "检查失败，建议重新评估"
        }


@unified_registry.register_action_tool
async def optimize_daily_route(pois: List[Dict[str, Any]], trace_id: str = "") -> List[Dict[str, Any]]:
    """
    优化当天的POI访问顺序，基于地理位置进行排序以减少路线上的"反复横跳"

    Args:
        pois: POI列表，每个POI应包含location字段（格式为"lng,lat"）
        trace_id: 追踪ID

    Returns:
        优化后的POI列表
    """
    try:
        logger.info(f"[{trace_id}] 开始优化每日路线，POI数量: {len(pois)}")

        if len(pois) <= 1:
            logger.info(f"[{trace_id}] POI数量不足，无需优化")
            return pois

        # 提取有效位置的POI
        pois_with_location = []
        pois_without_location = []

        for poi in pois:
            location = poi.get('location', '').strip()
            if location and ',' in location:
                try:
                    lng_str, lat_str = location.split(',', 1)
                    lng, lat = float(lng_str.strip()), float(lat_str.strip())

                    # 验证经纬度范围
                    if -180 <= lng <= 180 and -90 <= lat <= 90:
                        poi_copy = poi.copy()
                        poi_copy['_lng'] = lng
                        poi_copy['_lat'] = lat
                        pois_with_location.append(poi_copy)
                    else:
                        logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 经纬度超出范围: {location}")
                        pois_without_location.append(poi)
                except (ValueError, IndexError) as e:
                    logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 位置解析失败: {location}, 错误: {str(e)}")
                    pois_without_location.append(poi)
            else:
                logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 缺少有效位置信息")
                pois_without_location.append(poi)

        if not pois_with_location:
            logger.warning(f"[{trace_id}] 没有有效位置的POI，返回原始顺序")
            return pois

        # 实现简单的地理排序算法
        # 按经度排序，然后按纬度排序，实现从西到东、从南到北的游览顺序
        pois_with_location.sort(key=lambda x: (x.get('_lng', 0), x.get('_lat', 0)))

        # 移除临时添加的经纬度字段
        for poi in pois_with_location:
            poi.pop('_lng', None)
            poi.pop('_lat', None)

        # 将有位置的POI和无位置的POI合并（无位置的放在最后）
        optimized_pois = pois_with_location + pois_without_location

        logger.info(f"[{trace_id}] 路线优化完成，有效位置POI: {len(pois_with_location)}, 无位置POI: {len(pois_without_location)}")

        return optimized_pois

    except Exception as e:
        logger.error(f"[{trace_id}] 路线优化失败: {str(e)}")
        return pois  # 返回原始顺序


@unified_registry.register_action_tool
async def calculate_route_distance(pois: List[Dict[str, Any]], trace_id: str = "") -> Dict[str, Any]:
    """
    计算POI列表的总路线距离和时间

    Args:
        pois: POI列表
        trace_id: 追踪ID

    Returns:
        路线统计信息
    """
    try:
        logger.info(f"[{trace_id}] 开始计算路线距离，POI数量: {len(pois)}")

        if len(pois) < 2:
            return {
                "total_distance": 0.0,
                "total_duration": 0,
                "route_segments": [],
                "average_distance": 0.0
            }

        total_distance = 0.0  # 公里
        total_duration = 0    # 分钟
        route_segments = []

        for i in range(len(pois) - 1):
            current_poi = pois[i]
            next_poi = pois[i + 1]

            current_location = current_poi.get('location', '')
            next_location = next_poi.get('location', '')

            if current_location and next_location:
                # 计算直线距离（简化版本）
                distance = _calculate_haversine_distance(current_location, next_location)
                # 估算步行时间（假设步行速度4km/h）
                duration = int(distance * 15)  # 分钟

                segment = {
                    "from": current_poi.get('name', 'Unknown'),
                    "to": next_poi.get('name', 'Unknown'),
                    "distance": round(distance, 2),
                    "duration": duration
                }
                route_segments.append(segment)

                total_distance += distance
                total_duration += duration

        average_distance = total_distance / len(route_segments) if route_segments else 0.0

        result = {
            "total_distance": round(total_distance, 2),
            "total_duration": total_duration,
            "route_segments": route_segments,
            "average_distance": round(average_distance, 2)
        }

        logger.info(f"[{trace_id}] 路线计算完成，总距离: {result['total_distance']}km, 总时间: {result['total_duration']}分钟")

        return result

    except Exception as e:
        logger.error(f"[{trace_id}] 路线距离计算失败: {str(e)}")
        return {
            "total_distance": 0.0,
            "total_duration": 0,
            "route_segments": [],
            "average_distance": 0.0
        }


def _calculate_haversine_distance(location1: str, location2: str) -> float:
    """
    使用Haversine公式计算两个经纬度点之间的直线距离

    Args:
        location1: 位置1，格式为"lng,lat"
        location2: 位置2，格式为"lng,lat"

    Returns:
        距离（公里）
    """
    try:
        lng1, lat1 = map(float, location1.split(','))
        lng2, lat2 = map(float, location2.split(','))

        # 转换为弧度
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])

        # Haversine公式
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # 地球半径（公里）
        r = 6371

        return c * r

    except (ValueError, IndexError):
        return 0.0


def _extract_city_from_activities(activities: List[Dict[str, Any]]) -> Optional[str]:
    """
    从活动列表中提取城市信息
    """
    for activity in activities:
        # 尝试从地址中提取城市
        address = activity.get("address", "")
        if address:
            # 简单的城市提取逻辑，可以根据需要改进
            if "北京" in address:
                return "北京"
            elif "上海" in address:
                return "上海"
            elif "广州" in address:
                return "广州"
            elif "深圳" in address:
                return "深圳"
            elif "杭州" in address:
                return "杭州"
            elif "成都" in address:
                return "成都"
            elif "西安" in address:
                return "西安"
            elif "南京" in address:
                return "南京"
            elif "莆田" in address:
                return "莆田"
            elif "福州" in address:
                return "福州"
            elif "厦门" in address:
                return "厦门"
            # 可以继续添加更多城市

        # 尝试从POI名称中推断城市
        name = activity.get("name", "")
        if "故宫" in name or "天安门" in name or "颐和园" in name:
            return "北京"
        elif "外滩" in name or "东方明珠" in name:
            return "上海"
        elif "湄洲" in name or "妈祖" in name or "凤凰山" in name or "绶溪" in name:
            return "莆田"

    return None


async def _search_restaurants_for_city(city: str) -> List[Dict[str, Any]]:
    """
    为指定城市搜索餐厅
    """
    try:
        # 导入POI搜索工具
        from src.tools.travel_planner.amap_poi_tools import search_poi

        # 搜索餐厅
        restaurants = await search_poi(
            keywords="餐厅 美食",
            city=city,
            types="050000",  # 餐饮服务类型
            limit=10
        )

        # 转换为标准格式
        formatted_restaurants = []
        for restaurant in restaurants[:6]:  # 最多取6个餐厅
            formatted_restaurant = {
                "poi_id": restaurant.get("poi_id", ""),
                "name": restaurant.get("name", ""),
                "type": "dining",
                "address": restaurant.get("address", ""),
                "rating": restaurant.get("rating", 4.0),
                "phone": restaurant.get("phone", ""),
                "description": restaurant.get("introduction", f"{restaurant.get('name', '')}提供美味佳肴"),
                "cuisine_type": _infer_cuisine_type(restaurant.get("name", "")),
                "location": restaurant.get("location", "")
            }
            formatted_restaurants.append(formatted_restaurant)

        return formatted_restaurants

    except Exception as e:
        logger.error(f"搜索餐厅失败: {str(e)}")
        # 返回默认餐厅信息
        return _get_default_restaurants(city)


def _infer_cuisine_type(restaurant_name: str) -> str:
    """
    根据餐厅名称推断菜系类型
    """
    if any(keyword in restaurant_name for keyword in ["川菜", "四川", "麻辣", "火锅"]):
        return "川菜"
    elif any(keyword in restaurant_name for keyword in ["粤菜", "广东", "茶餐厅", "港式"]):
        return "粤菜"
    elif any(keyword in restaurant_name for keyword in ["湘菜", "湖南", "辣椒"]):
        return "湘菜"
    elif any(keyword in restaurant_name for keyword in ["鲁菜", "山东"]):
        return "鲁菜"
    elif any(keyword in restaurant_name for keyword in ["苏菜", "江苏", "淮扬"]):
        return "苏菜"
    elif any(keyword in restaurant_name for keyword in ["浙菜", "杭帮"]):
        return "浙菜"
    elif any(keyword in restaurant_name for keyword in ["闽菜", "福建"]):
        return "闽菜"
    elif any(keyword in restaurant_name for keyword in ["徽菜", "安徽"]):
        return "徽菜"
    elif any(keyword in restaurant_name for keyword in ["烤鸭", "全聚德", "便宜坊"]):
        return "北京菜"
    elif any(keyword in restaurant_name for keyword in ["西餐", "意大利", "法式", "牛排"]):
        return "西餐"
    elif any(keyword in restaurant_name for keyword in ["日料", "日式", "寿司", "拉面"]):
        return "日料"
    elif any(keyword in restaurant_name for keyword in ["韩式", "韩国", "烤肉"]):
        return "韩料"
    else:
        return "中餐"


def _get_used_attractions(current_state: Dict[str, Any], exclude_day: int) -> set:
    """
    获取已在其他天使用的景点名称，用于去重

    Args:
        current_state: 当前状态
        exclude_day: 排除的天数（当前天）

    Returns:
        已使用的景点名称集合
    """
    used_attractions = set()
    daily_plans = current_state.get("daily_plans", {})

    for day_key, plans in daily_plans.items():
        # 跳过当前天
        if day_key == f"day_{exclude_day}":
            continue

        if isinstance(plans, list):
            for activity in plans:
                if activity.get("type") == "attraction":
                    used_attractions.add(activity.get("name", ""))

    return used_attractions


async def _calculate_distance_between_activities(activity1: Dict[str, Any], activity2: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算两个活动之间的距离和行车时间

    Args:
        activity1: 起始活动
        activity2: 目标活动

    Returns:
        距离信息字典，包含距离和时间
    """
    try:
        from src.tools.unified_registry import unified_registry

        # 获取距离计算工具
        distance_tool = unified_registry.get_tool("calculate_route_distance")
        if not distance_tool:
            return None

        # 获取地址信息
        origin = activity1.get("address", "")
        destination = activity2.get("address", "")

        if not origin or not destination:
            return None

        # 调用距离计算工具
        result = await distance_tool(
            origin=origin,
            destination=destination,
            mode="driving"
        )

        if result and result.get("success"):
            distance_km = result.get("distance_km", 0)
            duration_minutes = result.get("duration_minutes", 0)

            return {
                "distance_km": round(distance_km, 1),
                "duration_minutes": duration_minutes,
                "duration_text": f"{duration_minutes}分钟" if duration_minutes < 60 else f"{duration_minutes//60}小时{duration_minutes%60}分钟"
            }

    except Exception as e:
        logger.warning(f"距离计算失败: {str(e)}")

    return None


async def _optimize_attractions_by_location(attractions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    按地理位置优化景点顺序，减少来回奔波

    Args:
        attractions: 景点列表

    Returns:
        优化后的景点列表
    """
    if len(attractions) <= 1:
        return attractions

    try:
        # 使用高德地图API计算距离并优化路线
        from src.tools.unified_registry import unified_registry

        # 获取距离计算工具
        distance_tool = unified_registry.get_tool("calculate_route_distance")
        if not distance_tool:
            logger.warning("距离计算工具不可用，使用原始顺序")
            return attractions

        # 提取景点位置信息
        locations = []
        for attraction in attractions:
            address = attraction.get("address", "")
            if address:
                locations.append(address)

        if len(locations) < 2:
            return attractions

        # 简单的贪心算法优化路线：从第一个景点开始，每次选择最近的未访问景点
        optimized = [attractions[0]]  # 从第一个景点开始
        remaining = attractions[1:]

        while remaining:
            current_location = optimized[-1].get("address", "")
            if not current_location:
                # 如果当前景点没有地址，随机选择下一个
                optimized.append(remaining.pop(0))
                continue

            # 找到距离当前位置最近的景点
            min_distance = float('inf')
            nearest_idx = 0

            for i, attraction in enumerate(remaining):
                next_location = attraction.get("address", "")
                if next_location:
                    try:
                        # 计算距离（这里简化为地址字符串比较，实际应该用地理坐标）
                        # 在实际实现中，可以调用高德地图API计算真实距离
                        distance = len(set(current_location) ^ set(next_location))  # 简化的距离计算
                        if distance < min_distance:
                            min_distance = distance
                            nearest_idx = i
                    except Exception as e:
                        logger.warning(f"距离计算失败: {str(e)}")

            # 添加最近的景点到路线中
            optimized.append(remaining.pop(nearest_idx))

        logger.info(f"路线优化完成，景点顺序: {[a.get('name', '未知') for a in optimized]}")
        return optimized

    except Exception as e:
        logger.error(f"景点位置优化失败: {str(e)}")
        return attractions


def _get_diverse_search_keywords(
    current_day: int,
    preferred_types: List[str],
    city: str,
    daily_plans: Dict[str, Any]
) -> str:
    """
    为不同天数生成多样化的搜索关键词，避免景点重复

    Args:
        current_day: 当前天数
        preferred_types: 用户偏好的景点类型
        city: 目的地城市
        daily_plans: 已有的每日计划

    Returns:
        搜索关键词字符串
    """
    # 获取已选择的景点，避免重复
    used_pois = set()
    for day_key, plans in daily_plans.items():
        if isinstance(plans, list):
            for activity in plans:
                if activity.get("type") == "attraction":
                    used_pois.add(activity.get("name", ""))

    # 根据城市和偏好类型定义多样化的关键词策略
    if city == "北京":
        keyword_strategies = {
            1: ["故宫", "天安门广场", "紫禁城"],  # 第1天：核心历史景点
            2: ["颐和园", "圆明园", "北海公园"],  # 第2天：皇家园林
            3: ["天坛", "雍和宫", "孔庙"],      # 第3天：宗教文化
            4: ["长城", "明十三陵", "居庸关"],   # 第4天：长城相关
            5: ["什刹海", "南锣鼓巷", "胡同"]    # 第5天：传统街区
        }
    else:
        # 其他城市的通用策略
        keyword_strategies = {
            1: ["历史文化景点", "古迹"],
            2: ["公园", "自然景观"],
            3: ["博物馆", "文化场馆"],
            4: ["特色街区", "商业区"],
            5: ["寺庙", "宗教建筑"]
        }

    # 获取当前天数对应的关键词列表
    day_keywords = keyword_strategies.get(current_day, keyword_strategies.get(1, ["景点"]))

    # 选择一个未使用的关键词
    for keyword in day_keywords:
        # 检查是否已经搜索过类似的景点
        if not any(keyword in poi_name for poi_name in used_pois):
            return keyword

    # 如果所有关键词都用过了，使用备选方案
    fallback_keywords = ["景点", "旅游景点", "名胜古迹", "文化景点"]
    return fallback_keywords[(current_day - 1) % len(fallback_keywords)]


def _get_default_restaurants(city: str) -> List[Dict[str, Any]]:
    """
    获取默认餐厅信息（当搜索失败时使用）
    """
    default_restaurants = {
        "北京": [
            {
                "poi_id": "default_bj_1",
                "name": "全聚德烤鸭店",
                "type": "dining",
                "address": f"{city}市朝阳区",
                "rating": 4.5,
                "description": "北京著名烤鸭老字号，传统京味美食",
                "cuisine_type": "北京菜"
            },
            {
                "poi_id": "default_bj_2",
                "name": "老北京炸酱面馆",
                "type": "dining",
                "address": f"{city}市东城区",
                "rating": 4.2,
                "description": "地道老北京风味，炸酱面香浓可口",
                "cuisine_type": "北京菜"
            },
            {
                "poi_id": "default_bj_3",
                "name": "东来顺涮羊肉",
                "type": "dining",
                "address": f"{city}市西城区",
                "rating": 4.3,
                "description": "百年老字号涮羊肉，肉质鲜嫩汤底醇厚",
                "cuisine_type": "北京菜"
            }
        ],
        "上海": [
            {
                "poi_id": "default_sh_1",
                "name": "南翔小笼包",
                "type": "dining",
                "address": f"{city}市黄浦区",
                "rating": 4.4,
                "description": "上海传统小笼包，皮薄汁多",
                "cuisine_type": "沪菜"
            },
            {
                "poi_id": "default_sh_2",
                "name": "本帮菜馆",
                "type": "dining",
                "address": f"{city}市徐汇区",
                "rating": 4.3,
                "description": "正宗本帮菜，红烧肉、白切鸡经典美味",
                "cuisine_type": "沪菜"
            },
            {
                "poi_id": "default_sh_3",
                "name": "老盛昌汤包",
                "type": "dining",
                "address": f"{city}市静安区",
                "rating": 4.2,
                "description": "上海老字号汤包店，鲜美汤汁回味无穷",
                "cuisine_type": "沪菜"
            }
        ]
    }

    return default_restaurants.get(city, [
        {
            "poi_id": "default_1",
            "name": f"{city}特色餐厅",
            "type": "dining",
            "address": f"{city}市中心",
            "rating": 4.0,
            "description": f"品尝{city}当地特色美食",
            "cuisine_type": "当地菜"
        },
        {
            "poi_id": "default_2",
            "name": f"{city}风味小馆",
            "type": "dining",
            "address": f"{city}市中心",
            "rating": 4.1,
            "description": f"地道{city}风味，家常菜品",
            "cuisine_type": "当地菜"
        }
    ])
