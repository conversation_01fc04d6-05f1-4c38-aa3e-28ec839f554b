from fastapi import HTTPException
import httpx
import logging
import traceback
import asyncio
import json
import uuid
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import os
from src.core.config import get_settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

settings = get_settings()

def get_api_url(server_name: str) -> str:
    return settings.comfyui_url
    # """根据请求方法获取API URL"""
    # api_url = settings.API_URLS.get(server_name.lower(), None)
    # if not api_url:
    #     return settings.API_URL
    #     # raise ValueError(f"未找到对应的API URL配置: {server_name}")
    # return api_url

def load_workflow(workflow_name: str) -> Dict[str, Any]:
    """加载工作流文件"""
    try:
        workflow_path = Path(__file__).parent.parent / "workApi" / workflow_name
        if not workflow_path.exists():
            logger.error(f"工作流文件不存在: {workflow_path}")
            raise HTTPException(status_code=500, detail=f"Workflow file not found: {workflow_path}")
            
        with open(workflow_path, "r", encoding="utf-8") as f:
            workflow = json.load(f)
            logger.info(f"成功读取工作流文件: {workflow_name}")
            return workflow
    except json.JSONDecodeError as e:
        logger.error(f"解析工作流JSON时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error parsing workflow JSON: {str(e)}")
    except Exception as e:
        logger.error(f"读取工作流文件时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error loading workflow: {str(e)}")

async def send_prompt(server_name: str, workflow: Dict[str, Any]) -> Dict[str, Any]:
    """发送工作流到ComfyUI服务器"""
    try:
        prompt_data = {
            "prompt": workflow,
            "client_id": str(uuid.uuid4())
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{get_api_url(server_name)}/prompt",
                json=prompt_data
            )
            response.raise_for_status()
            return response.json()
    except httpx.HTTPError as e:
        logger.error(f"调用ComfyUI API失败: {str(e)}")
        
        # 添加response存在性检查
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"响应状态码: {e.response.status_code}")
            logger.error(f"响应内容: {e.response.text}")
        else:
            logger.error("未收到有效响应，可能是网络连接问题或请求超时")
        
        raise HTTPException(status_code=500, detail=f"ComfyUI API error: {str(e)}")

async def check_image_status(server_name: str, prompt_id: str) -> Dict[str, Any]:
    """检查图像生成状态"""
    max_retries = 3
    retry_count = 0
    retry_delay = 1  # 初始重试延迟（秒）
    
    while retry_count < max_retries:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{get_api_url(server_name)}/history/{prompt_id}"
                )
                response.raise_for_status()
                history = response.json()
                # logger.info(f"历史记录响应: {json.dumps(history, indent=2)}")
                
                # 如果响应为空对象，进行重试
                if not history:
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"历史记录为空，第{retry_count}次重试 (prompt_id: {prompt_id})")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue
                    else:
                        logger.error(f"历史记录连续{max_retries}次为空，返回not_found状态 (prompt_id: {prompt_id})")
                        return {"status": "not_found"}
                
                if prompt_id not in history:
                    logger.warning(f"在历史记录中未找到prompt_id: {prompt_id}")
                    return {"status": "not_found"}
                    
                prompt_data = history[prompt_id]
                if "status" not in prompt_data:
                    logger.warning(f"响应中缺少status字段 (prompt_id: {prompt_id})")
                    return {"status": "invalid_response"}
                    
                status_data = prompt_data["status"]
                if status_data.get("completed", False):
                    logger.info(f"任务已完成 (prompt_id: {prompt_id})")
                    return {
                        "status": "completed",
                        "outputs": prompt_data.get("outputs", {})
                    }
                elif "error" in status_data:
                    logger.error(f"任务出错: {status_data['error']} (prompt_id: {prompt_id})")
                    return {
                        "status": "error",
                        "error": status_data["error"]
                    }
                else:
                    logger.info(f"任务处理中 (prompt_id: {prompt_id})")
                    return {"status": "processing"}
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                logger.warning(f"检查图像状态出错，第{retry_count}次重试: {str(e)} (prompt_id: {prompt_id})")
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                logger.error(f"检查图像状态连续{max_retries}次出错: {str(e)} (prompt_id: {prompt_id})")
                return {"status": "error", "error": str(e)}
    
    # 不应该到达这里，但为了安全起见
    return {"status": "error", "error": "超过最大重试次数"}

async def wait_for_image(server_name: str, prompt_id: str, node_id: str, subfolder: str, max_retries: int = 300) -> Optional[str]:
    """等待图像生成完成，最长等待10分钟"""
    retries = 0
    while retries < max_retries:
        try:
            status_data = await check_image_status(server_name, prompt_id)
            
            if status_data["status"] == "completed":
                outputs = status_data.get("outputs", {})
                if node_id in outputs:
                    image_data = outputs[node_id].get("images", [])
                    if image_data and len(image_data) > 0:
                        image_info = image_data[0]
                        # 构建完整的URL
                        url = f"{get_api_url(server_name)}/view?filename={image_info['filename']}&type=output&subfolder={image_info['subfolder']}&t={int(datetime.now().timestamp())}"
                        logger.info(f"生成图片URL: {url}")
                        return url
                    
            elif status_data["status"] == "error":
                logger.error(f"图像生成错误: {status_data.get('error')}")
                return None
                
            retries += 1
            await asyncio.sleep(2)  # 每2秒检查一次
            
        except Exception as e:
            logger.error(f"等待图像时出错: {str(e)}")
            return None
            
    logger.error("等待图像生成超时（超过10分钟）")
    return None

async def wait_for_text(server_name: str, prompt_id: str, node_id: str, subfolder: str,max_retries: int = 300) -> Optional[str]:
    """等待文本生成完成，最长等待40秒"""
    retries = 0
    while retries < max_retries:
        try:
            status_data = await check_image_status(server_name, prompt_id)

            if status_data["status"] == "completed":
                outputs = status_data.get("outputs", {})
                if node_id in outputs:
                    text_data = outputs[node_id].get("text", '')
                    if text_data and len(text_data) > 0:
                        return text_data[0]

            elif status_data["status"] == "error":
                logger.error(f"文本生成错误: {status_data.get('error')}")
                return None

            retries += 1
            await asyncio.sleep(2)  # 每2秒检查一次

        except Exception as e:
            logger.error(f"等待文本时出错: {str(e)}")
            return None

    logger.error("等待文本生成超时（超过40秒）")
    return None

async def wait_for_audio(server_name: str, prompt_id: str, node_id: str, subfolder: str, max_retries: int = 300) -> Optional[str]:
    """等待音频生成完成，最长等待2分钟"""
    retries = 0
    while retries < max_retries:
        try:
            status_data = await check_image_status(server_name, prompt_id)

            if status_data["status"] == "completed":
                outputs = status_data.get("outputs", {})
                if node_id in outputs:
                    audio_data = outputs[node_id].get("audio", [])
                    if audio_data and len(audio_data) > 0:
                        audio_info = audio_data[0]
                        # 构建完整的URL
                        url = f"{get_api_url(server_name)}/view?filename={audio_info['filename']}&type=output&subfolder={audio_info['subfolder']}&t={int(datetime.now().timestamp())}"
                        logger.info(f"生成音频URL: {url}")
                        return url

            elif status_data["status"] == "error":
                logger.error(f"音频生成错误: {status_data.get('error')}")
                return None

            retries += 1
            await asyncio.sleep(2)  # 每2秒检查一次

        except Exception as e:
            logger.error(f"等待音频时出错: {str(e)}")
            return None

    logger.error("等待音频生成超时（超过2分钟）")
    return None

async def wait_for_video(server_name: str, prompt_id: str, node_id: str, subfolder: str, max_retries: int = 150) -> Optional[str]:
    """等待视频生成完成，最长等待5分钟"""
    video_filename = None
    retries = 0
    while retries < max_retries:
        try:
            status_data = await check_image_status(server_name, prompt_id)

            if status_data["status"] == "completed":
                outputs = status_data.get("outputs", {})
                if node_id in outputs:
                    video_data = outputs[node_id].get("gifs", [])
                    if video_data and len(video_data) > 0:
                        video_info = video_data[0]
                        # 构建完整的URL
                        url = f"{get_api_url(server_name)}/view?filename={video_info['filename']}&subfolder={video_info['subfolder']}&type=output&format=video%2Fh264-mp4&frame_rate=16&t={int(datetime.now().timestamp())}"
                        logger.info(f"生成视频URL: {url}")
                        return url

            elif status_data["status"] == "error":
                logger.error(f"视频生成错误: {status_data.get('error')}")
                return None

            retries += 1
            await asyncio.sleep(2)  # 每2秒检查一次

        except Exception as e:
            logger.error(f"等待视频时出错: {str(e)}")
            return None

    logger.error("等待视频生成超时（超过5分钟）")
    return None

async def wait_for_multiple_images(server_name: str, prompt_id: str, node_id: str, subfolder: str) -> list[str]:
    """等待多张图片生成完成并返回图片URL列表"""
    try:
        while True:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{get_api_url(server_name)}/history/{prompt_id}")
                if response.status_code != 200:
                    logger.error(f"获取历史记录失败: {response.status_code} - {response.text}")
                    raise HTTPException(status_code=500, detail=f"Failed to get history: {response.text}")
                
                history = response.json()
                logger.info(f"历史记录响应: {json.dumps(history, indent=2)}")
                
                if prompt_id in history:
                    outputs = history[prompt_id].get("outputs", {})
                    # 检查是否有任何输出
                    if outputs:
                        urls = []
                        # 遍历所有输出节点
                        for node_output in outputs.values():
                            if "images" in node_output:
                                for image in node_output["images"]:
                                    url = f"{get_api_url(server_name)}/view?filename={image['filename']}&type=output&subfolder={subfolder}&t={int(datetime.now().timestamp())}"
                                    logger.info(f"生成图片URL: {url}")
                                    urls.append(url)
                        if urls:
                            return urls
                await asyncio.sleep(1)
    except Exception as e:
        logger.error(f"等待图片生成时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error waiting for images: {str(e)}")

async def upload_image(server_name: str, image_file: Any, image_type: str) -> str:
    """上传图片到ComfyUI服务器"""
    try:
        logger.info(f"上传图片到ComfyUI - 文件类型: {type(image_file)}")
        
        # 检查文件对象是否有效
        if not hasattr(image_file, 'read'):
            logger.error("文件对象无效，没有read方法")
            raise HTTPException(status_code=400, detail="无效的文件对象")
            
        logger.info("文件对象有效，具有read方法")
        
        # 确保HTTP请求使用正确的Content-Type
        headers = {
            "Accept": "application/json"
        }
        
        # 生成唯一文件名(保留原始扩展名)
        if hasattr(image_file, 'filename') and image_file.filename:
            # 如果文件对象有filename属性，从中提取扩展名
            _, ext = os.path.splitext(image_file.filename)
            if not ext:
                ext = ".jpg"  # 默认扩展名
        else:
            ext = ".jpg"  # 默认扩展名
            
        img_filename = f"upload_{uuid.uuid4().hex}{ext}"
        logger.info(f"生成的唯一文件名: {img_filename}")
        
        # 读取文件内容用于记录大小
        file_content = None
        file_size = 0
        
        # 区分异步读取和同步读取
        try:
            # 检查是否为异步读取方法
            if asyncio.iscoroutinefunction(image_file.read):
                file_content = await image_file.read()
                # 异步模式下重置
                if hasattr(image_file, 'seek') and asyncio.iscoroutinefunction(image_file.seek):
                    await image_file.seek(0)
                else:
                    # 如果没有异步seek方法但有同步seek
                    if hasattr(image_file, 'seek'):
                        image_file.seek(0)
            else:
                # 同步读取
                file_content = image_file.read()
                if hasattr(image_file, 'seek'):
                    image_file.seek(0)
                    
            if file_content:
                file_size = len(file_content)
                logger.info(f"文件大小: {file_size} 字节")
        except Exception as e:
            logger.error(f"读取文件内容失败: {str(e)}")
            # 继续处理，不中断上传
        
        # 准备files参数，确保文件名存在
        files = {"image": (img_filename, image_file, "image/jpeg")}
        logger.info(f"正在POST请求到: {get_api_url(server_name)}/upload/image")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{get_api_url(server_name)}/upload/image", headers=headers, files=files)
            logger.info(f"POST请求完成，状态码: {response.status_code}")
            
            # 记录完整响应内容以便调试
            try:
                response_data = response.json()
                logger.info(f"完整响应数据: {response_data}")
            except Exception as e:
                logger.error(f"解析响应JSON失败: {str(e)}")
                logger.error(f"原始响应内容: {response.text}")
                raise HTTPException(status_code=500, detail="Failed to parse response from ComfyUI")
            
            response.raise_for_status()
            
            # 提取文件名，确保它是有效的
            uploaded_filename = response_data.get("name", "")
            if not uploaded_filename or uploaded_filename.startswith("None"):
                logger.error(f"ComfyUI返回了无效的文件名: {uploaded_filename}")
                logger.info(f"使用我们自己生成的文件名: {img_filename}")
                return img_filename
                
            logger.info(f"上传的{image_type}文件名: {uploaded_filename}")
            return uploaded_filename
    except httpx.HTTPError as e:
        logger.error(f"上传图片到ComfyUI失败: {str(e)}")
        if 'response' in locals():
            logger.error(f"响应状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
        raise HTTPException(status_code=500, detail=f"Failed to upload image to ComfyUI: {str(e)}")
    except Exception as e:
        logger.error(f"上传图片到ComfyUI时发生未知错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Unknown error during image upload: {str(e)}")

# --- 新增获取历史记录函数 ---
async def get_history(server_name: str, prompt_id: str) -> Dict:
    """从ComfyUI获取指定prompt_id的执行历史"""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client: # 设置超时
            response = await client.get(f"{get_api_url(server_name)}/history/{prompt_id}")
            response.raise_for_status() # 检查HTTP错误
            history_data = response.json()
            logger.debug(f"获取到历史记录 prompt_id={prompt_id}: {history_data}")
            return history_data
    except httpx.TimeoutException:
        logger.warning(f"查询ComfyUI历史记录超时: prompt_id={prompt_id}")
        return {} # 返回空字典表示超时或未找到
    except httpx.HTTPStatusError as e:
        logger.error(f"查询ComfyUI历史记录HTTP错误: prompt_id={prompt_id}, status={e.response.status_code}")
        return {} # 返回空字典表示错误
    except Exception as e:
        logger.error(f"查询ComfyUI历史记录时发生未知错误: prompt_id={prompt_id}, error={str(e)}", exc_info=True)
        return {} # 返回空字典表示错误 