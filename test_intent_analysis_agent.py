#!/usr/bin/env python3
"""
意图识别Agent完整流程测试脚本

测试V3.0统一架构中的两阶段意图分析流程：
1. 核心框架分析 (framework_analysis)
2. 个性化偏好分析 (preference_analysis)
3. 意图整合和上下文准备 (prepare_planning_context)

使用方法:
python test_intent_analysis_agent.py
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.tools.unified_registry import unified_registry
from unittest.mock import Mock, AsyncMock

# 确保所有工具被正确注册
import src.tools.travel_planner.consolidated_tools
import src.tools.travel_planner.icp_tools
# 注意：暂时跳过amap_service的导入以避免依赖问题
# import src.agents.services.amap_service


class IntentAnalysisAgentTester:
    """意图识别Agent测试器"""
    
    def __init__(self):
        """初始化测试器"""
        print("🚀 初始化意图识别Agent测试器（使用真实LLM）")

        # 创建模拟Redis客户端（仅用于事件总线，不影响LLM调用）
        self.mock_redis = AsyncMock()
        self.mock_redis_client = Mock()
        self.mock_redis_client.client = self.mock_redis

        # 创建事件总线
        self.event_bus = UnifiedEventBus(self.mock_redis_client, task_ttl=3600)

        # 创建图实例（将使用真实的LLM服务）
        self.graph = TravelPlannerGraphV3(event_bus=self.event_bus)

        print("✅ 测试器初始化完成，将调用真实LLM进行意图分析")
    
    def create_test_scenarios(self) -> list:
        """创建测试场景"""
        scenarios = [
            {
                "name": "北京历史文化游",
                "input": {
                    "user_query": "我想去北京玩三天，喜欢历史文化景点，预算3000元",
                    "user_id": "test_user_001",
                    "execution_mode": "automatic",
                    "user_profile": {
                        "age": 30,
                        "interests": ["文化", "历史", "摄影"],
                        "budget_level": "中等",
                        "travel_style": "深度游"
                    }
                },
                "expected": {
                    "destination": "北京",
                    "days": 3,
                    "budget": 3000,
                    "interests": ["历史", "文化"]
                }
            },
            {
                "name": "上海美食购物游",
                "input": {
                    "user_query": "想和朋友去上海2天，主要想吃美食和购物",
                    "user_id": "test_user_002",
                    "execution_mode": "automatic",
                    "user_profile": {
                        "age": 25,
                        "interests": ["美食", "购物", "时尚"],
                        "budget_level": "高",
                        "travel_style": "休闲游"
                    }
                },
                "expected": {
                    "destination": "上海",
                    "days": 2,
                    "interests": ["美食", "购物"]
                }
            },
            {
                "name": "家庭亲子游",
                "input": {
                    "user_query": "计划带孩子去广州长隆玩，一家四口，孩子8岁和5岁",
                    "user_id": "test_user_003",
                    "execution_mode": "automatic",
                    "user_profile": {
                        "age": 35,
                        "interests": ["亲子", "游乐园", "动物园"],
                        "budget_level": "中等",
                        "travel_style": "家庭游",
                        "group_size": 4,
                        "children_ages": [8, 5]
                    }
                },
                "expected": {
                    "destination": "广州",
                    "group_size": 4,
                    "interests": ["亲子", "游乐园"]
                }
            }
        ]
        
        return scenarios
    
    async def test_single_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个场景"""
        print(f"\n📋 测试场景: {scenario['name']}")
        print(f"📝 用户查询: {scenario['input']['user_query']}")
        
        try:
            print(f"🔄 开始执行真实LLM意图分析...")

            # 执行意图分析流程（使用真实LLM）
            # 注意：这将调用真实的LLM服务进行分析
            result = await self.graph.invoke(scenario['input'])

            print(f"✅ LLM分析完成，当前阶段: {result.get('current_phase', 'Unknown')}")

            # 分析结果
            analysis_result = {
                "scenario_name": scenario['name'],
                "success": True,
                "framework_analysis": result.get("framework_analysis"),
                "preference_analysis": result.get("preference_analysis"),
                "consolidated_intent": result.get("consolidated_intent"),
                "icp_context": result.get("icp_context"),
                "current_phase": result.get("current_phase"),
                "has_error": result.get("has_error", False),
                "error_message": result.get("error_message")
            }
            
            # 验证期望结果
            self._validate_expectations(analysis_result, scenario.get("expected", {}))
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            return {
                "scenario_name": scenario['name'],
                "success": False,
                "error": str(e)
            }
    
    def _validate_expectations(self, result: Dict[str, Any], expected: Dict[str, Any]):
        """验证期望结果"""
        print("\n🔍 验证分析结果:")
        
        # 检查基本状态
        if result.get("has_error"):
            print(f"❌ 发现错误: {result.get('error_message')}")
            return
        
        # 检查框架分析
        framework = result.get("framework_analysis", {})
        if framework:
            print("✅ 框架分析完成")
            core_intent = framework.get("core_intent", {})
            if "destination" in expected:
                actual_dest = core_intent.get("destination", "")
                expected_dest = expected["destination"]
                if expected_dest.lower() in actual_dest.lower():
                    print(f"✅ 目的地识别正确: {actual_dest}")
                else:
                    print(f"⚠️ 目的地识别可能有误: 期望包含'{expected_dest}', 实际'{actual_dest}'")
        else:
            print("❌ 框架分析缺失")
        
        # 检查偏好分析
        preference = result.get("preference_analysis", {})
        if preference:
            print("✅ 偏好分析完成")
            interests = preference.get("interests", [])
            if "interests" in expected:
                expected_interests = expected["interests"]
                found_interests = [i for i in expected_interests if any(i.lower() in interest.lower() for interest in interests)]
                if found_interests:
                    print(f"✅ 兴趣识别正确: {found_interests}")
                else:
                    print(f"⚠️ 兴趣识别可能有误: 期望{expected_interests}, 实际{interests}")
        else:
            print("❌ 偏好分析缺失")
        
        # 检查整合意图
        consolidated = result.get("consolidated_intent", {})
        if consolidated:
            print("✅ 意图整合完成")
        else:
            print("❌ 意图整合缺失")
        
        # 检查ICP上下文
        icp_context = result.get("icp_context", {})
        if icp_context:
            print("✅ ICP上下文准备完成")
        else:
            print("❌ ICP上下文缺失")
    
    def _print_detailed_results(self, result: Dict[str, Any]):
        """打印详细结果"""
        print(f"\n📊 详细分析结果:")
        print(f"当前阶段: {result.get('current_phase', 'Unknown')}")
        
        # 框架分析结果
        framework = result.get("framework_analysis", {})
        if framework:
            print("\n🏗️ 框架分析结果:")
            core_intent = framework.get("core_intent", {})
            for key, value in core_intent.items():
                print(f"  {key}: {value}")
        
        # 偏好分析结果
        preference = result.get("preference_analysis", {})
        if preference:
            print("\n❤️ 偏好分析结果:")
            for key, value in preference.items():
                if isinstance(value, list):
                    print(f"  {key}: {', '.join(map(str, value))}")
                else:
                    print(f"  {key}: {value}")
        
        # 整合意图
        consolidated = result.get("consolidated_intent", {})
        if consolidated:
            print("\n🎯 整合意图:")
            for key, value in consolidated.items():
                if isinstance(value, (dict, list)):
                    print(f"  {key}: {json.dumps(value, ensure_ascii=False, indent=2)}")
                else:
                    print(f"  {key}: {value}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始意图识别Agent完整流程测试")
        print("=" * 60)
        
        scenarios = self.create_test_scenarios()
        results = []
        
        for scenario in scenarios:
            result = await self.test_single_scenario(scenario)
            results.append(result)
            
            if result.get("success"):
                print("✅ 测试通过")
                self._print_detailed_results(result)
            else:
                print("❌ 测试失败")
            
            print("-" * 40)
        
        # 总结报告
        self._print_summary_report(results)
    
    def _print_summary_report(self, results: list):
        """打印总结报告"""
        print("\n📈 测试总结报告")
        print("=" * 60)
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get("success"))
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"失败测试: {total_tests - successful_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        
        print("\n📋 各场景结果:")
        for result in results:
            status = "✅" if result.get("success") else "❌"
            print(f"{status} {result['scenario_name']}")
            if not result.get("success"):
                print(f"   错误: {result.get('error', 'Unknown error')}")
        
        print("\n🎯 意图识别Agent测试完成!")


async def main():
    """主函数"""
    tester = IntentAnalysisAgentTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
