# 旅行规划中基于距离的POI组织逻辑

本文档详细说明了 AutoPilot AI 项目在旅行规划中，如何根据兴趣点（POI）之间的地理位置（距离）来组织和优化每日行程，确保路线的合理性。

## 1. 核心目标

避免在规划的行程中出现地理位置上的“反复横跳”，例如从城东的一个景点直接跳到城西，然后再返回城东。系统应生成一条在地理上连续、顺畅的游览路线，以节省交通时间并提升用户体验。

## 2. 逻辑实现概览

系统通过一个多步骤的工作流来实现基于距离的规划。其核心思想并非在规划开始时就计算所有POI之间的复杂距离矩阵，而是在生成每日行程的环节，对当天的POI列表进行地理位置排序。

### 工作流分解

1.  **POI 收集**: 系统首先根据用户偏好和目的地，搜集相关的景点、餐厅等POI。
2.  **POI 质量筛选**: 使用 `_score_and_sort_pois` 函数，根据评分、价格等因素对POI进行初步筛选和排序，选出高质量的候选点。
3.  **每日POI分配**: 将筛选后的POI分配到旅行的每一天。
4.  **每日路线优化 (核心)**: 对每一天的POI列表，使用 `_optimize_daily_route` 函数进行地理位置排序，这是实现距离规划的关键。
5.  **交通时间估算**: 在POI顺序确定后，调用 `_calculate_daily_travel_time` 函数，估算当天行程所需的总交通时间。

## 3. 关键文件与函数

### 文件位置

-   **`src/agents/travel_planner_lg/nodes.py`**: 这是所有核心规划逻辑的所在地。

### 关键函数

#### a. `execute_planning_stage`

-   **职责**: 规划阶段的入口函数，负责调用POI搜索和行程创建流程。
-   **位置**: `src/agents/travel_planner_lg/nodes.py`

#### b. `_create_intelligent_itinerary`

-   **职责**: 接受POI列表，并为单个城市创建完整的每日行程。它负责调用POI筛选、分配和路线优化等子任务。
-   **位置**: `src/agents/travel_planner_lg/nodes.py`

#### c. `_optimize_daily_route`

-   **职责**: **这是实现距离规划的核心函数**。它接收某一天的POI列表，并根据地理位置对其进行重新排序。
-   **位置**: `src/agents/travel_planner_lg/nodes.py`
-   **具体实现**:
    ```python
    # src/agents/travel_planner_lg/nodes.py

    async def _optimize_daily_route(pois: list, amap_service, trace_id: str) -> list:
        """
        优化当天的POI访问顺序
        """
        if len(pois) <= 1:
            return pois

        try:
            # ...
            
            # 按经纬度排序，实现简单的地理聚类
            pois_with_location = []
            for poi in pois:
                location = poi.get('location', '').split(',')
                if len(location) == 2:
                    try:
                        lng, lat = float(location[0]), float(location[1])
                        poi['_lng'] = lng
                        poi['_lat'] = lat
                        pois_with_location.append(poi)
                    # ...
            
            # 按经度排序，然后按纬度排序，实现简单的路线优化
            pois_with_location.sort(key=lambda x: (x.get('_lng', 0), x.get('_lat', 0)))

            return pois_with_location

        except Exception as e:
            # ...
            return pois  # 返回原始顺序
    ```

## 4. 实现逻辑分析

-   **当前方法**: 系统采用的是一种**简化的地理排序算法**。它首先获取每个POI的经纬度，然后将它们按照“先经度，后纬度”的规则进行排序。
-   **效果**: 这种方法可以将地理位置上相近的POI聚集在一起。例如，一天的行程会大致沿着从西到东（或从东到西）的方向展开，有效避免了在城市两端之间来回穿梭的情况。
-   **优点**:
    -   **性能高**: 相比于复杂的TSP（旅行商问题）算法，简单的排序操作计算成本极低，可以快速生成结果。
    -   **实现简单**: 代码逻辑清晰，易于维护。
    -   **效果显著**: 对于大多数城市内的旅行规划场景，这种方法足以生成一条看起来非常合理的路线。
-   **潜在局限**:
    -   **非最优解**: 这不是一个严格的TSP解决方案，不能保证生成的是绝对意义上的“最短路径”。在一些POI分布极其复杂的极端情况下，可能不是最优选择。但对于当前应用场景，这是一个非常出色的工程折衷。

## 5. 总结

AutoPilot AI项目已经成功实现了基于距离的POI规划逻辑。该逻辑通过在行程生成阶段对每日POI进行地理位置排序，确保了行程路线的合理性和流畅性，为用户提供了高质量的规划体验。

## 6. 架构重构：从“分层递进”到“AI驱动的迭代式规划”

为了从根本上提升规划的智能化、灵活性和对复杂场景的适应能力，我们决定进行一次架构重构。放弃原有的、偏程序化的“分层递进式”策略，引入一种全新的、由AI主导的**“迭代式上下文规划”（Iterative Contextual Planning, ICP）**模型。

### 核心思想转变

-   **旧模式 (分层递进)**: 流程僵化，如 `景点 -> 美食 -> 住宿`。AI主要在流程最后进行“语言润色”，无法在规划过程中动态决策。
-   **新模式 (ICP)**: **AI是规划的主导者**。它通过一个**“思考 -> 行动 -> 观察”**的循环，一步步动态地构建行程。每一步决策都基于此前所有步骤积累的完整上下文，从而能做出更合理、更人性化的选择。

### “迭代式上下文规划” (ICP) 工作流

ICP模型将 `LangGraph` 的能力发挥到极致，其核心是一个智能循环，而非线性流程。

1.  **启动 (Initial Analysis)**:
    -   保留并复用现有的`analyze_core_intent`等分析节点。
    -   **产出**: 结构化的核心意图和用户偏好，作为迭代规划的初始上下文。

2.  **进入迭代循环 (Planning Loop)**:
    -   **步骤A: 思考 (Planner Agent Node)**
        -   **角色**: 这是新的核心节点，由一个强大的LLM Agent驱动。
        -   **输入**: 当前完整的`AgentState`，包含初始意图、已规划的POI、剩余时间、预算等所有上下文。
        -   **任务**: 基于当前上下文，决定**下一步最应该做什么**。例如：
            -   “第一天上午，我们应该先找一个核心景点。”
            -   “这个景点已经规划好了，现在是午餐时间，应该在附近找个符合用户口味的餐厅。”
            -   “今天已经安排了3个活动，时间差不多了，应该规划住宿了。”
            -   “所有天的行程都已规划完毕，现在应该结束规划。”
        -   **输出**: 一个结构化的**行动指令**，如 `{ "tool_name": "search_poi", "parameters": { "keywords": "故宫" } }` 或 `{ "tool_name": "finish_planning", "parameters": {} }`。

    -   **步骤B: 行动 (Tool Executor Node)**
        -   **角色**: 这是一个工具调用节点。
        -   **任务**: 根据上一步LLM生成的指令，精确地调用对应的工具（如`AmapService.search_poi`, `AmapService.search_around`等）。
        -   **输出**: 工具调用的原始结果。

    -   **步骤C: 观察 (State Update)**
        -   **角色**: 系统的状态管理机制。
        -   **任务**: 将工具调用的结果整合进`AgentState`，丰富上下文，然后**将控制权交还给步骤A的Planner Agent**。

3.  **结束 (Finalization)**:
    -   当Planner Agent决定规划完成并发出`finish_planning`指令后，循环终止。
    -   系统进入最终的行程格式化和输出环节。

### 新架构优势
- **高度智能化**: AI全程主导，能处理复杂的依赖关系和动态变化（例如，如果一个景点没搜到，AI可以决定换一个）。
- **灵活性强**: 不再局限于固定的`景-餐-住`顺序，可以根据实际情况灵活安排，如先安排酒店入住再开始游玩。
- **用户体验更佳**: 规划过程更贴近人类思维，最终结果更合理、更个性化。
- **可扩展性好**: 未来增加新的规划能力（如购物、娱乐），只需为Planner Agent增加新的知识和工具选项，而无需重构整个工作流。

---
## 7. 实施路线图与待办事项 (Implementation Roadmap & Todos)

本章节旨在将全新的ICP架构蓝图转化为一份具体、可执行的开发任务清单。

### T1: LangGraph工作流重构 (Workflow Refactoring)
- **任务描述**: 重构 `src/agents/travel_planner_lg/graph.py` 的工作流。将线性的规划节点链，改造为一个以 `planner_agent_node` 为核心的循环结构。
    - 创建一个 `router` (条件边)，根据 `planner_agent_node` 输出的指令（`tool_name`），决定是调用`tool_executor_node`还是`finish_node`。

### T2: 核心Planner Agent节点实现 (Planner Agent Implementation)
- **任务描述**: 在 `src/agents/travel_planner_lg/nodes.py` 中创建新的核心节点 `planner_agent_node`。
- **核心逻辑**:
    1.  从`AgentState`中提取所有相关上下文。
    2.  设计一个强大的**新Prompt** (`planner_agent.md`)，指导LLM扮演“行程规划师”的角色。
    3.  **增加时空连续性逻辑**：Prompt必须明确指示AI，在规划完一个耗时较长的活动（如景点）后，需要：
        a.  根据活动耗时，推进内部的“虚拟时钟”。
        b.  判断当前时间是否进入饭点（如11:30-13:30）。
        c.  如果是，则下一个生成的行动指令**必须是**在刚规划好的POI附近，使用`search_around`等工具寻找合适的餐厅。
    4.  调用 `ReasoningService` 执行LLM推理。
    5.  解析LLM返回的JSON格式行动指令，并更新到`AgentState`中，供`router`使用。

### T3: 通用工具执行器节点实现 (Tool Executor Implementation)
- **任务描述**: 在 `src/agents/travel_planner_lg/nodes.py` 中创建 `tool_executor_node`。
- **核心逻辑**:
    1.  从`AgentState`中读取`planner_agent_node`生成的行动指令。
    2.  根据`tool_name`动态调用`AmapService`中对应的服务方法。
    3.  将工具执行结果写回`AgentState`。

### T4: AgentState状态扩展 (State Enhancement)
- **任务描述**: 扩展 `src/agents/travel_planner_lg/state.py` 中的 `TravelPlanState`。
- **新增字段**:
    - `planning_log: List[str]`: 记录Planner Agent每一步的思考过程。
    - `current_action: Dict[str, Any]`: 存储当前需要执行的工具调用指令。
    - `daily_plans: Dict[int, List[Dict]]`: 按天存储已确定的POI。
    - `daily_time_tracker: Dict[int, int]`: 按天跟踪已用时间。
    - `total_budget_tracker: float`: 跟踪已用预算。

### T5: 新建Planner Agent提示词 (New Prompt Creation)
- **任务描述**: 在 `src/prompts/travel_planner/` 目录下创建一个新的 `planner_agent.md` 文件。
- **Prompt核心要点**:
    - **角色定义**: “你是一位专业的旅行规划师...”
    - **上下文输入**: 接收`core_intent`, `user_preferences`, `daily_plans`, `time_tracker`等状态。
    - **核心任务**: “根据当前已规划的行程和剩余时间，决定下一步最合理的操作。”
    - **时空连续性指令**:
        - "你必须维护一个内部的虚拟时钟，每安排一个活动，就要根据其建议时长推进时钟。"
        - "当一个活动结束后，如果虚拟时钟显示已到午餐（11:30-13:30）或晚餐（17:30-19:30）时间，你的首要任务是在上一个活动的地点附近寻找餐厅。"
    - **可用工具列表**: 明确告知AI可以调用哪些工具 (`search_poi`, `search_around`, `get_driving_route`等)。
    - **输出格式约束**: 强制要求以JSON格式输出包含`tool_name`和`parameters`的行动指令。
    - **终止条件**: 指导AI在何时应判断规划完成并输出`finish_planning`指令。

### 建议实施优先级
1.  **最高优先级**: `T4` 和 `T5`。定义好新的状态和大脑（Prompt）是基础。
2.  **第二优先级**: `T2` 和 `T3`。实现新的核心循环节点。
3.  **第三优先级**: `T1`。将所有节点在计算图中正确地连接起来。

## 8. 具体代码修改点 (Concrete Code Modification Points)

本章节提供更具体、可操作的编码指导。

### a. `src/agents/travel_planner_lg/graph.py`
- **目标**: `TravelPlannerGraph.build_graph()`
- **Action**:
    ```python
    # 伪代码
    from langgraph.graph import StateGraph, END

    workflow = StateGraph(AgentState)
    
    # 1. 定义节点
    workflow.add_node("initial_analyzer", nodes.run_initial_analysis)
    workflow.add_node("planner_agent", nodes.planner_agent_node)
    workflow.add_node("tool_executor", nodes.tool_executor_node)

    # 2. 定义边
    workflow.set_entry_point("initial_analyzer")
    workflow.add_edge("initial_analyzer", "planner_agent")
    workflow.add_edge("tool_executor", "planner_agent") # 循环的关键

    # 3. 定义条件边 (Router)
    workflow.add_conditional_edges(
        "planner_agent",
        nodes.route_action, # 一个根据action决定下一跳的函数
        {
            "execute_tool": "tool_executor",
            "finish": END
        }
    )
    
    app = workflow.compile()
    ```

### b. `src/agents/travel_planner_lg/nodes.py`
- **Action**:
    1.  **创建 `planner_agent_node(state)`**: 内部调用`ReasoningService`和新的`planner_agent.md`。
    2.  **创建 `tool_executor_node(state)`**: 使用`getattr(amap_service, tool_name)`动态调用工具。
    3.  **创建 `route_action(state)`**:
        ```python
        # 伪代码
        def route_action(state: AgentState):
            action = state.get("current_action", {})
            if action.get("tool_name") == "finish_planning":
                return "finish"
            else:
                return "execute_tool"
        ```

### c. `src/agents/services/amap_service.py`
- **Action**: 无需大改。确保该文件中的方法（`search_poi`, `search_around`等）粒度合适，易于被`tool_executor_node`调用即可。可在此处统一集成缓存和重试逻辑。

### d. `src/prompts/travel_planner/planner_agent.md` (新建)
- **Action**: 创建该文件，并编写强大的、包含完整指令的Prompt。这是新架构的“大脑”，其质量直接决定规划效果。

## 9. 可复用代码分析 (Reusable Code Analysis for ICP)

在新架构下，我们依然可以复用大量现有代码，但它们的角色发生了变化。

-   **Component**: `analyze_core_intent()`, etc.
    -   **Reusability**: `完全可复用`。
    -   **Role**: 作为新工作流的**启动节点**，为Planner Agent提供初始规划上下文。

-   **Component**: `_optimize_daily_route()`, `_score_and_sort_pois()`
    -   **Reusability**: `可作为独立工具复用`。
    -   **Role**: 不再是流程的一部分，而是可以被Planner Agent**按需调用**的工具。例如，当一天规划了多个POI后，Planner Agent可以决定调用`_optimize_daily_route`工具来优化当天的顺序。

-   **Component**: `AmapService` 中的所有方法
    -   **Reusability**: `完全可复用`。
    -   **Role**: 这些方法现在是Planner Agent可用的**核心工具集**。

-   **Component**: `UserProfileService`, `MemoryService`
    -   **Reusability**: `完全可复用`。
    -   **Role**: 继续为Agent提供关键的用户画像和记忆信息，是Planner Agent做出个性化决策的重要依据。

## 10. 最终效果与展望 (Final Effect & Outlook)

通过实施ICP架构，我们将得到一个真正意义上的“智能规划Agent”。

- **规划过程**: 用户将能通过日志（`planning_log`）清晰地看到AI的每一步思考过程，例如：“*决定先规划故宫*”、“*已安排故宫游览时间为9:00-12:00，时间已到中午，我将在故宫附近搜索评价高的烤鸭店*”、“*路线显示从烤鸭店去颐和园太远，决定明天再去*”。
- **规划结果**: 最终的行程将是AI在综合考虑了时间、空间、预算和用户偏好后，动态生成的最优解，而不仅仅是一个数据列表。
- **未来发展**: 此架构为未来的功能扩展奠定了坚实的基础。无论是增加对“实时路况”的考量，还是引入“门票预订”等新功能，都只需要为Planner Agent增加新的知识和工具，而无需改变核心的迭代式工作流。

这份重构计划将引领AutoPilot AI的旅行规划能力迈上一个新的台阶，从一个“信息聚合器”进化为一个真正的“AI旅行专家”。

## 11. 统一工具与Function Call架构 (Unified Tool & Function Call Architecture)

本章节是对ICP模型中"行动"（Tool/Function Call）层面的深入设计，旨在建立一个接口统一、可扩展、易于维护的工具调用架构，以解决当前项目中工具定义分散、接口不一的问题。

### 11.1 核心设计原则：职责分离

为了实现接口统一，我们将工具明确划分为两类：

1.  **行动工具 (Action Tools)**:
    *   **职责**: 执行与外部世界交互的具体动作。它们是AI的"手和脚"。
    *   **示例**: `AmapService.search_poi`, `DatabaseService.query_user_profile`。
    *   **管理方式**: 通过一个中央**"工具注册表" (`ToolRegistry`)** 进行统一注册和管理。

2.  **规划器工具 (Planner Tools)**:
    *   **职责**: 辅助`planner_agent`进行思考和推理，通常用于生成复杂的提示或定义LLM的输出格式。它们是AI的"内部思维框架"。
    *   **示例**: `src/tools/travel_planner/format_tools.py`中的所有函数。
    *   **管理方式**: 作为`planner_agent_node`内部的辅助函数，**不进入**全局注册表，按需直接调用。

这种分离确保了`planner_agent`在决策时，其"可用动作"的列表是清晰、稳定且可管理的。

### 11.2 `UnifiedToolRegistry`：中央工具注册表

这是统一架构的核心。我们将创建一个`UnifiedToolRegistry`类，负责管理所有的`Action Tools`，并与现有的format_tools整合。

*   **文件路径**: 建议创建 `src/tools/unified_registry.py`。
*   **核心功能**:
    1.  **`@register_action_tool`装饰器**:
        -   一个用于注册`Action Tool`的装饰器。
        -   它会自动解析被装饰函数的：
            -   函数名（作为`tool_name`）。
            -   类型注解的参数（作为`parameters`的`properties`）。
            -   函数的docstring（作为`description`）。
        -   最终生成一个符合OpenAI Function Calling规范的JSON Schema，并存入注册表。
    2.  **`get_tool_schema(name)`**: 根据工具名获取其JSON Schema。
    3.  **`get_all_action_schemas()`**: 获取所有已注册Action Tools的Schema列表。这是提供给`planner_agent`的"可用工具说明书"。
    4.  **`execute_action_tool(name, **kwargs)`**: 根据工具名和参数，动态查找并执行对应的函数。这是`tool_executor_node`的核心逻辑。
    5.  **`get_planner_tools()`**: 获取所有规划器工具的引用，用于`planner_agent_node`内部使用。

**示例 (`src/tools/unified_registry.py`)**:
```python
# 统一工具注册表实现
import inspect
import json
from typing import Dict, Any, Callable, List, Optional
from functools import wraps
from pydantic import BaseModel, Field

class UnifiedToolRegistry:
    def __init__(self):
        self._action_tools: Dict[str, Callable] = {}
        self._action_schemas: Dict[str, Dict] = {}
        self._planner_tools: Dict[str, Callable] = {}

    def register_action_tool(self, func: Callable) -> Callable:
        """注册Action Tool装饰器"""
        tool_name = func.__name__
        schema = self._generate_openai_schema(func)
        
        self._action_tools[tool_name] = func
        self._action_schemas[tool_name] = schema
        return func

    def register_planner_tool(self, func: Callable) -> Callable:
        """注册Planner Tool装饰器"""
        tool_name = func.__name__
        self._planner_tools[tool_name] = func
        return func

    def _generate_openai_schema(self, func: Callable) -> Dict[str, Any]:
        """生成OpenAI Function Calling格式的Schema"""
        sig = inspect.signature(func)
        properties = {}
        required = []
        
        for param_name, param in sig.parameters.items():
            if param_name in ['self', 'cls']:
                continue
                
            param_info = {
                "type": self._get_type_string(param.annotation),
                "description": f"Parameter {param_name}"
            }
            
            if param.default == inspect.Parameter.empty:
                required.append(param_name)
            
            properties[param_name] = param_info
        
        return {
            "type": "function",
            "function": {
                "name": func.__name__,
                "description": func.__doc__ or f"Execute {func.__name__}",
                "parameters": {
                    "type": "object",
                    "properties": properties,
                    "required": required
                }
            }
        }

    def _get_type_string(self, annotation) -> str:
        """将Python类型转换为JSON Schema类型"""
        if annotation == str:
            return "string"
        elif annotation == int:
            return "integer"
        elif annotation == float:
            return "number"
        elif annotation == bool:
            return "boolean"
        elif annotation == list:
            return "array"
        elif annotation == dict:
            return "object"
        else:
            return "string"

    def get_all_action_schemas(self) -> List[Dict[str, Any]]:
        """获取所有Action Tools的Schema"""
        return list(self._action_schemas.values())

    async def execute_action_tool(self, name: str, **kwargs) -> Any:
        """执行Action Tool"""
        if name not in self._action_tools:
            raise ValueError(f"Action tool '{name}' not found.")
        
        tool_func = self._action_tools[name]
        if inspect.iscoroutinefunction(tool_func):
            return await tool_func(**kwargs)
        else:
            return tool_func(**kwargs)

    def get_planner_tool(self, name: str) -> Optional[Callable]:
        """获取Planner Tool"""
        return self._planner_tools.get(name)

    def get_all_planner_tools(self) -> Dict[str, Callable]:
        """获取所有Planner Tools"""
        return self._planner_tools.copy()

# 创建全局实例
unified_registry = UnifiedToolRegistry()

# 用法示例
# @unified_registry.register_action_tool
# async def search_poi(keywords: str, city: str) -> List[Dict]:
#     """根据关键词在指定城市搜索POI"""
#     ...

# @unified_registry.register_planner_tool
# def format_intent_analysis_prompt(user_query: str, user_profile: Dict) -> str:
#     """格式化意图分析提示词"""
#     ...
```

### 11.3 LangGraph状态管理标准化

为了支持统一的工具调用和事件驱动架构，我们需要定义标准化的AgentState结构：

**文件路径**: `src/agents/travel_planner_lg/state.py`
**标准化状态定义**:
```python
from typing import Dict, Any, List, Optional, Annotated
from typing_extensions import TypedDict
from langgraph.graph.message import add_messages

class StandardAgentState(TypedDict):
    # 基础消息流
    messages: Annotated[List[Dict[str, Any]], add_messages]
    
    # 意图分析结果（两步整合模式）
    framework_analysis: Optional[Dict[str, Any]]  # 核心框架分析
    preference_analysis: Optional[Dict[str, Any]]  # 偏好分析
    consolidated_intent: Optional[Dict[str, Any]]  # 整合后的意图
    
    # ICP迭代规划状态
    planning_log: List[str]  # 规划思考日志
    current_action: Optional[Dict[str, Any]]  # 当前待执行的动作
    daily_plans: Dict[int, List[Dict[str, Any]]]  # 按天存储的POI
    daily_time_tracker: Dict[int, int]  # 按天的时间跟踪
    total_budget_tracker: float  # 预算跟踪
    
    # 工具执行结果
    tool_results: Dict[str, Any]  # 工具执行结果缓存
    
    # 事件驱动状态
    task_id: str  # 任务ID
    current_phase: str  # 当前阶段（analysis/planning/completed）
    notification_service: Optional[Any]  # 通知服务实例
    
    # 最终结果
    final_itinerary: Optional[Dict[str, Any]]  # 最终行程
    is_completed: bool  # 是否完成
    has_error: bool  # 是否有错误
    error_message: Optional[str]  # 错误信息
```

### 11.4 改造Service层以支持统一注册

现有的`AmapService`等服务需要进行调整，以将其方法注册为`Action Tools`：

**文件路径**: `src/agents/services/amap_service.py`
**改造方式**:
```python
from src.tools.unified_registry import unified_registry

class AmapService:
    @unified_registry.register_action_tool
    async def search_poi(self, keywords: str, city: str, poi_type: str = "") -> List[Dict]:
        """
        根据关键词在指定城市搜索POI（兴趣点）。
        
        Args:
            keywords: 搜索关键词
            city: 目标城市
            poi_type: POI类型（景点、餐厅、酒店等）
        """
        # ... 原有实现 ...

    @unified_registry.register_action_tool
    async def search_around(self, location: str, radius: int, poi_type: str) -> List[Dict]:
        """
        在指定坐标点周边一定半径范围内搜索POI。
        
        Args:
            location: 中心点坐标（经度,纬度格式）
            radius: 搜索半径（米）
            poi_type: POI类型
        """
        # ... 原有实现 ...

    @unified_registry.register_action_tool
    async def get_driving_route(self, origin: str, destination: str, waypoints: List[str] = None) -> Dict:
        """
        获取驾车路线规划。
        
        Args:
            origin: 起点坐标
            destination: 终点坐标
            waypoints: 途经点列表（可选）
        """
        # ... 原有实现 ...
```

### 11.5 整合现有Format Tools

将现有的`format_tools.py`中的函数注册为Planner Tools：

**文件路径**: `src/tools/travel_planner/format_tools.py`
**整合方式**:
```python
from src.tools.unified_registry import unified_registry

@unified_registry.register_planner_tool
def define_intent_extraction_schema() -> Dict[str, Any]:
    """定义意图提取的JSON Schema"""
    # ... 原有实现 ...

@unified_registry.register_planner_tool
def format_llm_prompt_with_schema(prompt_template: str, schema: Dict[str, Any], **variables) -> str:
    """格式化包含Schema的LLM提示词"""
    # ... 原有实现 ...

@unified_registry.register_planner_tool
def define_poi_scoring_models() -> Dict[str, Any]:
    """定义POI评分模型的Schema"""
    # ... 原有实现 ...
```

### 11.6 更新ICP工作流以使用统一注册表

最后，我们需要更新`nodes.py`中的核心节点，使其与`UnifiedToolRegistry`进行交互：

**a. `planner_agent_node`**:
-   **改造点**: 在调用LLM之前，先从`UnifiedToolRegistry`获取所有Action Tools的Schema。
-   **代码实现**:
    ```python
    from src.tools.unified_registry import unified_registry

    async def planner_agent_node(state: StandardAgentState):
        # 1. 获取所有可用Action Tools的Schema
        available_actions = unified_registry.get_all_action_schemas()
        
        # 2. 获取Planner Tools
        format_prompt = unified_registry.get_planner_tool("format_llm_prompt_with_schema")
        
        # 3. 使用Planner Tool格式化提示词
        formatted_prompt = format_prompt(
            prompt_template="planner_agent.md",
            available_tools=available_actions,
            current_state=state
        )

        # 4. 调用LLM
        response = await reasoning_service.call_llm(formatted_prompt)
        
        # 5. 解析并存储行动指令
        action = response.get("action", {})
        return {"current_action": action}
    ```

**b. `tool_executor_node`**:
-   **改造点**: 使用`UnifiedToolRegistry`的统一执行方法。
-   **代码实现**:
    ```python
    from src.tools.unified_registry import unified_registry

    async def tool_executor_node(state: StandardAgentState):
        action = state.get("current_action", {})
        tool_name = action.get("tool_name")
        parameters = action.get("parameters", {})
        
        try:
            # 直接委托给统一注册表执行
            result = await unified_registry.execute_action_tool(tool_name, **parameters)
            
            # 将结果存储到状态中
            tool_results = state.get("tool_results", {})
            tool_results[tool_name] = result
            
            return {"tool_results": tool_results}
            
        except Exception as e:
            # 错误处理
            return {
                "has_error": True,
                "error_message": f"Tool execution failed: {str(e)}"
            }
    ```

### 11.7 Context7 SDK集成

为了标准化外部API调用，我们将集成Context7 SDK：

**文件路径**: `src/tools/context7_client.py`
**实现方式**:
```python
from typing import Dict, Any, Optional
import httpx
from src.tools.unified_registry import unified_registry

class Context7Client:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.client = httpx.AsyncClient()

    @unified_registry.register_action_tool
    async def call_external_api(self, endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        调用外部API的标准化方法。
        
        Args:
            endpoint: API端点
            method: HTTP方法
            data: 请求数据
        """
        headers = {"Authorization": f"Bearer {self.api_key}"}
        
        response = await self.client.request(
            method=method,
            url=f"{self.base_url}/{endpoint}",
            json=data,
            headers=headers
        )
        
        return response.json()
```

### 11.8 结论

通过引入`UnifiedToolRegistry`和`Action/Planner Tools`的分类，我们为ICP架构建立了一个强大、统一且可扩展的Function Call体系。

-   **接口统一**: 所有`Action Tools`都通过同一套机制（`@register_action_tool`）定义，并通过同一个入口（`unified_registry.execute_action_tool`）执行。
-   **AI决策更可靠**: `planner_agent`总能拿到一份实时、准确的"工具说明书"，避免了因Prompt与实际工具能力不匹配而导致的"幻觉"。
-   **高可维护性**: 当需要新增或修改工具时，开发者只需在对应的Service文件中应用装饰器即可，无需修改任何Agent核心逻辑。
-   **现有代码兼容**: 通过Planner Tools机制，现有的format_tools得以保留并整合到新架构中。
-   **标准化状态管理**: `StandardAgentState`为所有工作流提供了统一的状态结构。

## 12. 与其他重构文档的集成规范 (Integration Specifications)

本章节定义了本文档与其他三个重构文档（意图整合.md、旅游搭子UI.md、推送.md）的接口规范和集成点。

### 12.1 与意图整合.md的集成

**集成点**: 两步意图分析与ICP迭代规划的融合
- **统一状态字段**: `framework_analysis`, `preference_analysis`, `consolidated_intent`
- **工作流整合**: 意图分析作为ICP的初始化阶段
- **Prompt管理**: 通过UnifiedToolRegistry的Planner Tools统一管理

### 12.2 与推送.md的集成

**集成点**: 事件驱动架构与LangGraph状态管理
- **统一事件格式**: 基于StandardAgentState的事件生成
- **Redis集成**: `notification_service`字段存储事件发布器实例
- **状态同步**: LangGraph状态与Redis HASH的双向同步

### 12.3 与旅游搭子UI.md的集成

**集成点**: 前端组件与后端状态的映射
- **状态映射**: StandardAgentState字段直接映射到前端组件
- **事件流**: 统一的SSE事件格式支持前端实时更新
- **组件化**: 前端组件与Action Tools的一一对应关系

## 13. 详细POI展示逻辑与数据格式 (Detailed POI Display Logic & Data Format)

为了极大地提升最终生成行程的用户体验，规划中的每个POI点都需要包含丰富、详细且格式化的信息。本章节将定义统一的POI数据模型、数据获取流程以及最终输出的格式规范。

### 13.1 目标 (Goal)

确保每个POI（景点、餐厅、酒店）在最终行程中都清晰地展示以下信息：
-   建议的访问时间
-   POI名称
-   一句话亮点介绍
-   联系电话（针对酒店和餐厅）
-   用户评分
-   展示图片

### 13.2 统一POI数据模型 (`EnrichedPOI`)

为了标准化数据结构，我们定义一个Pydantic模型来表示一个信息丰富的POI。这将作为`StandardAgentState`中存储POI的标准格式。

```python
from pydantic import BaseModel, Field
from typing import Optional, List

class EnrichedPOI(BaseModel):
    """
    一个信息丰富的POI数据模型，用于在行程中展示。
    """
    poi_id: str = Field(..., description="POI的唯一标识符, 来源: 高德API")
    name: str = Field(..., description="POI的名称, 来源: 高德API")
    poi_type: str = Field(..., description="POI类型: 'attraction', 'restaurant', 'hotel', 来源: 高德API")
    location: str = Field(..., description="经纬度坐标，格式为 'lng,lat', 来源: 高德API")
    
    # 新增的丰富信息字段
    introduction: str = Field(..., description="一句话亮点介绍，突出其核心特色, 来源: AI生成")
    suggested_time: str = Field(..., description="建议的游玩或用餐时间段，例如 '上午 9:00 - 12:00' 或 '午餐 12:00 - 13:30', 来源: AI生成")
    rating: Optional[float] = Field(None, description="用户评分 (范围1-5), 来源: 高德API")
    phone_number: Optional[str] = Field(None, description="联系电话 (主要针对酒店、餐厅), 来源: 高德API")
    image_urls: List[str] = Field(default_factory=list, description="POI的展示图片URL列表, 来源: 高德API")

```

### 13.3 数据来源与调用示例

POI的基础信息主要通过调用`MapTool`中的工具从高德API获取。以下示例展示了如何调用工具并映射数据。

#### 13.3.1 POI搜索原始输出示例

以下是调用`MapTool.search_pois`后，从高德API获取的真实原始数据。这些数据是后续所有丰富化处理的基础。

```python
# test_agent_tools_integration.py -> test_search_pois() output:
[
    POIResult(id='B000A83C1S', name='天安门广场', type='风景名胜;风景名胜;红色景区|风景名胜;公园广场;城市广场', address='东长安街', location=Location(latitude=39.903182, longitude=116.397755, name='天安门广场', address='东长安街'), distance=0, rating=4.9, price=None, photos=['http://aos-cdn-image.amap.com/sns/ugccomment/fab72424-5643-4c14-9486-ae1c8475eab7.jpg', 'http://aos-cdn-image.amap.com/sns/ugccomment/51705f12-6b79-4c6a-a0a6-462008c22043.jpg', 'http://store.is.autonavi.com/showpic/6c22735ae72cfab207adae0fa74b3a75'], image='http://aos-cdn-image.amap.com/sns/ugccomment/fab72424-5643-4c14-9486-ae1c8475eab7.jpg', phone='010-63095745', business_hours='', price_range=[]),
    POIResult(id='B000A60DA1', name='天安门', type='风景名胜;风景名胜;国家级景点', address='长安街北侧', location=Location(latitude=39.909187, longitude=116.397455, name='天安门', address='长安街北侧'), distance=0, rating=0, price=None, photos=['http://store.is.autonavi.com/showpic/17a36a737908810a310387c7d53e878a', 'http://store.is.autonavi.com/showpic/60f13ff4d6160ca43927f50b176ee12c', 'http://store.is.autonavi.com/showpic/65840d00b861c023afa24b753dee2790'], image='http://store.is.autonavi.com/showpic/17a36a737908810a310387c7d53e878a', phone='010-86409660', business_hours='', price_range=[]),
    POIResult(id='B000A8UIN8', name='故宫博物院', type='风景名胜;风景名胜;世界遗产|科教文化服务;博物馆;博物馆', address='景山前街4号', location=Location(latitude=39.917839, longitude=116.397029, name='故宫博物院', address='景山前街4号'), distance=0, rating=4.9, price=60.0, photos=['http://store.is.autonavi.com/showpic/2f968490d105bb2741e17f90b85c6b79', 'http://store.is.autonavi.com/showpic/7c09872350b4b3d857d62d09803cb7c7', 'http://store.is.autonavi.com/showpic/cd462908859f2bc924b7e7bf886e3606'], image='http://store.is.autonavi.com/showpic/2f968490d105bb2741e17f90b85c6b79', phone='4009501925', business_hours='', price_range='60.00'),
    POIResult(id='B000A8UR3U', name='前门大街', type='地名地址信息;地名地址信息;地名地址信息', address='东城区', location=Location(latitude=39.896152, longitude=116.403895, name='前门大街', address='东城区'), distance=0, rating=4.8, price=None, photos=['http://store.is.autonavi.com/showpic/34231e4fb695d9aebac2a72575212a97', 'http://store.is.autonavi.com/showpic/b17c8a39b9cafd2b8161853595904149', 'http://store.is.autonavi.com/showpic/108e5a05394bbb932f00d0af20b901ff'], image='http://store.is.autonavi.com/showpic/34231e4fb695d9aebac2a72575212a97', phone=[], business_hours='', price_range=[]),
    POIResult(id='B000A83U0P', name='中国国家博物馆', type=' 科教文化服务;博物馆;博物馆', address='东长安街16号天安门广场东侧', location=Location(latitude=39.905374, longitude=116.401304, name='中国国家博物馆', address='东长安街16号天安门广场东侧'), distance=0, rating=4.9, price=None, photos=['https://aos-comment.amap.com/B000A83U0P/headerImg/c18b1708f9b307ca92bf78809f7a0220_2048_2048_80.jpg', 'http://store.is.autonavi.com/showpic/620280e69fbdcc9681f8bf30f62fb9eb', 'http://store.is.autonavi.com/showpic/d44b1c52716bc8bb56489a3504846f34'], image='https://aos-comment.amap.com/B000A83U0P/headerImg/c18b1708f9b307ca92bf78809f7a0220_2048_2048_80.jpg', phone='010-65116400', business_hours='', price_range=[])
]
```
*（为简洁起见，部分字段和列表内容被截断）*


#### 13.3.2 数据映射示例

**调用方式**:
```python
from tools.Amap.map_tool import MapTool

# 1. 初始化工具
map_tool = MapTool()

# 2. 调用POI搜索工具
# 这个方法在 `test_agent_tools_integration.py` 中有详细测试
poi_results = map_tool.search_pois(
    keywords="历史文化景点",
    city="北京",
    page_size=5
)

# 3. 数据映射 (以故宫博物院为例)
# 假设 poi_results[2] 是故宫的数据 (见上文原始数据)
gugong_poi_result = poi_results[2]

# 映射到EnrichedPOI模型
# 注意: introduction 和 suggested_time 需要由AI后续生成
enriched_poi_data = {
    "poi_id": gugong_poi_result.id,  # 'B000A8UIN8'
    "name": gugong_poi_result.name, # '故宫博物院'
    "poi_type": gugong_poi_result.type, # '风景名胜;风景名胜;世界遗产|科教文化服务;博物馆;博物馆'
    "location": f"{gugong_poi_result.location.longitude},{gugong_poi_result.location.latitude}", # '116.397029,39.917839'
    "rating": float(gugong_poi_result.rating) if gugong_poi_result.rating else 0.0, # 4.9
    "phone_number": gugong_poi_result.phone, # '4009501925'
    "image_urls": gugong_poi_result.photos # ['http://store.is.autonavi.com/showpic/2f968490d105bb2741e17f90b85c6b79', ...]
}
```

### 13.4 规划流程中的数据获取

在ICP的“思考 -> 行动 -> 观察”循环中，`planner_agent`需要通过多次、有序的工具调用来逐步构建`EnrichedPOI`对象。

1.  **初步搜索**: Agent调用`search_pois`或`search_around`等工具，获取基础POI列表（包含`poi_id`, `name`, `location`, `poi_type`）。
2.  **详情获取**: 针对选中的POI，Agent必须调用`get_poi_details`工具，传入`poi_id`，以获取`rating`、`phone_number`等详细信息。
3.  **图片获取**: Agent调用`get_poi_images`工具，获取`image_urls`。
4.  **AI信息润色**: 这是关键一步。在获取所有基础数据后，`planner_agent`需要：
    -   根据POI类型、用户偏好和上下文，生成吸引人的`introduction`（一句话介绍）。
    -   根据行程的整体安排，分配合理的`suggested_time`。
    -   将所有信息组装成一个`EnrichedPOI`对象，并存入`daily_plans`状态中。

### 13.5 对`Action Tools`的要求

为了支持上述流程，`UnifiedToolRegistry`中必须包含以下（或功能等价的）`Action Tools`：

-   `search_poi(keywords: str, city: str, ...) -> List[BasicPOI]`: 返回基础POI列表，每个POI必须包含`poi_id`。
-   `get_poi_details(poi_id: str) -> DetailedPOIInfo`: 返回一个包含`rating`, `phone_number`, `address`等详细信息的结构。
-   `get_poi_images(poi_id: str) -> List[str]`: 返回一个包含图片URL的列表。

这些工具需要被正确地实现并使用`@unified_registry.register_action_tool`装饰器进行注册。

### 13.6 更新 `StandardAgentState`

`StandardAgentState`中的`daily_plans`字段需要更新，以明确其存储的是`EnrichedPOI`对象的列表。

**文件路径**: `src/agents/travel_planner_lg/state.py`
**更新后定义**:
```python
# (假设EnrichedPOI模型已在某处定义或直接使用字典)
class StandardAgentState(TypedDict):
    # ... 其他字段 ...
    
    # daily_plans现在存储的是结构化的、信息丰富的POI对象
    daily_plans: Dict[int, List[Dict[str, Any]]] # 实际应为 List[EnrichedPOI]
    
    # ... 其他字段 ...
```
*注意：在`TypedDict`中我们仍然使用`List[Dict[str, Any]]`，但在实际运行时，这些字典的结构应严格遵守`EnrichedPOI`模型。*

### 13.6 最终行程的Function Call格式

当`planner_agent`决定结束规划时（即发出`finish_planning`指令），它需要将`daily_plans`中的数据格式化为最终的行程。这个最终行程的JSON结构也需要标准化，以便前端或其他下游服务消费。

我们可以定义一个`Planner Tool`来生成这个最终行程的Schema，`planner_agent`在最后一步调用这个工具来确保输出格式的正确性。

**示例Planner Tool (`src/tools/travel_planner/consolidated_tools.py`)**:
```python
@unified_registry.register_planner_tool
def define_final_itinerary_schema() -> Dict[str, Any]:
    """
    定义最终输出的、完整的旅行计划的JSON Schema。
    """
    enriched_poi_schema = {
        "type": "object",
        "properties": {
            "name": {"type": "string", "description": "POI名称"},
            "introduction": {"type": "string", "description": "一句话亮点介绍"},
            "suggested_time": {"type": "string", "description": "建议访问时间"},
            "rating": {"type": "number", "description": "评分"},
            "phone_number": {"type": "string", "description": "联系电话"},
            "image_urls": {
                "type": "array",
                "items": {"type": "string", "format": "uri"},
                "description": "图片URL列表"
            },
            "location": {"type": "string", "description": "经纬度"},
            "poi_type": {"type": "string", "description": "POI类型"}
        },
        "required": ["name", "introduction", "suggested_time", "location", "poi_type"]
    }

    daily_plan_schema = {
        "type": "object",
        "properties": {
            "day": {"type": "integer", "description": "天数（第几天）"},
            "theme": {"type": "string", "description": "当天的主题或总结"},
            "activities": {
                "type": "array",
                "items": enriched_poi_schema
            }
        },
        "required": ["day", "theme", "activities"]
    }

    return {
        "type": "function",
        "function": {
            "name": "submit_travel_plan",
            "description": "提交最终的旅行规划方案",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {"type": "string", "description": "目标城市"},
                    "total_days": {"type": "integer", "description": "总天数"},
                    "daily_plans": {
                        "type": "array",
                        "items": daily_plan_schema
                    }
                },
                "required": ["city", "total_days", "daily_plans"]
            }
        }
    }
```
`planner_agent`的最终Prompt需要指导它在完成所有规划后，调用`submit_travel_plan`这个Function Call，并按照上述Schema填充所有详细信息。

## 14. 代码实现映射 (Code Implementation Mapping)

本章节旨在将文档中定义的数据模型和工具与项目中的实际代码进行映射。

### 14.1 `EnrichedPOI` 数据获取

文档中 `EnrichedPOI` 模型的数据需要通过多个工具协作获取，其代码实现分布如下：

1.  **基础信息获取 (`poi_id`, `name`, `location`, etc.)**
    *   **文件路径**: `src/tools/travel_planner/amap_poi_tools.py`
    *   **对应函数**: `search_poi`
    *   **现状分析**:
        *   该函数已实现，能够根据关键词返回一个包含基础信息的POI列表，可作为数据获取的第一步。
        *   **需要注意**: 该函数返回的 `rating` 和 `phone` 可能不完整，且 **不包含 `image_urls`**。

2.  **详细信息获取 (`rating`, `phone_number`)**
    *   **文件路径**: `src/tools/travel_planner/amap_poi_tools.py` (建议)
    *   **对应函数**: `get_poi_details(poi_id: str)`
    *   **现状分析**:
        *   **待补充**: 该函数在文档中被要求，但在当前代码中 **缺失**。需要在此文件中补充实现，通过 `poi_id` 查询更详细的POI信息，以获取更准确的评分和电话。

3.  **图片信息获取 (`image_urls`)**
    *   **文件路径**: `src/tools/travel_planner/amap_poi_tools.py` (建议)
    *   **对应函数**: `get_poi_images(poi_id: str)`
    *   **现状分析**:
        *   **待补充**: 该函数在文档中被要求，但在当前代码中 **缺失**。需要补充实现以获取POI的图片列表。

4.  **AI生成信息 (`introduction`, `suggested_time`)**
    *   **实现方式**: 在 `planner_agent_node` 中通过LLM生成。
    *   **现状分析**:
        *   这部分属于AI推理逻辑，不体现为具体的工具函数，文档中的描述是正确的。`planner_agent` 在获取到所有API数据后，需要调用LLM来完成这一步的润色和生成。

### 14.2 最终行程格式定义

1.  **功能**: 定义最终输出的、完整的旅行计划的JSON Schema。
2.  **文件路径**: `src/tools/travel_planner/consolidated_tools.py` (建议)
3.  **对应函数**: `define_final_itinerary_schema()`
4.  **现状分析**:
    *   **待补充**: 该`Planner Tool`在文档13.6节中有详细定义，但在 `consolidated_tools.py` 中 **缺失**。需要按照文档中的定义，在此文件中补充该函数的实现，并使用 `@unified_registry.register_planner_tool` 进行注册。
