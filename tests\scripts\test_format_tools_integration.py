"""
测试原子化Function Call工具集成和TravelPlannerAgent修复效果

验证：
1. 工具注册表正常工作
2. Format Tool能正确生成Schema
3. TravelPlannerAgent能正确使用工具并输出结构化数据
4. 数据传递问题得到修复
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.tools import tool_registry, register_tool
from src.tools.travel_planner.format_tools import (
    define_intent_extraction_schema,
    get_formatted_tools_for_phase1_and_2
)
from src.tools.travel_planner.format_tools_phase3 import (
    define_poi_scoring_models,
    define_final_itinerary_schema,
    format_llm_prompt_with_schema
)
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import TravelPlanRequest


def test_tool_registry():
    """测试工具注册表功能"""
    print("[测试] 工具注册表功能")
    
    # 检查工具是否正确注册
    available_tools = tool_registry.list_tools()
    print(f"已注册工具数量: {len(available_tools)}")
    print(f"工具列表: {available_tools}")
    
    # 测试关键格式化工具
    required_tools = [
        "extract_travel_intent",
        "define_layer1_parallel_calls", 
        "define_final_itinerary_schema",
        "format_llm_prompt_with_schema"
    ]
    
    missing_tools = []
    for tool_name in required_tools:
        if tool_name not in available_tools:
            missing_tools.append(tool_name)
    
    if missing_tools:
        print(f"[ERROR] 缺少必需工具: {missing_tools}")
        return False
    else:
        print("[OK] 所有必需工具已注册")
        return True


def test_format_tools():
    """测试格式化工具生成Schema"""
    print("\n[测试] 格式化工具Schema生成")
    
    try:
        # 测试意图提取Schema
        intent_schema = extract_travel_intent("测试查询", "2024-01-01")
        print(f"[OK] 意图提取Schema长度: {len(intent_schema)}")
        
        # 验证是否为有效JSON
        parsed_schema = json.loads(intent_schema)
        required_props = ["destination", "days", "transport_mode"]
        for prop in required_props:
            if prop not in parsed_schema.get("properties", {}):
                print(f"[ERROR] Schema缺少属性: {prop}")
                return False
        
        print("[OK] 意图提取Schema验证通过")
        
        # 测试最终行程Schema
        final_schema = define_final_itinerary_schema()
        parsed_final = json.loads(final_schema)
        if "daily_plans" not in parsed_final.get("properties", {}):
            print("[ERROR] 最终行程Schema缺少daily_plans")
            return False
            
        print("[OK] 最终行程Schema验证通过")
        return True
        
    except Exception as e:
        print(f"[ERROR] 格式化工具测试失败: {str(e)}")
        return False


def test_prompt_formatting():
    """测试提示词格式化功能"""
    print("\n[测试] 提示词格式化功能")
    
    try:
        base_prompt = "请分析用户查询并提取信息"
        formatted_prompt = format_llm_prompt_with_schema(
            base_prompt=base_prompt,
            schema_tool_name="extract_travel_intent",
            context={"user_query": "我要去上海玩3天", "current_datetime": "2024-01-01"}
        )
        
        # 检查格式化结果
        if "JSON Schema" not in formatted_prompt:
            print("[ERROR] 格式化提示词缺少Schema部分")
            return False
            
        if len(formatted_prompt) <= len(base_prompt):
            print("[ERROR] 格式化后提示词长度异常")
            return False
            
        print(f"[OK] 提示词格式化成功，长度: {len(formatted_prompt)}")
        return True
        
    except Exception as e:
        print(f"[ERROR] 提示词格式化失败: {str(e)}")
        return False


async def test_travel_planner_integration():
    """测试TravelPlannerAgent集成效果"""
    print("\n[测试] TravelPlannerAgent集成测试")
    
    try:
        # 创建测试请求
        test_request = TravelPlanRequest(
            user_id="test_user_001",
            query="我在上海人民广场，周末要去萧山玩两天"
        )
        
        # 创建Agent实例
        agent = TravelPlannerAgent()
        
        # 执行意图理解步骤
        print("[INFO] 开始测试意图理解步骤...")
        
        # 模拟执行意图理解
        from src.models.travel_planner import AgentState
        test_state = AgentState(
            trace_id="test_trace_001",
            user_id=test_request.user_id,
            original_query=test_request.query,
            current_step="intent_understanding"
        )
        
        events = []
        async for event in agent._understand_intent(test_state):
            events.append(event)
            print(f"[EVENT] {event.event_type}: {event.payload}")
        
        # 验证结果
        if not test_state.extracted_entities:
            print("[ERROR] 意图理解未提取到实体")
            return False
            
        extracted = test_state.extracted_entities
        print(f"[INFO] 提取的实体: {extracted}")
        
        # 验证关键字段
        if "destination" not in extracted:
            print("[ERROR] 缺少destination字段")
            return False
            
        if "days" not in extracted or not isinstance(extracted["days"], int):
            print("[ERROR] days字段类型错误")
            return False
            
        print("[OK] 意图理解集成测试通过")
        
        # 测试最终行程生成
        print("[INFO] 测试最终行程生成...")
        
        # 设置一些模拟数据
        test_state.tool_results["weather_forecast"] = []
        test_state.tool_results["daily_plans"] = []
        
        final_events = []
        async for event in agent._generate_final_itinerary(test_state):
            final_events.append(event)
            print(f"[EVENT] {event.event_type}")
        
        # 验证最终行程
        if not test_state.final_itinerary:
            print("[ERROR] 未生成最终行程")
            return False
            
        final_itinerary = test_state.final_itinerary
        print(f"[INFO] 最终行程标题: {final_itinerary.summary.title}")
        print(f"[INFO] 行程天数: {final_itinerary.summary.days}")
        print(f"[INFO] 目的地: {final_itinerary.summary.destination_city}")
        print(f"[INFO] 每日计划数量: {len(final_itinerary.daily_plans)}")
        
        # 验证数据完整性
        if not final_itinerary.summary.title:
            print("[ERROR] 行程标题为空")
            return False
            
        if not final_itinerary.summary.destination_city:
            print("[ERROR] 目的地城市为空")
            return False
            
        if len(final_itinerary.daily_plans) != final_itinerary.summary.days:
            print("[ERROR] 每日计划数量与天数不符")
            return False
            
        print("[OK] 最终行程生成集成测试通过")
        return True
        
    except Exception as e:
        print(f"[ERROR] TravelPlannerAgent集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def run_all_tests():
    """运行所有测试"""
    print("开始原子化Function Call工具集成测试")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 工具注册表测试
    results.append(("工具注册表", test_tool_registry()))
    
    # 2. 格式化工具测试
    results.append(("格式化工具", test_format_tools()))
    
    # 3. 提示词格式化测试
    results.append(("提示词格式化", test_prompt_formatting()))
    
    # 4. Agent集成测试
    results.append(("Agent集成", await test_travel_planner_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    all_passed = True
    for test_name, passed in results:
        status = "[通过]" if passed else "[失败]"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("[成功] 所有测试通过！原子化Function Call工具集成成功")
        print("\n修复效果总结:")
        print("✅ 工具注册表正常工作")
        print("✅ Schema格式定义正确")
        print("✅ LLM输出格式可控")
        print("✅ 数据传递问题已修复")
        print("✅ 最终行程结构完整")
    else:
        print("[失败] 部分测试未通过，需要进一步调试")
    
    return all_passed


if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(run_all_tests())
    sys.exit(0 if result else 1) 