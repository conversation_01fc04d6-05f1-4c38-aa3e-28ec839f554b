"""
数据库模型定义

定义MongoDB和MySQL数据库的模型结构，用于存储用户信息、行程数据、记忆等。
"""
from datetime import datetime
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema
from bson import ObjectId
from enum import Enum


class PyObjectId(ObjectId):
    """自定义ObjectId类型，用于Pydantic模型"""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
        
    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)
        
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler
    ) -> core_schema.CoreSchema:
        """Pydantic v2 核心模式定义"""
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate)
                ])
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            ),
        )
    
    @classmethod
    def __get_pydantic_json_schema__(
        cls, core_schema: core_schema.CoreSchema, handler
    ) -> JsonSchemaValue:
        """Pydantic v2 JSON模式定义"""
        return {"type": "string"}


class MongoBaseModel(BaseModel):
    """MongoDB基础模型"""
    
    model_config = {
        # 允许使用ObjectId
        "arbitrary_types_allowed": True,
        # JSON编码器
        "json_encoders": {ObjectId: str}
    }


class UserDocument(MongoBaseModel):
    """用户文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: str = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="用户偏好")
    tags: List[str] = Field(default_factory=list, description="用户标签")
    budget_preference: Optional[str] = Field(None, description="预算偏好")
    travel_style: Optional[str] = Field(None, description="旅行风格")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class ItineraryDocument(MongoBaseModel):
    """行程文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    trace_id: str = Field(..., description="追踪ID")
    user_id: str = Field(..., description="用户ID")
    title: str = Field(..., description="行程标题")
    destination_city: str = Field(..., description="目的地城市")
    days: int = Field(..., description="天数")
    status: str = Field(default="draft", description="状态：draft, completed, cancelled")
    raw_user_query: str = Field(..., description="原始用户查询")
    summary: Dict[str, Any] = Field(..., description="行程摘要")
    weather_forecast: List[Dict[str, Any]] = Field(default_factory=list, description="天气预报")
    map_info: Optional[Dict[str, Any]] = Field(None, description="地图信息")
    daily_plans: List[Dict[str, Any]] = Field(default_factory=list, description="每日计划")
    budget_estimation: Optional[Dict[str, Any]] = Field(None, description="预算估算")
    tags: List[str] = Field(default_factory=list, description="标签")
    cover_image_url: Optional[str] = Field(None, description="封面图片URL")
    is_public: bool = Field(default=False, description="是否公开")
    likes_count: int = Field(default=0, description="点赞数")
    views_count: int = Field(default=0, description="浏览数")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class MemoryDocument(MongoBaseModel):
    """记忆文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: str = Field(..., description="用户ID")
    memory_type: str = Field(..., description="记忆类型：preference, experience, feedback")
    content: str = Field(..., description="记忆内容")
    context: Dict[str, Any] = Field(default_factory=dict, description="上下文信息")
    importance_score: float = Field(default=1.0, description="重要性评分")
    tags: List[str] = Field(default_factory=list, description="标签")
    related_itinerary_id: Optional[str] = Field(None, description="关联行程ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    accessed_at: datetime = Field(default_factory=datetime.now, description="最后访问时间")
    access_count: int = Field(default=0, description="访问次数")


class ConversationDocument(MongoBaseModel):
    """对话文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    session_id: str = Field(..., description="会话ID")
    user_id: str = Field(..., description="用户ID")
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="消息列表")
    context: Dict[str, Any] = Field(default_factory=dict, description="对话上下文")
    status: str = Field(default="active", description="状态：active, completed, abandoned")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class POIDocument(MongoBaseModel):
    """POI文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    poi_id: str = Field(..., description="高德POI ID")
    name: str = Field(..., description="POI名称")
    category: str = Field(..., description="POI类别")
    city: str = Field(..., description="所在城市")
    address: str = Field(..., description="地址")
    location: Dict[str, float] = Field(..., description="地理位置")
    rating: Optional[float] = Field(None, description="评分")
    price_level: Optional[int] = Field(None, description="价格等级")
    opening_hours: Optional[str] = Field(None, description="营业时间")
    phone: Optional[str] = Field(None, description="电话")
    website: Optional[str] = Field(None, description="网站")
    images: List[str] = Field(default_factory=list, description="图片URL列表")
    description: Optional[str] = Field(None, description="描述")
    tips: Optional[str] = Field(None, description="小贴士")
    features: List[str] = Field(default_factory=list, description="特色标签")
    estimated_duration_min: Optional[int] = Field(None, description="预计游玩时长")
    best_visit_time: Optional[str] = Field(None, description="最佳游览时间")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class FeedbackDocument(MongoBaseModel):
    """反馈文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: str = Field(..., description="用户ID")
    itinerary_id: str = Field(..., description="行程ID")
    feedback_type: str = Field(..., description="反馈类型：rating, comment, suggestion")
    rating: Optional[int] = Field(None, description="评分 1-5")
    comment: Optional[str] = Field(None, description="评论内容")
    aspects: Dict[str, Any] = Field(default_factory=dict, description="各方面评价")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class AnalyticsDocument(MongoBaseModel):
    """分析数据文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    event_type: str = Field(..., description="事件类型")
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    trace_id: Optional[str] = Field(None, description="追踪ID")
    properties: Dict[str, Any] = Field(default_factory=dict, description="事件属性")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class InteractionStatus(str, Enum):
    """AI交互日志状态"""
    PROCESSING = "PROCESSING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class BusinessStepStatus(str, Enum):
    """业务步骤状态"""
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class BusinessStepLog(MongoBaseModel):
    """业务步骤日志模型"""
    step_name: str = Field(..., description="步骤名称")
    status: BusinessStepStatus = Field(..., description="状态")
    summary: Optional[str] = Field(None, description="摘要")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")

class RawModelTrace(MongoBaseModel):
    """模型原始痕迹模型"""
    content: str = Field(..., description="内容")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")

class AIInteractionLog(MongoBaseModel):
    """AI交互日志文档模型"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    interaction_id: str = Field(..., description="全局唯一的交互ID")
    user_id: str = Field(..., description="用户ID")
    application_source: Optional[str] = Field(None, description="来源App")
    status: InteractionStatus = Field(..., description="任务的最终状态")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="任务开始时间")
    business_steps_log: List[BusinessStepLog] = Field(default_factory=list, description="业务步骤日志")
    raw_model_trace: List[RawModelTrace] = Field(default_factory=list, description="模型原始痕迹")
    final_output: Optional[Dict[str, Any]] = Field(None, description="AI生成的最终完整结果")


# 数据库集合名称常量
class Collections:
    """数据库集合名称"""
    USERS = "users"
    ITINERARIES = "itineraries"
    MEMORIES = "memories"
    CONVERSATIONS = "conversations"
    POIS = "pois"
    FEEDBACK = "feedback"
    ANALYTICS = "analytics"
    AI_INTERACTION_LOGS = "ai_interaction_logs"


# 索引定义
INDEXES = {
    Collections.USERS: [
        ("user_id", 1),
        ("email", 1),
        ("phone", 1),
    ],
    Collections.ITINERARIES: [
        ("user_id", 1),
        ("trace_id", 1),
        ("destination_city", 1),
        ("status", 1),
        ("created_at", -1),
        ("is_public", 1),
    ],
    Collections.MEMORIES: [
        ("user_id", 1),
        ("memory_type", 1),
        ("importance_score", -1),
        ("created_at", -1),
        ("tags", 1),
    ],
    Collections.CONVERSATIONS: [
        ("session_id", 1),
        ("user_id", 1),
        ("status", 1),
        ("created_at", -1),
    ],
    Collections.POIS: [
        ("poi_id", 1),
        ("city", 1),
        ("category", 1),
        ("rating", -1),
        ("location", "2dsphere"),  # 地理位置索引
    ],
    Collections.FEEDBACK: [
        ("user_id", 1),
        ("itinerary_id", 1),
        ("feedback_type", 1),
        ("created_at", -1),
    ],
    Collections.ANALYTICS: [
        ("event_type", 1),
        ("user_id", 1),
        ("timestamp", -1),
    ],
    Collections.AI_INTERACTION_LOGS: [
        ("interaction_id", 1),
        ("user_id", 1),
        ("application_source", 1),
        ("status", 1),
        ("timestamp", -1),
    ],
}


# 数据验证规则
VALIDATION_RULES = {
    Collections.USERS: {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["user_id"],
            "properties": {
                "user_id": {"bsonType": "string"},
                "email": {"bsonType": ["string", "null"]},
                "phone": {"bsonType": ["string", "null"]},
                "preferences": {"bsonType": "object"},
                "tags": {"bsonType": "array"},
                "created_at": {"bsonType": "date"},
                "updated_at": {"bsonType": "date"}
            }
        }
    },
    Collections.ITINERARIES: {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["trace_id", "user_id", "title", "destination_city", "days"],
            "properties": {
                "trace_id": {"bsonType": "string"},
                "user_id": {"bsonType": "string"},
                "title": {"bsonType": "string"},
                "destination_city": {"bsonType": "string"},
                "days": {"bsonType": "int", "minimum": 1, "maximum": 30},
                "status": {"enum": ["draft", "completed", "cancelled"]},
                "is_public": {"bsonType": "bool"},
                "likes_count": {"bsonType": "int", "minimum": 0},
                "views_count": {"bsonType": "int", "minimum": 0},
                "created_at": {"bsonType": "date"},
                "updated_at": {"bsonType": "date"}
            }
        }
    },
    Collections.AI_INTERACTION_LOGS: {
        "$jsonSchema": {
            "bsonType": "object",
            "required": ["interaction_id", "user_id", "status", "timestamp"],
            "properties": {
                "interaction_id": {"bsonType": "string"},
                "user_id": {"bsonType": "string"},
                "application_source": {"bsonType": ["string", "null"]},
                "status": {"enum": ["PROCESSING", "SUCCESS", "FAILED"]},
                "timestamp": {"bsonType": "date"},
            }
        }
    }
}
