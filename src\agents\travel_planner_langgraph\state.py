"""
TravelPlannerAgent LangGraph状态定义

定义了旅行规划Agent的完整状态结构，支持双模运行和状态快照。
"""

from typing import Dict, List, Any, Optional, Union, TypedDict, Annotated
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from langgraph.graph import add_messages


class PlanningMode(Enum):
    """规划模式枚举"""
    RANGE_AWARE = "range_aware"  # 精准续航规划模式
    GENERAL_ASSISTANCE = "general_assistance"  # 通用驾驶辅助模式


class ProcessingStage(Enum):
    """处理阶段枚举"""
    INTENT_ANALYSIS = "intent_analysis"
    MULTI_CITY_STRATEGY = "multi_city_strategy"
    DRIVING_CONTEXT = "driving_context"
    PREFERENCE_ANALYSIS = "preference_analysis"
    ITINERARY_GENERATION = "itinerary_generation"
    OPTIMIZATION = "optimization"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class UserProfile:
    """用户画像数据结构"""
    user_id: str
    age_group: Optional[str] = None
    travel_style: Optional[str] = None
    budget_level: Optional[str] = None
    interests: List[str] = field(default_factory=list)
    dietary_restrictions: List[str] = field(default_factory=list)
    accessibility_needs: List[str] = field(default_factory=list)
    preferred_languages: List[str] = field(default_factory=list)


@dataclass
class VehicleInfo:
    """车辆信息数据结构"""
    vehicle_type: str  # "electric", "fuel", "hybrid"
    brand: Optional[str] = None
    model: Optional[str] = None
    nominal_range_km: Optional[int] = None
    charge_type: Optional[str] = None  # "fast", "slow", "both"
    vehicle_size: Optional[str] = None  # "compact", "mid", "large", "suv"


@dataclass
class CoreIntent:
    """核心意图分析结果"""
    destinations: List[str]
    days: int
    travel_dates: Dict[str, Any]
    transportation: Dict[str, Any]
    travel_theme: str
    budget: Dict[str, Any]
    travelers: Dict[str, Any]
    preferences: Dict[str, Any]
    extracted_keywords: List[str]
    confidence_score: float
    missing_info: List[str]


@dataclass
class MultiCityStrategy:
    """多城市策略分析结果"""
    strategy_type: str
    recommended_order: List[str]
    time_allocation: List[Dict[str, Any]]
    transportation_plan: List[Dict[str, Any]]
    accommodation_strategy: Dict[str, Any]
    highlights: Dict[str, Any]
    total_travel_time: str
    estimated_transport_cost: str
    flexibility_score: int
    recommendation_confidence: float


@dataclass
class DrivingContext:
    """驾驶情境分析结果"""
    driving_strategy: str
    vehicle_analysis: Dict[str, Any]
    range_planning: Dict[str, Any]
    driving_constraints: Dict[str, Any]
    charging_strategy: Dict[str, Any]
    route_preferences: Dict[str, Any]
    user_guidance: Dict[str, Any]
    confidence_score: float
    requires_clarification: List[str]


@dataclass
class PreferenceProfile:
    """偏好画像数据结构"""
    attraction_preferences: Dict[str, Any]
    food_preferences: Dict[str, Any]
    accommodation_preferences: Dict[str, Any]
    activity_preferences: Dict[str, Any]
    confidence_score: float


@dataclass
class ItineraryItem:
    """行程项目数据结构"""
    day: int
    time_slot: str
    type: str  # "attraction", "restaurant", "hotel", "transport", "activity"
    name: str
    location: Dict[str, Any]
    duration_minutes: int
    description: str
    rating: Optional[float] = None
    price_range: Optional[str] = None
    booking_info: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None


@dataclass
class DailyItinerary:
    """每日行程数据结构"""
    day: int
    date: Optional[str] = None
    city: str = ""
    theme: str = ""
    items: List[ItineraryItem] = field(default_factory=list)
    total_duration_minutes: int = 0
    estimated_cost: Optional[float] = None
    driving_distance_km: Optional[float] = None
    driving_time_minutes: Optional[int] = None
    notes: Optional[str] = None


class TravelPlanState(TypedDict):
    """
    TravelPlannerAgent的完整状态定义
    
    这个状态结构支持：
    1. 双模运行（精准续航规划 vs 通用驾驶辅助）
    2. 状态快照和恢复
    3. SSE事件流转换
    4. 完整的规划流程追踪
    """
    
    # === 基础信息 ===
    session_id: str
    user_id: str
    original_query: str
    created_at: str
    updated_at: str
    
    # === 处理状态 ===
    current_stage: str  # ProcessingStage的值
    planning_mode: str  # PlanningMode的值
    is_completed: bool
    has_error: bool
    error_message: Optional[str]
    
    # === 用户信息 ===
    user_profile: Optional[Dict[str, Any]]
    user_memories: Optional[List[Dict[str, Any]]]
    vehicle_info: Optional[Dict[str, Any]]
    
    # === 分析结果 ===
    core_intent: Optional[Dict[str, Any]]
    multi_city_strategy: Optional[Dict[str, Any]]
    driving_context: Optional[Dict[str, Any]]
    preference_profile: Optional[Dict[str, Any]]
    
    # === 规划结果 ===
    daily_itineraries: List[Dict[str, Any]]
    summary: Optional[Dict[str, Any]]
    recommendations: List[Dict[str, Any]]
    
    # === 消息历史 ===
    messages: Annotated[List[Dict[str, str]], add_messages]
    
    # === 工具调用记录 ===
    tool_calls: List[Dict[str, Any]]
    api_calls: List[Dict[str, Any]]
    
    # === 元数据 ===
    processing_time_seconds: float
    tokens_used: int
    cost_estimate: float
    
    # === SSE事件流支持 ===
    events: List[Dict[str, Any]]  # 用于SSE事件流的事件列表
    last_event_id: int


def create_initial_state(
    session_id: str,
    user_id: str,
    original_query: str,
    user_profile: Optional[UserProfile] = None,
    vehicle_info: Optional[VehicleInfo] = None
) -> TravelPlanState:
    """
    创建初始状态
    
    Args:
        session_id: 会话ID
        user_id: 用户ID
        original_query: 原始查询
        user_profile: 用户画像
        vehicle_info: 车辆信息
        
    Returns:
        初始化的TravelPlanState
    """
    now = datetime.now().isoformat()
    
    return TravelPlanState(
        # 基础信息
        session_id=session_id,
        user_id=user_id,
        original_query=original_query,
        created_at=now,
        updated_at=now,
        
        # 处理状态
        current_stage=ProcessingStage.INTENT_ANALYSIS.value,
        planning_mode=PlanningMode.GENERAL_ASSISTANCE.value,
        is_completed=False,
        has_error=False,
        error_message=None,
        
        # 用户信息
        user_profile=user_profile if isinstance(user_profile, dict) else (user_profile.__dict__ if user_profile else None),
        user_memories=None,
        vehicle_info=vehicle_info if isinstance(vehicle_info, dict) else (vehicle_info.__dict__ if vehicle_info else None),
        
        # 分析结果
        core_intent=None,
        multi_city_strategy=None,
        driving_context=None,
        preference_profile=None,
        
        # 规划结果
        daily_itineraries=[],
        summary=None,
        recommendations=[],
        
        # 消息历史
        messages=[],
        
        # 工具调用记录
        tool_calls=[],
        api_calls=[],
        
        # 元数据
        processing_time_seconds=0.0,
        tokens_used=0,
        cost_estimate=0.0,
        
        # SSE事件流支持
        events=[],
        last_event_id=0
    )


def update_state_stage(state: TravelPlanState, new_stage: ProcessingStage) -> TravelPlanState:
    """更新状态阶段"""
    state["current_stage"] = new_stage.value
    state["updated_at"] = datetime.now().isoformat()
    return state


def add_event_to_state(state: TravelPlanState, event_type: str, data: Dict[str, Any]) -> TravelPlanState:
    """向状态添加SSE事件"""
    event = {
        "id": state["last_event_id"] + 1,
        "type": event_type,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    state["events"].append(event)
    state["last_event_id"] = event["id"]
    return state
