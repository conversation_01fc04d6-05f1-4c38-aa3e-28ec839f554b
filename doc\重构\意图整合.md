# 意图分析流程重构方案：六步整合为两步 (V3.0 - 统一架构版)

## 1. 概述 (Overview)

当前旅行规划中的意图分析流程被拆分成了六个独立的步骤，每个步骤都需要与LLM进行一次交互。这种设计虽然保证了每个任务的专注和高可靠性，但也带来了明显的性能瓶颈和高昂的成本。为了优化用户体验并降低运营开销，我们提出将这六步整合为"两步走"的重构方案，并与统一的UnifiedToolRegistry和StandardAgentState深度集成。

-   **当前问题 (Problem)**: 串行调用LLM六次，导致总耗时长、成本高。
-   **解决方案 (Solution)**: 将逻辑上关联的分析任务合并，把六次LLM调用减少为两次，并同步优化下游的数据消费链路。
-   **架构集成**: 与ICP迭代式规划架构无缝融合，作为其初始化阶段。

## 2. 方案设计 (Solution Design)

我们将采用"先定框架，再填细节"的思路，将分析流程重构为两大步，并与StandardAgentState的字段结构保持一致。

### 2.1. 两步整合策略 (Two-Step Consolidation Strategy)

1.  **第一步：核心框架分析 (Core Framework Analysis)**
    -   **目的**: 搭建旅行的"骨架"，快速确定行程的核心要素（去哪儿、多久、怎么去）。
    -   **整合内容**:
        -   `01_core_intent_analyzer.md` (核心意图)
        -   `01a_multi_city_strategy_analyzer.md` (多城市策略)
        -   `01b_driving_context_analyzer.md` (自驾情境)
    -   **状态字段**: 对应StandardAgentState中的`framework_analysis`

2.  **第二步：个性化偏好分析 (Personalized Preference Analysis)**
    -   **目的**: 为旅行框架填充"血肉"，深入理解用户的个性化偏好（玩什么、吃什么、住哪里）。
    -   **整合内容**:
        -   `02_attraction_preference_analyzer.md` (景点偏好)
        -   `03_food_preference_analyzer.md` (美食偏好)
        -   `04_accommodation_preference_analyzer.md` (住宿偏好)
    -   **上下文依赖**: 此步骤将使用第一步的输出结果作为分析上下文。
    -   **状态字段**: 对应StandardAgentState中的`preference_analysis`

### 2.2. 新Prompt设计 (New Prompt Design)

为支持新的两步策略，我们将创建两个新的整合式Prompt文件，并通过UnifiedToolRegistry的Planner Tools进行管理。

-   **新Prompt存放位置**: `src/prompts/travel_planner/consolidated/`

#### 2.2.1. `01_framework_analyzer.md`

-   **目的**: 单一Prompt完成对用户核心意图、多城市策略和自驾情境的综合分析。
-   **输入**: 原始用户查询、用户画像（从user_profile_service获取）。
-   **输出 (JSON结构)**:
    ```json
    {
      "core_intent": {
        "destinations": ["北京"],
        "travel_days": 3,
        "travel_theme": ["亲子", "文化"],
        "budget_range": "中等",
        "group_size": 4,
        "departure_city": "上海"
      },
      "multi_city_strategy": {
        "is_multi_city": false,
        "city_priority": ["北京"],
        "transportation_between_cities": "高铁",
        "time_allocation": {"北京": 3}
      },
      "driving_context": {
        "has_driving_needs": true,
        "driving_scenarios": ["城市内观光", "景点间通勤"],
        "vehicle_requirements": "家庭轿车",
        "parking_considerations": ["景点停车", "酒店停车"]
      }
    }
    ```

#### 2.2.2. `02_preference_analyzer.md`

-   **目的**: 基于已确定的旅行框架，一次性完成对用户景点、美食、住宿偏好的综合分析。
-   **输入**: 原始用户查询、用户画像、以及第一步输出的`核心框架`。
-   **输出 (JSON结构)**:
    ```json
    {
      "attraction_preferences": {
        "preferred_types": ["历史文化", "亲子娱乐"],
        "must_visit": ["故宫", "天安门"],
        "avoid_types": ["极限运动"],
        "accessibility_needs": ["儿童友好", "无障碍通道"],
        "time_preferences": {"morning": "户外景点", "afternoon": "室内景点"}
      },
      "food_preferences": {
        "cuisine_types": ["北京菜", "川菜"],
        "dietary_restrictions": ["不吃辣"],
        "budget_per_meal": "100-200元",
        "dining_scenarios": ["家庭聚餐", "特色小吃"],
        "meal_timing": {"breakfast": "酒店", "lunch": "景点附近", "dinner": "市区"}
      },
      "accommodation_preferences": {
        "hotel_level": "四星级",
        "location_priority": ["交通便利", "景点附近"],
        "room_requirements": ["家庭房", "双床房"],
        "amenities_needed": ["停车场", "早餐", "游泳池"],
        "budget_per_night": "400-600元"
      }
    }
    ```

## 3. 代码整合总览 (Code Integration Overview)

此方案将主要修改基于`LangGraph`的新版Agent，涉及`UnifiedToolRegistry`、`StandardAgentState`和事件驱动架构的深度集成。

-   **核心思路**:
    1.  用两个新的分析阶段替换旧的六个。
    2.  增加一个**工具节点**，负责将两步分析的原始结果"压缩"并适配成下游规划节点所需的统一上下文 (`consolidated_intent`)。
    3.  更新`LangGraph`的计算图，形成一个清晰的、三段式的分析流程。
    4.  与UnifiedEventBus集成，实现实时的阶段进度推送。

## 4. 工具层优化 (Tools Optimization)

我们将通过UnifiedToolRegistry的Planner Tools机制来管理分析结果的整合，实现逻辑解耦。

### 4.1. 新建Planner Tool

-   **注册位置**: 通过`unified_registry.register_planner_tool`装饰器注册
-   **文件路径**: `src/tools/travel_planner/consolidated_tools.py`

### 4.2. 核心Planner Tool：`create_consolidated_intent`

```python
# src/tools/travel_planner/consolidated_tools.py
from src.tools.unified_registry import unified_registry
from typing import Dict, Any

@unified_registry.register_planner_tool
def create_consolidated_intent(framework_analysis: Dict[str, Any], preference_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    将两步分析结果整合为统一的意图上下文。
    
    Args:
        framework_analysis: 核心框架分析结果
        preference_analysis: 偏好分析结果
    
    Returns:
        consolidated_intent: 整合后的统一意图
    """
    # 提取核心信息
    core_intent = framework_analysis.get("core_intent", {})
    multi_city = framework_analysis.get("multi_city_strategy", {})
    driving = framework_analysis.get("driving_context", {})
    
    attractions = preference_analysis.get("attraction_preferences", {})
    food = preference_analysis.get("food_preferences", {})
    accommodation = preference_analysis.get("accommodation_preferences", {})
    
    # 整合为统一格式
    consolidated = {
        "destinations": core_intent.get("destinations", []),
        "travel_days": core_intent.get("travel_days", 1),
        "travel_theme": core_intent.get("travel_theme", []),
        "budget_range": core_intent.get("budget_range", "中等"),
        "group_info": {
            "size": core_intent.get("group_size", 1),
            "members": core_intent.get("group_members", [])
        },
        "transportation": {
            "departure_city": core_intent.get("departure_city", ""),
            "has_driving": driving.get("has_driving_needs", False),
            "vehicle_type": driving.get("vehicle_requirements", ""),
            "between_cities": multi_city.get("transportation_between_cities", "")
        },
        "preferences": {
            "attractions": attractions,
            "food": food,
            "accommodation": accommodation
        },
        "constraints": {
            "budget_per_day": core_intent.get("budget_per_day", 0),
            "accessibility_needs": attractions.get("accessibility_needs", []),
            "dietary_restrictions": food.get("dietary_restrictions", [])
        }
    }
    
    return consolidated

@unified_registry.register_planner_tool
def format_framework_analysis_prompt(user_query: str, user_profile: Dict[str, Any]) -> str:
    """格式化核心框架分析的提示词"""
    prompt_template = """
    # 旅行规划核心框架分析

    ## 用户查询
    {user_query}

    ## 用户画像
    {user_profile}

    ## 分析任务
    请对用户的旅行需求进行核心框架分析，包括：
    1. 核心意图分析：目的地、天数、主题、预算、人数等
    2. 多城市策略：是否涉及多个城市，交通方式，时间分配
    3. 自驾情境：是否需要自驾，应用场景，车辆要求

    ## 输出格式
    请严格按照JSON格式输出，包含core_intent、multi_city_strategy、driving_context三个主要部分。
    """
    
    return prompt_template.format(
        user_query=user_query,
        user_profile=str(user_profile)
    )

@unified_registry.register_planner_tool
def format_preference_analysis_prompt(user_query: str, user_profile: Dict[str, Any], framework_result: Dict[str, Any]) -> str:
    """格式化偏好分析的提示词"""
    prompt_template = """
    # 旅行规划偏好分析

    ## 用户查询
    {user_query}

    ## 用户画像
    {user_profile}

    ## 已确定的旅行框架
    {framework_result}

    ## 分析任务
    基于已确定的旅行框架，请深入分析用户的个性化偏好：
    1. 景点偏好：类型、必游景点、避免类型、无障碍需求、时间偏好
    2. 美食偏好：菜系、饮食限制、预算、用餐场景、时间安排
    3. 住宿偏好：酒店等级、位置优先级、房间要求、设施需求、预算

    ## 输出格式
    请严格按照JSON格式输出，包含attraction_preferences、food_preferences、accommodation_preferences三个主要部分。
    """
    
    return prompt_template.format(
        user_query=user_query,
        user_profile=str(user_profile),
        framework_result=str(framework_result)
    )
```

## 5. Agent代码修改 (Agent Code Modification)

### 5.1. LangGraph节点重构

在`src/agents/travel_planner_lg/nodes.py`中实现新的两步分析节点：

```python
# src/agents/travel_planner_lg/nodes.py
from src.tools.unified_registry import unified_registry
from src.services.unified_event_bus import UnifiedEventBus

async def run_framework_analysis(state: StandardAgentState):
    """执行核心框架分析"""
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")
    
    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id, 
            "framework_analysis", 
            "核心框架分析", 
            "正在分析您的旅行核心需求和框架..."
        )
    
    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_framework_analysis_prompt")
        
        # 格式化提示词
        user_query = state.get("messages", [])[-1].get("content", "")
        user_profile = {}  # 从user_profile_service获取
        
        prompt = format_prompt(user_query, user_profile)
        
        # 调用LLM
        response = await reasoning_service.call_llm(prompt)
        
        # 解析结果
        framework_analysis = response  # 假设返回的是JSON格式
        
        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "framework_analysis",
                "success",
                framework_analysis
            )
        
        return {"framework_analysis": framework_analysis}
        
    except Exception as e:
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "framework_analysis")
        raise

async def run_preference_analysis(state: StandardAgentState):
    """执行偏好分析"""
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")
    
    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "preference_analysis",
            "个性化偏好分析",
            "正在深入分析您的景点、美食、住宿偏好..."
        )
    
    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_preference_analysis_prompt")
        
        # 获取前一步的结果
        framework_result = state.get("framework_analysis", {})
        user_query = state.get("messages", [])[-1].get("content", "")
        user_profile = {}  # 从user_profile_service获取
        
        # 格式化提示词
        prompt = format_prompt(user_query, user_profile, framework_result)
        
        # 调用LLM
        response = await reasoning_service.call_llm(prompt)
        
        # 解析结果
        preference_analysis = response  # 假设返回的是JSON格式
        
        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "preference_analysis", 
                "success",
                preference_analysis
            )
        
        return {"preference_analysis": preference_analysis}
        
    except Exception as e:
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "preference_analysis")
        raise

async def prepare_planning_context(state: StandardAgentState):
    """整合分析结果为规划上下文"""
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")
    
    try:
        # 获取Planner Tool
        create_consolidated = unified_registry.get_planner_tool("create_consolidated_intent")
        
        # 获取两步分析的结果
        framework_analysis = state.get("framework_analysis", {})
        preference_analysis = state.get("preference_analysis", {})
        
        # 整合结果
        consolidated_intent = create_consolidated(framework_analysis, preference_analysis)
        
        # 更新状态：分析阶段完成，准备进入规划阶段
        updates = {
            "consolidated_intent": consolidated_intent,
            "current_phase": "planning_ready"
        }
        
        # 同步状态到Redis
        if event_bus:
            await event_bus.sync_from_agent_state(task_id, {**state, **updates})
        
        return updates
        
    except Exception as e:
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "context_preparation")
        raise
```

### 5.2. 计算图更新

在`src/agents/travel_planner_lg/graph.py`中更新LangGraph的结构：

```python
# src/agents/travel_planner_lg/graph.py
from langgraph.graph import StateGraph, START, END

def build_unified_graph():
    """构建统一的LangGraph计算图"""
    builder = StateGraph(StandardAgentState)
    
    # 添加两步分析节点
    builder.add_node("framework_analyzer", run_framework_analysis)
    builder.add_node("preference_analyzer", run_preference_analysis)
    builder.add_node("context_preparer", prepare_planning_context)
    
    # 添加ICP规划节点
    builder.add_node("planner_agent", planner_agent_node)
    builder.add_node("tool_executor", tool_executor_node)
    
    # 定义边：两步分析 → 上下文整合 → ICP规划
    builder.add_edge(START, "framework_analyzer")
    builder.add_edge("framework_analyzer", "preference_analyzer")
    builder.add_edge("preference_analyzer", "context_preparer")
    builder.add_edge("context_preparer", "planner_agent")
    
    # ICP规划的循环边
    builder.add_edge("tool_executor", "planner_agent")
    builder.add_conditional_edges(
        "planner_agent",
        route_action,
        {
            "execute_tool": "tool_executor",
            "finish": END
        }
    )
    
    return builder.compile()
```

## 6. 与其他重构文档的集成 (Integration with Other Documents)

### 6.1. 与规划距离逻辑.md的集成

**无缝衔接**: 两步意图分析的结果通过`consolidated_intent`字段直接传递给ICP迭代规划阶段，作为其初始上下文。

**状态统一**: 使用StandardAgentState确保意图分析与ICP规划的状态完全兼容。

### 6.2. 与推送.md的集成

**阶段事件**: 两个分析阶段都会发布对应的`phase_start`和`phase_end`事件：
- `framework_analysis` → 核心框架分析阶段
- `preference_analysis` → 偏好分析阶段

**实时反馈**: 前端可以实时看到分析进度，提升用户体验。

### 6.3. 与旅游搭子UI.md的集成

**UI映射**: 前端的分析面板可以直接根据阶段事件更新对应的分析项状态。

**结果展示**: 分析结果可以直接在左侧分析面板中展示，为用户提供透明的分析过程。

## 7. 实施步骤 (Implementation Steps)

建议按照以下步骤进行重构：

1.  **创建Planner Tools**: 在`src/tools/travel_planner/consolidated_tools.py`中实现所有相关的Planner Tools。
2.  **编写新Prompt**: 在`src/prompts/travel_planner/consolidated/`目录中创建两个新的Prompt文件。
3.  **实现新节点**: 在`src/agents/travel_planner_lg/nodes.py`中实现三个新的节点函数。
4.  **重构计算图**: 修改`src/agents/travel_planner_lg/graph.py`，更新图的节点和边。
5.  **集成测试**: 对重构后的流程进行端到端测试，确保分析结果的质量和数据流的正确性。
6.  **清理旧资产**: 确认新流程工作正常后，可以安全地删除旧的六个Prompt文件和相关的分析函数。

## 8. 预期收益 (Expected Benefits)

### 8.1. 性能提升
- **响应时间**: 从6次LLM调用减少到2次，预计响应时间减少60%
- **成本降低**: API调用次数减少67%，运营成本显著下降

### 8.2. 架构优化
- **代码简化**: 从6个分析节点简化为2个，代码维护成本降低
- **状态统一**: 与StandardAgentState完全集成，状态管理更加清晰
- **事件驱动**: 与UnifiedEventBus集成，实现完整的实时推送

### 8.3. 用户体验
- **实时反馈**: 用户可以看到清晰的两阶段分析进度
- **透明化**: 分析结果直接展示在前端，增强用户信任感
- **流畅性**: 更快的响应时间提升整体使用体验

这份重构方案将意图分析与整个统一架构深度融合，为AutoPilot AI项目的智能化升级奠定了坚实的基础。
