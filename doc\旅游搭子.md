# Travel Planner Agent 调用流程详解

本文档详细解析 `TravelPlannerAgentLangGraph` 的内部工作流，阐明其在不同阶段如何调用大语言模型（LLM）和外部工具（如POI查询）。

## 整体流程概览

Agent的规划过程主要分为两个核心阶段：

1.  **分析阶段（Analysis Phase）**: 完全由LLM驱动，用于理解用户的高层意图和偏好，**此阶段不调用任何外部POI查询工具**。
2.  **规划阶段（Planning Phase）**: 由工具驱动，将分析阶段的意图转化为具体的、可执行的旅行计划。此阶段会密集调用POI查询、路线规划等工具。

---

## 第一阶段：分析阶段

此阶段的目标是“理解用户想要什么”，而非“具体去哪里”。

-   **触发**: 用户在前端界面输入查询，点击“开始分析”。
-   **核心方法**: `plan_travel_stream_interactive`
-   **核心节点**: `core_intent_analyzer`

### 流程步骤:

1.  **启动与核心意图分析 (LLM调用#1)**
    -   **任务**: Agent调用LLM，分析用户输入的原始文本。
    -   **输入**:
        -   用户查询: "我在福州闽东大厦，这周末要去长沙玩两天"
        -   用户画像和记忆 (从数据库加载)
        -   `core_intent_schema` (JSON结构化模板)
    -   **输出**: 结构化的核心意图，例如：`{"destinations": ["长沙"], "days": 2, "transportation": "self_driving", ...}`

2.  **细化偏好分析 (LLM调用#2, #3, ...)**
    -   **任务**: 基于核心意图，LLM继续进行更深层次的偏好推断。
    -   **输入**: 核心意图结果 + 针对性的偏好分析Prompt。
    -   **输出**:
        -   **景点偏好**: `{"type": "历史文化", "pace": "休闲"}`
        -   **美食偏好**: `{"taste": "辣", "style": "湘菜"}`
        -   **住宿偏好**: `{"level": "中档", "requirements": ["免费停车"]}`

3.  **阶段结束**
    -   Agent将所有分析结果打包，并通过SSE推送一个 `complete` 事件给前端。
    -   此时，流程暂停，等待用户点击“立即规划”进入下一阶段。

---

## 第二阶段：规划阶段

此阶段的目标是“将想法变为现实”。

-   **触发**: 用户在前端界面点击“立即规划”。
-   **核心方法**: `start_planning_phase`
-   **核心节点**: `itinerary_generator` 及后续节点

### 流程步骤:

1.  **POI搜索 (工具密集调用)**
    -   **任务**: Agent**首次**调用外部工具（如高德地图API）。
    -   **输入**: 分析阶段产出的偏好，如`长沙`, `历史文化`, `湘菜`, `中档酒店`。
    -   **动作**: 并发执行多个搜索任务：
        -   搜索长沙的“历史文化”类景点。
        -   搜索长沙的“湘菜”餐厅。
        -   搜索长沙的“中档”且“带停车场”的酒店。
    -   **输出**: 多个原始的、未排序的POI列表。

2.  **路线规划与距离计算 (工具调用)**
    -   **任务**: 对POI进行筛选和排序，**此刻开始计算POI之间的地理位置和通勤距离**。
    -   **输入**: 上一步获得的POI列表。
    -   **动作**: 调用路线规划工具，计算关键POI（如酒店、核心景点）之间的驾车或步行距离和时间。
    -   **输出**: 经过地理位置聚类和排序的、更合理的POI候选列表。

3.  **行程编排 (LLM调用)**
    -   **任务**: 将排序后的POI列表串联成每日的详细行程。
    -   **输入**: 带有距离和时间信息的POI候选列表。
    -   **输出**: 结构化的每日行程（Day 1: 上午做什么，下午做什么...）。

4.  **总结与润色 (LLM调用)**
    -   **任务**: 为整个旅行计划生成一个吸引人的摘要、撰写注意事项、估算总预算等。
    -   **输入**: 每日行程。
    -   **输出**: 最终呈现给用户的图文并茂的完整旅行计划。

## 流程图

```mermaid
graph TD
    A[用户输入查询] --> B{分析阶段};

    subgraph B [分析阶段 (LLM驱动)]
        B1[LLM: 核心意图分析] --> B2[LLM: 偏好分析];
    end

    B --> C{等待用户确认};

    C -- 用户点击“立即规划” --> D{规划阶段};

    subgraph D [规划阶段 (工具驱动)]
        D1[工具: POI搜索] --> D2[工具: 路线/距离计算];
        D2 --> D3[LLM: 行程编排];
        D3 --> D4[LLM: 总结润色];
    end

    D --> E[生成最终方案];
```
