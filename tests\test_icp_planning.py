"""
ICP迭代规划测试

测试"思考-行动-观察"循环的ICP规划流程
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# 导入要测试的组件
from src.agents.travel_planner_lg.nodes import run_icp_planning
from src.agents.travel_planner_lg.state import StandardAgentState
from src.tools.unified_registry import unified_registry

# 确保工具被正确注册
import src.tools.travel_planner.icp_tools  # 这会触发工具注册
import src.tools.travel_planner.consolidated_tools


class TestICPPlanning:
    """测试ICP迭代规划"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建完整的测试状态
        self.test_state: StandardAgentState = {
            "messages": [{"content": "我想去北京玩三天"}],
            "task_id": "test_icp_001",
            "user_id": "test_user",
            "original_query": "我想去北京玩三天",
            "current_phase": "planning_ready",
            "execution_mode": "automatic",
            "framework_analysis": {
                "core_intent": {
                    "destinations": ["北京"],
                    "travel_days": 3,
                    "travel_theme": ["文化"],
                    "budget_range": "中等"
                }
            },
            "preference_analysis": {
                "attraction_preferences": {
                    "preferred_types": ["历史文化"],
                    "must_visit": ["故宫"]
                }
            },
            "consolidated_intent": {
                "destinations": ["北京"],
                "travel_days": 3,
                "preferences": {
                    "attractions": {
                        "preferred_types": ["历史文化"]
                    }
                }
            },
            "icp_context": {
                "planning_goals": ["为北京规划3天行程"],
                "available_tools": ["search_poi", "geocode"],
                "constraints": {
                    "max_days": 3,
                    "budget_limit": 1000
                },
                "success_criteria": {
                    "min_attractions_per_day": 2,
                    "max_attractions_per_day": 4
                }
            },
            "planning_log": [],
            "current_action": None,
            "daily_plans": {},
            "daily_time_tracker": {},
            "total_budget_tracker": 0.0,
            "tool_results": {},
            "notification_service": None,
            "user_profile": {},
            "user_memories": None,
            "vehicle_info": None,
            "final_itinerary": None,
            "is_completed": False,
            "has_error": False,
            "error_message": None,
            "destinations": None,
            "core_intent": None,
            "multi_city_strategy": None,
            "driving_context": None
        }
    
    @pytest.mark.asyncio
    async def test_icp_planning_success(self):
        """测试ICP规划成功场景"""
        # 创建模拟事件总线
        mock_event_bus = AsyncMock()
        state = self.test_state.copy()
        state["notification_service"] = mock_event_bus
        
        # 模拟search_poi工具返回结果
        mock_poi_results = [
            {"name": "故宫", "address": "北京市东城区", "type": "景点", "rating": 4.8},
            {"name": "天安门", "address": "北京市东城区", "type": "景点", "rating": 4.7},
            {"name": "颐和园", "address": "北京市海淀区", "type": "景点", "rating": 4.6}
        ]
        
        # 模拟工具执行
        with patch.object(unified_registry, 'execute_action_tool', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = mock_poi_results
            
            # 执行ICP规划
            result = await run_icp_planning(state)
        
        # 验证结果
        assert result.get("is_completed", False) is True
        assert "final_itinerary" in result
        assert "daily_plans" in result
        assert result["current_phase"] == "completed"
        assert not result.get("has_error", False)
        
        # 验证规划日志
        planning_log = result.get("planning_log", [])
        assert len(planning_log) > 0
        
        # 验证事件发布
        assert mock_event_bus.notify_phase_start.called
        assert mock_event_bus.notify_phase_end.called
    
    @pytest.mark.asyncio
    async def test_icp_planning_with_multiple_iterations(self):
        """测试多次迭代的ICP规划"""
        mock_event_bus = AsyncMock()
        state = self.test_state.copy()
        state["notification_service"] = mock_event_bus
        
        # 模拟多次工具调用
        call_count = 0
        async def mock_tool_execution(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                # 第一次返回第一天的景点
                return [
                    {"name": "故宫", "address": "北京市东城区", "type": "景点", "rating": 4.8},
                    {"name": "天安门", "address": "北京市东城区", "type": "景点", "rating": 4.7}
                ]
            elif call_count == 2:
                # 第二次返回第二天的景点
                return [
                    {"name": "颐和园", "address": "北京市海淀区", "type": "景点", "rating": 4.6},
                    {"name": "圆明园", "address": "北京市海淀区", "type": "景点", "rating": 4.5}
                ]
            else:
                # 第三次返回第三天的景点
                return [
                    {"name": "长城", "address": "北京市延庆区", "type": "景点", "rating": 4.9},
                    {"name": "明十三陵", "address": "北京市昌平区", "type": "景点", "rating": 4.4}
                ]
        
        with patch.object(unified_registry, 'execute_action_tool', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = mock_tool_execution
            
            result = await run_icp_planning(state)
        
        # 验证多天规划
        daily_plans = result.get("daily_plans", {})
        assert len(daily_plans) >= 2  # 至少规划了2天
        
        # 验证工具被多次调用
        assert mock_execute.call_count >= 2
        
        # 验证规划日志记录了多个步骤
        planning_log = result.get("planning_log", [])
        assert len(planning_log) >= 3  # 至少有3个规划步骤
    
    @pytest.mark.asyncio
    async def test_icp_planning_tool_failure_handling(self):
        """测试工具执行失败的处理"""
        mock_event_bus = AsyncMock()
        state = self.test_state.copy()
        state["notification_service"] = mock_event_bus
        
        # 模拟工具执行失败
        with patch.object(unified_registry, 'execute_action_tool', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("API调用失败")
            
            result = await run_icp_planning(state)
        
        # 验证错误处理
        # ICP应该能够处理工具失败并继续规划
        assert "planning_log" in result
        planning_log = result.get("planning_log", [])
        
        # 应该记录了工具执行失败的信息
        log_content = " ".join(planning_log)
        assert "search_poi" in log_content or "执行" in log_content
    
    @pytest.mark.asyncio
    async def test_icp_planning_missing_context(self):
        """测试缺少ICP上下文的错误处理"""
        state = self.test_state.copy()
        # 移除ICP上下文
        del state["icp_context"]
        
        result = await run_icp_planning(state)
        
        # 验证错误处理
        assert result["has_error"] is True
        assert "error_message" in result
        assert "ICP context not found" in result["error_message"]
        assert result["current_phase"] == "error"
    
    @pytest.mark.asyncio
    async def test_icp_planning_missing_tools(self):
        """测试缺少必需工具的错误处理"""
        # 临时移除关键工具
        original_tools = {}
        tool_names = ["generate_planning_thought", "select_next_action", "observe_action_result"]
        
        for tool_name in tool_names:
            if tool_name in unified_registry._planner_tools:
                original_tools[tool_name] = unified_registry._planner_tools.pop(tool_name)
        
        try:
            state = self.test_state.copy()
            result = await run_icp_planning(state)
            
            # 验证错误处理
            assert result["has_error"] is True
            assert "error_message" in result
            assert "tools not found" in result["error_message"]
            
        finally:
            # 恢复工具
            for tool_name, tool_func in original_tools.items():
                unified_registry._planner_tools[tool_name] = tool_func
    
    def test_icp_tools_registration(self):
        """测试ICP工具是否正确注册"""
        required_tools = [
            "generate_planning_thought",
            "select_next_action", 
            "observe_action_result",
            "update_planning_state",
            "check_planning_completion"
        ]
        
        for tool_name in required_tools:
            assert tool_name in unified_registry._planner_tools, f"Tool {tool_name} not registered"
            
            # 验证工具可调用
            tool_func = unified_registry.get_planner_tool(tool_name)
            assert callable(tool_func), f"Tool {tool_name} is not callable"
    
    def test_planning_thought_generation(self):
        """测试规划思考生成"""
        think_tool = unified_registry.get_planner_tool("generate_planning_thought")
        
        current_state = {
            "daily_plans": {},
            "total_budget_tracker": 0,
            "consolidated_intent": {"travel_days": 3}
        }
        
        planning_context = {
            "constraints": {"max_days": 3, "budget_limit": 1000}
        }
        
        result = think_tool(current_state, planning_context, 1)
        
        # 验证思考结果
        assert "thought_content" in result
        assert "reasoning_step" in result
        assert "next_action_suggestion" in result
        assert result["reasoning_step"] == 1
        assert isinstance(result["thought_content"], str)
        assert len(result["thought_content"]) > 0
    
    def test_action_selection(self):
        """测试行动选择"""
        action_tool = unified_registry.get_planner_tool("select_next_action")
        
        thought_result = {
            "next_action_suggestion": {
                "action_type": "search_poi",
                "reason": "需要搜索景点"
            },
            "confidence": 0.8
        }
        
        available_tools = ["search_poi", "geocode"]
        current_state = {
            "daily_plans": {},
            "consolidated_intent": {
                "destinations": ["北京"],
                "preferences": {
                    "attractions": {
                        "preferred_types": ["历史文化"]
                    }
                }
            }
        }
        
        result = action_tool(thought_result, available_tools, current_state)
        
        # 验证行动选择
        assert "selected_action" in result
        assert "action_reasoning" in result
        
        selected_action = result["selected_action"]
        assert "tool_name" in selected_action
        assert "parameters" in selected_action
        assert selected_action["tool_name"] == "search_poi"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
