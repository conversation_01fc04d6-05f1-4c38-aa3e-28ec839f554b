#!/usr/bin/env python3
"""
前端集成测试
使用Playwright测试前端界面的实际交互
"""

import asyncio
import time
from playwright.async_api import async_playwright

async def test_frontend_integration():
    """测试前端集成功能"""
    
    print("🎭 启动前端集成测试...")
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)  # 设置为False以便观察
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 访问前端页面
            print("📱 访问前端页面...")
            await page.goto("http://localhost:8000/static/index.html")
            await page.wait_for_load_state("networkidle")
            
            # 检查页面标题
            title = await page.title()
            print(f"📄 页面标题: {title}")
            
            # 检查关键元素是否存在
            print("🔍 检查页面元素...")
            
            # 检查输入框
            query_input = page.locator("#userQuery")
            await query_input.wait_for(state="visible")
            print("✅ 查询输入框存在")
            
            # 检查用户ID输入框
            user_id_input = page.locator("#userId")
            await user_id_input.wait_for(state="visible")
            print("✅ 用户ID输入框存在")
            
            # 检查开始规划按钮
            start_btn = page.locator("#startPlanningBtn")
            print("✅ 开始规划按钮存在")
            
            # 检查分析面板
            analysis_panel = page.locator(".analysis-panel")
            await analysis_panel.wait_for(state="visible")
            print("✅ 分析面板存在")
            
            # 检查分析步骤
            analysis_steps = page.locator(".analysis-item")
            step_count = await analysis_steps.count()
            print(f"✅ 分析步骤数量: {step_count}")
            
            # 输入测试查询
            print("\n📝 输入测试查询...")
            await query_input.fill("上海3天亲子慢节奏趣味性儿童友好")
            await user_id_input.fill("1")
            
            # 点击开始规划按钮
            print("🚀 点击开始规划...")
            plan_btn = page.locator("button:has-text('开始规划')")
            await plan_btn.click()
            
            # 等待分析阶段开始
            print("⏳ 等待分析阶段...")
            await page.wait_for_selector(".analysis-item.active", timeout=10000)
            print("✅ 分析阶段已开始")
            
            # 等待分析步骤完成
            print("📊 等待分析步骤完成...")
            completed_steps = 0
            max_wait_time = 60  # 最大等待60秒
            start_time = time.time()
            
            while completed_steps < 4 and (time.time() - start_time) < max_wait_time:
                completed_elements = page.locator(".analysis-item.completed")
                completed_steps = await completed_elements.count()
                print(f"   已完成步骤: {completed_steps}/4")
                
                if completed_steps < 4:
                    await asyncio.sleep(2)
            
            if completed_steps == 4:
                print("✅ 所有分析步骤完成")
                
                # 检查立即规划按钮是否显示
                immediate_plan_btn = page.locator("#startPlanningBtn")
                is_visible = await immediate_plan_btn.is_visible()
                
                if is_visible:
                    print("✅ 立即规划按钮已显示")
                    
                    # 点击立即规划按钮
                    print("🎯 点击立即规划按钮...")
                    await immediate_plan_btn.click()
                    
                    # 等待规划阶段
                    print("⏳ 等待规划阶段...")
                    await asyncio.sleep(5)  # 等待规划阶段开始
                    
                    # 检查是否切换到规划视图
                    planning_view = page.locator("#analysisView")
                    is_planning_visible = await planning_view.is_visible()
                    
                    if is_planning_visible:
                        print("✅ 已切换到规划阶段视图")
                        
                        # 等待规划完成
                        print("⏳ 等待规划完成...")
                        await asyncio.sleep(30)  # 等待规划完成
                        
                        print("✅ 前端集成测试完成")
                        return True
                    else:
                        print("❌ 未能切换到规划阶段视图")
                        return False
                else:
                    print("❌ 立即规划按钮未显示")
                    return False
            else:
                print(f"❌ 分析步骤未完全完成: {completed_steps}/4")
                return False
                
        except Exception as e:
            print(f"❌ 前端集成测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            # 关闭浏览器
            await browser.close()

async def main():
    """主函数"""
    print("🧪 启动前端集成测试...")
    
    success = await test_frontend_integration()
    
    if success:
        print("\n🎉 前端集成测试成功！")
        print("✅ 页面加载正常")
        print("✅ 分析阶段正常")
        print("✅ 规划阶段正常")
        print("✅ 用户交互正常")
        return True
    else:
        print("\n💥 前端集成测试失败！")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎯 集成测试结论: 前端应用完全可用！")
    else:
        print("\n💥 集成测试结论: 前端应用存在问题")
