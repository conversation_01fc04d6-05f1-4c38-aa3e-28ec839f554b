#!/usr/bin/env python3
"""
测试餐饮活动详细信息显示
验证修复后的系统能否显示具体的餐厅信息而不是通用的"午餐时间"、"晚餐时间"
"""
import asyncio
import httpx
import json
from playwright.async_api import async_playwright

async def test_restaurant_details():
    """测试餐饮活动详细信息"""
    print("🍽️ 开始测试餐饮活动详细信息显示...")
    
    # 1. 先测试后端API
    print("\n📡 测试后端API...")
    await test_backend_restaurant_data()
    
    # 2. 再测试前端显示
    print("\n🖥️ 测试前端显示...")
    await test_frontend_restaurant_display()

async def test_backend_restaurant_data():
    """测试后端餐厅数据"""
    url = "http://localhost:8000/api/v3/travel-planner/plan"
    
    payload = {
        "user_query": "我想去北京玩3天，喜欢历史文化景点，需要自驾出行，预算中等",
        "user_id": "1",
        "execution_mode": "interactive",
        "vehicle_info": {
            "model": "特斯拉Model 3",
            "range": 500,
            "chargingType": "快充",
            "executionMode": "interactive"
        }
    }
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(url, json=payload)
            
            if response.status_code == 200:
                print("✅ API请求成功")
                
                # 解析SSE流
                content = response.text
                lines = content.split('\n')
                
                itinerary_updates = []
                
                for line in lines:
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            
                            if data.get('event') == 'ITINERARY_UPDATE':
                                itinerary_updates.append(data['data'])
                                
                        except json.JSONDecodeError:
                            continue
                
                print(f"📊 收到 {len(itinerary_updates)} 个行程更新事件")
                
                # 分析餐饮活动
                dining_activities = []
                for update in itinerary_updates:
                    activity = update.get('activity', {})
                    if activity.get('type') == 'dining':
                        dining_activities.append(activity)
                
                print(f"\n🍽️ 餐饮活动分析 (共{len(dining_activities)}个):")
                
                for i, dining in enumerate(dining_activities, 1):
                    name = dining.get('name', '未知')
                    meal_type = dining.get('meal_type', '')
                    cuisine_type = dining.get('cuisine_type', '')
                    address = dining.get('address', '')
                    rating = dining.get('rating', 0)
                    phone = dining.get('phone', '')
                    description = dining.get('description', '')
                    start_time = dining.get('start_time', '')
                    end_time = dining.get('end_time', '')
                    
                    print(f"\n  {i}. {name}")
                    print(f"     🕐 时间: {start_time} - {end_time}")
                    print(f"     🍽️ 餐饮类型: {meal_type}")
                    if cuisine_type:
                        print(f"     🥢 菜系: {cuisine_type}")
                    if address:
                        print(f"     📍 地址: {address}")
                    if rating > 0:
                        print(f"     ⭐ 评分: {rating}")
                    if phone:
                        print(f"     📞 电话: {phone}")
                    if description:
                        print(f"     📝 描述: {description}")
                    
                    # 检查是否是具体餐厅还是通用时间
                    if name in ["午餐时间", "晚餐时间"]:
                        print(f"     ❌ 仍然是通用时间，未获取到具体餐厅信息")
                    else:
                        print(f"     ✅ 已获取到具体餐厅信息")
                
                # 验证结果
                specific_restaurants = [d for d in dining_activities if d.get('name') not in ["午餐时间", "晚餐时间"]]
                generic_times = [d for d in dining_activities if d.get('name') in ["午餐时间", "晚餐时间"]]
                
                print(f"\n📊 餐饮信息统计:")
                print(f"  具体餐厅: {len(specific_restaurants)}个")
                print(f"  通用时间: {len(generic_times)}个")
                
                if len(specific_restaurants) >= len(dining_activities) * 0.8:
                    print("🎉 后端餐厅信息修复成功！大部分餐饮活动都有具体餐厅信息")
                elif len(specific_restaurants) > 0:
                    print("⚠️ 后端餐厅信息部分修复，仍有改进空间")
                else:
                    print("❌ 后端餐厅信息修复失败，仍然显示通用时间")
                    
            else:
                print(f"❌ API请求失败: {response.status_code}")
                
    except Exception as e:
        print(f"💥 后端测试异常: {str(e)}")

async def test_frontend_restaurant_display():
    """测试前端餐厅显示"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🚀 启动前端测试...")
            
            # 导航到页面
            await page.goto("http://localhost:8000/static/travel_planner_v3_refactored.html")
            await page.wait_for_load_state("networkidle")
            
            # 输入测试数据
            await page.fill('#userQuery', "我想去北京玩3天，喜欢历史文化景点，需要自驾出行，预算中等")
            
            # 开始分析
            await page.click('button:has-text("开始分析")')
            
            # 等待分析完成
            await page.wait_for_selector('button:has-text("立即规划")', timeout=120000)
            
            # 等待行程显示
            await page.wait_for_timeout(5000)
            
            # 检查餐饮活动显示
            dining_cards = await page.query_selector_all('.activity-card:has(.badge:has-text("餐饮"))')
            print(f"🍽️ 找到 {len(dining_cards)} 个餐饮活动卡片")
            
            restaurant_details = []
            for i, card in enumerate(dining_cards, 1):
                # 获取餐厅名称
                name_element = await card.query_selector('.card-title')
                name = await name_element.inner_text() if name_element else "未知"
                
                # 获取地址
                address_element = await card.query_selector('p:has(.bi-geo)')
                address = await address_element.inner_text() if address_element else ""
                
                # 获取菜系信息
                cuisine_element = await card.query_selector('p:has(.bi-bowl)')
                cuisine = await cuisine_element.inner_text() if cuisine_element else ""
                
                # 获取电话
                phone_element = await card.query_selector('p:has(.bi-telephone)')
                phone = await phone_element.inner_text() if phone_element else ""
                
                # 获取评分
                rating_element = await card.query_selector('div:has(.bi-star-fill)')
                rating = await rating_element.inner_text() if rating_element else ""
                
                # 获取时间
                time_element = await card.query_selector('.time-badge')
                time = await time_element.inner_text() if time_element else ""
                
                restaurant_info = {
                    "name": name.strip(),
                    "address": address.strip(),
                    "cuisine": cuisine.strip(),
                    "phone": phone.strip(),
                    "rating": rating.strip(),
                    "time": time.strip()
                }
                restaurant_details.append(restaurant_info)
                
                print(f"\n  餐厅 {i}:")
                print(f"    名称: {restaurant_info['name']}")
                print(f"    时间: {restaurant_info['time']}")
                if restaurant_info['address']:
                    print(f"    地址: {restaurant_info['address']}")
                if restaurant_info['cuisine']:
                    print(f"    菜系: {restaurant_info['cuisine']}")
                if restaurant_info['phone']:
                    print(f"    电话: {restaurant_info['phone']}")
                if restaurant_info['rating']:
                    print(f"    评分: {restaurant_info['rating']}")
                
                # 检查是否是具体餐厅
                if any(keyword in restaurant_info['name'] for keyword in ["午餐时间", "晚餐时间"]):
                    print(f"    ❌ 仍然显示通用时间")
                else:
                    print(f"    ✅ 显示具体餐厅信息")
            
            # 统计结果
            specific_restaurants = [r for r in restaurant_details if not any(keyword in r['name'] for keyword in ["午餐时间", "晚餐时间"])]
            
            print(f"\n📊 前端显示统计:")
            print(f"  总餐饮活动: {len(restaurant_details)}个")
            print(f"  具体餐厅: {len(specific_restaurants)}个")
            print(f"  通用时间: {len(restaurant_details) - len(specific_restaurants)}个")
            
            if len(specific_restaurants) >= len(restaurant_details) * 0.8:
                print("🎉 前端餐厅信息显示修复成功！")
            elif len(specific_restaurants) > 0:
                print("⚠️ 前端餐厅信息显示部分修复")
            else:
                print("❌ 前端餐厅信息显示修复失败")
            
            # 保持浏览器打开供检查
            print("\n⏳ 保持浏览器打开30秒供手动检查...")
            await page.wait_for_timeout(30000)
            
        except Exception as e:
            print(f"💥 前端测试异常: {str(e)}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_restaurant_details())
