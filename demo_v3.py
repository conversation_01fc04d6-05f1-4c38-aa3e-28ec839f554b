#!/usr/bin/env python3
"""
AutoPilot AI V3.0 演示脚本

展示统一架构的完整工作流：
1. 两阶段意图分析
2. ICP迭代规划
3. 实时事件流
4. 最终结果输出
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any
from unittest.mock import AsyncMock, Mock

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入统一架构组件
from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.tools.unified_registry import unified_registry

# 确保工具被正确注册
import src.tools.travel_planner.consolidated_tools
import src.tools.travel_planner.icp_tools


class DemoEventBus(UnifiedEventBus):
    """演示用的事件总线，打印事件到控制台"""
    
    def __init__(self):
        # 创建模拟Redis客户端
        mock_redis = AsyncMock()
        mock_redis_client = Mock()
        mock_redis_client.client = mock_redis
        super().__init__(mock_redis_client)
        
        self.events = []
    
    async def _publish(self, task_id: str, event_data: Dict[str, Any]):
        """重写发布方法，打印到控制台"""
        self.events.append(event_data)
        event_type = event_data.get('event', 'unknown')
        
        if event_type == 'phase_start':
            data = event_data.get('data', {})
            print(f"🚀 [{data.get('phase_name', 'unknown')}] {data.get('title', '')}: {data.get('message', '')}")
        
        elif event_type == 'phase_end':
            data = event_data.get('data', {})
            status = data.get('status', 'unknown')
            phase_name = data.get('phase_name', 'unknown')
            emoji = "✅" if status == "success" else "❌"
            print(f"{emoji} [{phase_name}] 阶段完成 - 状态: {status}")
        
        elif event_type == 'PLANNING_LOG':
            data = event_data.get('data', {})
            step = data.get('reasoning_step', 0)
            message = data.get('message', '')
            print(f"🧠 [步骤 {step}] {message}")
        
        elif event_type == 'ITINERARY_UPDATE':
            data = event_data.get('data', {})
            day = data.get('day', 0)
            activity = data.get('activity', {})
            print(f"📅 [第 {day} 天] 添加活动: {activity.get('name', '未知活动')}")
        
        elif event_type == 'complete':
            print("🎉 规划完成！")
        
        elif event_type == 'error':
            data = event_data.get('data', {})
            print(f"❌ 错误: {data.get('message', '未知错误')}")


async def demo_basic_workflow():
    """演示基本工作流"""
    print("\n" + "="*60)
    print("🎯 AutoPilot AI V3.0 基本工作流演示")
    print("="*60)
    
    # 创建演示事件总线
    event_bus = DemoEventBus()

    # 创建规划图（不传递event_bus，避免序列化问题）
    graph = TravelPlannerGraphV3(event_bus=None)
    
    # 准备输入数据
    input_data = {
        "user_query": "我想去北京玩三天，喜欢历史文化景点，预算中等",
        "user_id": "demo_user_001",
        "execution_mode": "automatic",
        "user_profile": {
            "age": 30,
            "interests": ["文化", "历史", "美食"],
            "budget_level": "中等",
            "travel_experience": "丰富"
        }
    }
    
    print(f"📝 用户查询: {input_data['user_query']}")
    print(f"👤 用户画像: {json.dumps(input_data['user_profile'], ensure_ascii=False)}")
    print("\n开始规划...")
    
    try:
        # 模拟工具执行结果
        mock_poi_results = [
            {"name": "故宫博物院", "address": "北京市东城区景山前街4号", "type": "历史文化", "rating": 4.8},
            {"name": "天安门广场", "address": "北京市东城区", "type": "历史文化", "rating": 4.7},
            {"name": "颐和园", "address": "北京市海淀区新建宫门路19号", "type": "历史文化", "rating": 4.6},
            {"name": "天坛公园", "address": "北京市东城区天坛路甲1号", "type": "历史文化", "rating": 4.5},
            {"name": "北海公园", "address": "北京市西城区文津街1号", "type": "历史文化", "rating": 4.4}
        ]
        
        # 模拟工具执行
        original_execute = unified_registry.execute_action_tool
        
        async def mock_execute_tool(tool_name, task_id=None, **kwargs):
            if tool_name == "search_poi":
                # 模拟搜索延迟
                await asyncio.sleep(0.5)
                return mock_poi_results[:3]  # 返回前3个结果
            else:
                return await original_execute(tool_name, task_id, **kwargs)
        
        unified_registry.execute_action_tool = mock_execute_tool
        
        # 执行规划
        result = await graph.invoke(input_data)
        
        # 恢复原始方法
        unified_registry.execute_action_tool = original_execute
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 规划结果摘要")
        print("="*60)
        
        if result.get("framework_analysis"):
            framework = result["framework_analysis"]
            core_intent = framework.get("core_intent", {})
            print(f"🎯 目的地: {', '.join(core_intent.get('destinations', ['未知']))}")
            print(f"📅 天数: {core_intent.get('travel_days', '未知')} 天")
            print(f"🏷️ 主题: {', '.join(core_intent.get('travel_theme', ['未知']))}")
            print(f"💰 预算: {core_intent.get('budget_range', '未知')}")
        
        if result.get("preference_analysis"):
            preferences = result["preference_analysis"]
            attractions = preferences.get("attraction_preferences", {})
            print(f"🏛️ 偏好景点类型: {', '.join(attractions.get('preferred_types', ['未知']))}")
            print(f"⭐ 必游景点: {', '.join(attractions.get('must_visit', ['无']))}")
        
        if result.get("daily_plans"):
            daily_plans = result["daily_plans"]
            print(f"\n📅 详细行程 (共 {len(daily_plans)} 天):")
            for day, activities in daily_plans.items():
                print(f"\n第 {day} 天:")
                for i, activity in enumerate(activities, 1):
                    print(f"  {i}. {activity.get('name', '未知活动')} - {activity.get('address', '地址未知')}")
        
        print(f"\n✅ 规划状态: {'完成' if result.get('is_completed') else '未完成'}")
        print(f"📈 事件总数: {len(event_bus.events)}")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        logger.exception("Demo failed")


async def demo_streaming_workflow():
    """演示流式工作流"""
    print("\n" + "="*60)
    print("🌊 AutoPilot AI V3.0 流式工作流演示")
    print("="*60)
    
    # 创建演示事件总线
    event_bus = DemoEventBus()

    # 创建规划图（不传递event_bus，避免序列化问题）
    graph = TravelPlannerGraphV3(event_bus=None)
    
    # 准备输入数据
    input_data = {
        "user_query": "我和家人想去杭州玩2天，有小孩，喜欢自然风光",
        "user_id": "demo_user_002",
        "execution_mode": "automatic",
        "user_profile": {
            "age": 35,
            "family_size": 4,
            "children_ages": [8, 12],
            "interests": ["自然", "亲子", "休闲"],
            "budget_level": "中等"
        }
    }
    
    print(f"📝 用户查询: {input_data['user_query']}")
    print("\n开始流式规划...")
    
    try:
        # 模拟工具执行
        mock_poi_results = [
            {"name": "西湖", "address": "杭州市西湖区", "type": "自然景观", "rating": 4.8},
            {"name": "灵隐寺", "address": "杭州市西湖区", "type": "文化景点", "rating": 4.6},
            {"name": "杭州动物园", "address": "杭州市西湖区", "type": "亲子景点", "rating": 4.3}
        ]
        
        original_execute = unified_registry.execute_action_tool
        
        async def mock_execute_tool(tool_name, task_id=None, **kwargs):
            if tool_name == "search_poi":
                await asyncio.sleep(0.3)
                return mock_poi_results
            else:
                return await original_execute(tool_name, task_id, **kwargs)
        
        unified_registry.execute_action_tool = mock_execute_tool
        
        # 流式执行
        step_count = 0
        async for step in graph.stream(input_data):
            step_count += 1
            print(f"\n📦 [步骤 {step_count}] 收到更新:")
            
            for node_name, node_result in step.items():
                if isinstance(node_result, dict):
                    if "framework_analysis" in node_result:
                        print(f"  ✅ {node_name}: 框架分析完成")
                    elif "preference_analysis" in node_result:
                        print(f"  ✅ {node_name}: 偏好分析完成")
                    elif "consolidated_intent" in node_result:
                        print(f"  ✅ {node_name}: 上下文准备完成")
                    elif "final_itinerary" in node_result:
                        print(f"  ✅ {node_name}: ICP规划完成")
                    else:
                        print(f"  📄 {node_name}: 状态更新")
        
        # 恢复原始方法
        unified_registry.execute_action_tool = original_execute
        
        print(f"\n🎉 流式规划完成！共处理 {step_count} 个步骤")
        
    except Exception as e:
        print(f"❌ 流式演示失败: {str(e)}")
        logger.exception("Streaming demo failed")


async def demo_tool_registry():
    """演示工具注册表功能"""
    print("\n" + "="*60)
    print("🔧 AutoPilot AI V3.0 工具注册表演示")
    print("="*60)
    
    # 获取工具信息
    tool_info = unified_registry.get_tool_info()
    
    print(f"📊 工具统计:")
    print(f"  Action Tools: {tool_info['action_tools_count']} 个")
    print(f"  Planner Tools: {tool_info['planner_tools_count']} 个")
    print(f"  事件总线连接: {'是' if tool_info['event_bus_connected'] else '否'}")
    
    print(f"\n🛠️ Action Tools 列表:")
    for tool_name in tool_info['action_tools']:
        print(f"  - {tool_name}")
    
    print(f"\n🧠 Planner Tools 列表:")
    for tool_name in tool_info['planner_tools']:
        print(f"  - {tool_name}")
    
    # 演示工具Schema生成
    schemas = unified_registry.get_all_action_schemas()
    if schemas:
        print(f"\n📋 Action Tool Schema 示例:")
        schema = schemas[0]
        print(f"  工具名: {schema['function']['name']}")
        print(f"  描述: {schema['function']['description']}")
        print(f"  参数数量: {len(schema['function']['parameters']['properties'])}")
    
    # 演示Planner Tool调用
    think_tool = unified_registry.get_planner_tool("generate_planning_thought")
    if think_tool:
        print(f"\n🧠 Planner Tool 调用演示:")
        current_state = {
            "daily_plans": {},
            "total_budget_tracker": 0,
            "consolidated_intent": {"travel_days": 3}
        }
        planning_context = {
            "constraints": {"max_days": 3, "budget_limit": 1000}
        }
        
        result = think_tool(current_state, planning_context, 1)
        print(f"  思考内容: {result.get('thought_content', '无')[:100]}...")
        print(f"  置信度: {result.get('confidence', 0):.2f}")


async def main():
    """主演示函数"""
    print("🎭 AutoPilot AI V3.0 统一架构演示")
    print("=" * 80)
    print("本演示将展示V3.0统一架构的核心功能：")
    print("1. 基本工作流 - 完整的两阶段规划流程")
    print("2. 流式工作流 - 实时进度更新")
    print("3. 工具注册表 - 统一的工具管理")
    print("=" * 80)
    
    try:
        # 演示1: 基本工作流
        await demo_basic_workflow()
        
        # 等待用户确认
        input("\n按回车键继续下一个演示...")
        
        # 演示2: 流式工作流
        await demo_streaming_workflow()
        
        # 等待用户确认
        input("\n按回车键继续下一个演示...")
        
        # 演示3: 工具注册表
        await demo_tool_registry()
        
        print("\n" + "="*80)
        print("🎉 所有演示完成！")
        print("感谢体验 AutoPilot AI V3.0 统一架构")
        print("="*80)
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {str(e)}")
        logger.exception("Demo failed")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
