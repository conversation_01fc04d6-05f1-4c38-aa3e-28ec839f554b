import json
import hashlib
import hmac
import time
import urllib.parse
from src.core.config import get_settings

# 构建请求头
def build_headers(x_app_id):
    return {
        'Content-Type': 'application/json',
        'X-APP-ID': x_app_id,
    }

class HttpAuth(object):
    # 编码函数
    def normalize(self, string, encoding_slash=True):
        safe = '~()*!/' if encoding_slash else '~()*!'
        quoted_string = urllib.parse.quote(string, safe=safe)
        return quoted_string

    # 生成Canonical URI
    def generate_canonical_uri(self, url):
        parsed_url = urllib.parse.urlparse(url)
        return '/'.join(self.normalize(segment, False) for segment in parsed_url.path.split('/'))

    # 生成Canonical Headers
    @staticmethod
    def generate_canonical_headers(input_params, signed_headers):
        signed_headers_list = signed_headers.split(';')
        signed_headers_set = set(signed_headers_list)
        sorted_headers = sorted((k.lower(), urllib.parse.quote(v.strip(), safe='')) for k, v in input_params.items() if
                                k.lower() in signed_headers_set)
        canonical_headers = '\n'.join(f"{k}:{v}" for k, v in sorted_headers)
        signed_headers_str = ';'.join(sorted(signed_headers_set))
        print("canonical_headers:", canonical_headers)
        return canonical_headers, signed_headers_str

    # 生成签名
    def generate_signature(self, x_app_id, x_app_key, region, timestamp, expiration_in_seconds, method,
                           canonical_uri,
                           canonical_headers, signed_headers_str, canonical_query_string, public_network_sign_header):
        # 在此处选择对应的鉴权头部
        signing_key_str = f"{public_network_sign_header}/{x_app_id}/{region}/{timestamp}/{expiration_in_seconds}"

        signing_key = hmac.new(bytes(x_app_key, 'utf-8'), bytes(signing_key_str, 'utf-8'), hashlib.sha256).hexdigest()

        # print(f"signing_key = {signing_key}")
        canonical_request = f"{method.upper()}\n{canonical_uri}\n{canonical_query_string}\n{canonical_headers}"
        print("canonical_request \n", canonical_request)
        signature = hmac.new(bytes(signing_key, 'utf-8'), bytes(canonical_request, 'utf-8'), hashlib.sha256).hexdigest()
        authorization = f"{signing_key_str}/{signed_headers_str}/{signature}"
        # print("authorization", authorization)
        return authorization

    # 获取Content-Length
    def get_content_length(self, data):
        body = json.dumps(data)
        return str(len(body))

    # 发送请求并获取响应
    def get_auth(self):
        """
        主逻辑（所有参数均从配置获取）
        """
        settings = get_settings()
        tele_conf = settings.tele_sign
        url = tele_conf.api_url
        method = tele_conf.method
        canonical_query_string = ''
        x_app_id = tele_conf.x_app_id
        x_app_key = tele_conf.x_app_key
        region = tele_conf.region
        expiration_in_seconds = str(tele_conf.expiration_in_seconds)
        signed_headers = tele_conf.signed_headers
        public_network_sign_header = tele_conf.public_network_sign_header
        canonical_uri = self.generate_canonical_uri(url)
        headers = build_headers(x_app_id)
        canonical_headers, signed_headers_str = self.generate_canonical_headers(headers, signed_headers)
        timestamp = str(int(time.time()))
        authorization = self.generate_signature(
            x_app_id, x_app_key, region, timestamp, expiration_in_seconds,
            method, canonical_uri, canonical_headers, signed_headers_str, canonical_query_string, public_network_sign_header)
        headers['Authorization'] = authorization
        print(f"authorization = {authorization}")
        return headers


getAuth = HttpAuth()