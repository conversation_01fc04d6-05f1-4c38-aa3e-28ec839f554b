#!/usr/bin/env python3
"""
POI处理逻辑单元测试

验证TravelPlannerAgent中的_orchestrate_itinerary方法能够正确处理POI数据，
特别是验证以下修复：
1. 为每个POIInfo对象成功生成唯一的poi_instance_id。
2. 正确解析包含经纬度的location字符串。
3. 健壮地处理可能为空的rating字段。
"""

import asyncio
import sys
import os
import uuid

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.append(project_root)

from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import AgentState, POIInfo

# 模拟从高德API获取并经过评分后的POI数据
MOCK_SCORED_POIS = [
    {
        "id": "B000A83U0P",
        "name": "鼓浪屿",
        "type": "风景名胜;风景名胜;世界文化遗产",
        "location": "118.06734,24.44462",
        "address": "思明区晃岩路1号",
        "rating": "4.6",
        "category": "景点",
        "score": 4.5
    },
    {
        "id": "B0FFG8QT9P",
        "name": "上屿水产·闽南焗海鲜（鼓浪屿店）",
        "type": "美食;海鲜;闽菜",
        "location": "118.073313,24.453308",
        "address": "鼓浪屿龙头路118号",
        "rating": "4.8",
        "category": "美食",
        "score": 4.2
    },
    {
        "id": "B0FFH58569",
        "name": "厦门鼓浪屿寓言酒店",
        "type": "住宿服务;宾馆酒店;酒店",
        "location": "",  # 模拟位置信息缺失
        "address": "鼓浪屿安海路32号",
        "rating": None, # 模拟评分缺失
        "category": "酒店",
        "score": 3.8
    }
]

async def test_poi_orchestration():
    """测试_orchestrate_itinerary方法中的POI处理逻辑"""
    print("🚀 开始测试POI处理与编排逻辑...")

    # 1. 准备测试环境
    agent = TravelPlannerAgent()
    state = AgentState(
        trace_id="test-poi-processing",
        user_id="test-user",
        original_query="去厦门玩",
        extracted_entities={"days": 1},
        scored_pois=MOCK_SCORED_POIS # 设置模拟的POI数据
    )

    # 2. 执行被测试的方法
    print("  - 调用 _orchestrate_itinerary 方法...")
    try:
        # _orchestrate_itinerary 是一个异步生成器，我们需要消费它来触发执行
        async for _ in agent._orchestrate_itinerary(state):
            pass
        print("  - 方法调用完成")
    except Exception as e:
        print(f"❌ _orchestrate_itinerary 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    # 3. 验证结果
    print("  - 验证生成的行程计划...")
    daily_plans = state.tool_results.get("daily_plans", [])

    if not daily_plans:
        print("❌ 验证失败: 未能生成任何每日计划 (DailyPlan)")
        return False
        
    pois_in_plan: list[POIInfo] = daily_plans[0].pois

    if len(pois_in_plan) != len(MOCK_SCORED_POIS):
        print(f"❌ 验证失败: 计划中的POI数量 ({len(pois_in_plan)}) "
              f"与模拟数据数量 ({len(MOCK_SCORED_POIS)}) 不符")
        return False

    print("  - 逐一验证每个POI对象...")
    all_passed = True
    for i, poi_info in enumerate(pois_in_plan):
        mock_poi = MOCK_SCORED_POIS[i]
        print(f"    - 正在验证: {poi_info.name}")
        
        # 验证1: poi_instance_id 是否已生成且格式正确
        try:
            uuid.UUID(poi_info.poi_instance_id)
            print("      ✅ poi_instance_id: 已生成且有效")
        except (ValueError, TypeError):
            print(f"      ❌ poi_instance_id: 无效或未生成 (值: {poi_info.poi_instance_id})")
            all_passed = False

        # 验证2: rating 是否被正确处理 (None -> 0.0)
        if mock_poi.get("rating") is None and poi_info.rating == 0.0:
            print("      ✅ rating: 缺失的评分被正确处理为0.0")
        elif mock_poi.get("rating") is not None and poi_info.rating == float(mock_poi.get("rating")):
             print(f"      ✅ rating: 评分被正确解析为 {poi_info.rating}")
        else:
            print(f"      ❌ rating: 评分处理错误 (期望: {mock_poi.get('rating')}, 实际: {poi_info.rating})")
            all_passed = False

        # 验证3: location 是否被正确处理 (空字符串 -> 默认坐标)
        if not mock_poi.get("location") and poi_info.location.longitude == 0 and poi_info.location.latitude == 0:
             print("      ✅ location: 缺失的位置被正确处理为(0,0)")
        elif mock_poi.get("location"):
            expected_lon, expected_lat = map(float, mock_poi.get("location").split(','))
            if poi_info.location.longitude == expected_lon and poi_info.location.latitude == expected_lat:
                print(f"      ✅ location: 位置被正确解析为 ({poi_info.location.longitude}, {poi_info.location.latitude})")
            else:
                print("      ❌ location: 位置解析错误")
                all_passed = False

    if all_passed:
        print("\n🎉 所有POI处理验证通过！")
    else:
        print("\n💥 部分POI处理验证失败！")
        
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(test_poi_orchestration())
    if success:
        print("\n[SUCCESS] POI核心业务逻辑修复验证成功。")
        sys.exit(0)
    else:
        print("\n[FAIL] POI核心业务逻辑修复验证失败。")
        sys.exit(1) 