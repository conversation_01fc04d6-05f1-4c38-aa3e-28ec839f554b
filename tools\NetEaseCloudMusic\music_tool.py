"""
Music Tool implementation using NetEase Cloud Music OpenAPI
"""
import time
import json
import base64
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256
import requests
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from .sign_utils import RSASignature
from .music_models import (
    FreeTrail, Song, Album, Artist, Playlist, Qualities,
    SongDetail, PlaylistDetail, AlbumArtist, AlbumDetail, HiResCategory
)
from urllib.parse import quote
import os
from dotenv import load_dotenv
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MusicTool:
    """
    Music tool implementation using NetEase Cloud Music API
    Can be wrapped as an AutoGen FunctionTool
    """ 
    def __init__(
        self
    ):
        print("Initializing MusicTool")
        """
        Initialize the MusicTool
        
        Args:
            app_id: Your application ID
            private_key: Your RSA private key in PEM format
            base_url: API base URL
        """
        self.device_info = {
            "deviceType": "andrcar",
            "os": "andrcar",
            "appVer": "0.1",
            "channel": "Telecom",
            "model": "kys",
            "deviceId": "321",
            "brand": "Telecom",
            "osVer": "8.1.0",
            "clientIp": "***********"
        }
        self.app_id: str = os.getenv("NETEASE_APP_ID", "a301020000000000dc6f2d37795a2ed0")
        self.private_key: str = os.getenv("NETEASE_PRIVATE_KEY", "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDLhqcO3xhKs0bABcigiYvUKrD9fIu2RgrpfE2sncWCNMGHiqGJBWj5A+2W1X902A0M9wTtP7UvbFPtQTQ1AAo53uPrD0HWsibhoYgmIa4/vHcB/Aj0Zl0+LSdVjsQZ41DbSqvv5GVpomYYrImi4gUQ7qq4XispEQ83PNCOkeCJwur7uPVFPP3vdQgA45YQ+GsWyBOw7nyXD2sHj0tM4L5gyZC3AKnrRgd4b4hdysclGLvKnTnDbU8REjlX5eWtiSFC69JGOQ2grmux4OCpb+GAlmCYWWbyKZnQoCb9oD0bnsZRdSGjRNQG6/6zga7QyPl6S8t0/nJnDborxowTQEK1AgMBAAECggEBAMABKStTdrRKSw2upyYkfODM4apSS+/KRlMdLJ4TPZhnssfvFhORIgYCGat5gMO3ayB3DZPQzVqO5OJn6Bu0DPGpf/VqsF8eNKYZ4LqwfZWtc8xQfBYtV/vnVztU81XE6Kvo1yn6m127knvmkfYzKjpeHnrOhAM7D9K5sbs+jckHhdIb+BoB8kFqiiTOdoTY2ojdakvL91Dgzn5OLPjRWYkXPl2+ZEm14NotNYkqxY7Ihq00rBebp5oyELQ3RqlhkEXZh9rPHVUYir4vMeDZZwRI8q14Dv/Nih/autqMWB6V/CRtgY95fdzN2/muVsROC/ZLN+BHuLdgNYLjmc99J8ECgYEA7p671Y+wqe5F8SsWEk5lYxXDUtvChk3puvy/bfmAFsElbJ7UKAXFnnWLYhYxUuNNvnEUHAo0OM5fN5fb+U5oBCKMfOp3W39aXSl1096jbZ4CAh4kP+d90VV1HW4ZPHmOQB4T2Egwxssb947Xj/pY0KEc5xde5QTbQTC+e/x4wtECgYEA2lmPrMVPqMCsqZ4UliSZ5h9rQLa8wgP/uakMysi/2XqDce3g92fonj4QZrKGTyexyLQMIIzmKZ5PoyhFEnVH0keOOg7h9Fy0cjXYy8KMp7OgHsTCjTPzT+KK5sIET3Y6TLpIIe3f0imuoHAZvqHy3UTTQpuG1C/sJ0pipt44EqUCgYAUTku7s7ub6lvnVvxM2IEGOo7hmTPni+/M6r87k5TT2uHdaXKyaTNU9bnDwhG2dQNjpmnIrRGel+lAaNreT07Is0vzQVyg26iL7wBI5FRR+rU/CuOY0JFDZBtQQtNA8zMov6Z+rD4CXLxnh69OYEZ4ko1MGRAJOy2RUZ2UHXsFUQKBgEZRpZWsjbT5KCIQCO05qF6WK8JRl4ScKRUvvjDoafCuJfrg8xx6WE3uCvYn9F01mWnbojQxbEZm95rB80y4fILaDJHiDR7XZD2L3d/xLb8seBqwnz8+RdOqT7V1MlIs7yWasHRM2VIjnluI/qGIImDkM202DTv0mwckS5UV59UJAoGAHegH8usdVmXTGuzbwN0q1KBRmua46ZkyooCr7avZY5ot0eiKYtZJC0ESIVnPIT5X0yrVdlULiM7TC5liLmHB/Euf6Un067uaRytBtq+hlzje61zfP8o64jLf8z8OU0Se82z7dqht/hUjtRLBgrjXC4ORxoa2dJKJUP0K52JD5hU=")
        self.base_url: str = os.getenv("NETEASE_BASE_URL", "https://openapi.music.163.com//openapi/music")

    def _generate_signature(self, biz_content: Dict) -> str:
        """Generate RSA SHA256 signature for API request"""
        current_time = int(time.time() * 1000)
        params = {
            'appId': self.app_id,
            # If prompted with '非法timestamp参数'(Illegal timestamp parameter 1649027002762)，Less than 5 minutes
            'timestamp': current_time,
            "accessToken":"t0950d8d4c5ac3f506b497f6b47255f1655ae763054b7593ds",
            'device': self.device_info,
            'signType': 'RSA_SHA256'
        }

        biz_content_str = json.dumps(biz_content, separators=(',', ':'))
        params['bizContent']=f'{biz_content_str}'
        rsa_signature = RSASignature()
        # Dictionary order sorting and concatenation parameters
        content = rsa_signature.format_parameters(params)
        # print(f"_generate_signature content: {content}")
        sign = rsa_signature.rsa_sign(content, self.private_key)
        # print(f"_generate_signature sign result: {sign}")
        params['sign'] = quote(sign)
        # c. Signature field with separate URL encoding
        # is_valid = rsa_signature.rsa_sign_check(to_sign, sign, public_key)
        # print(f"check sign result: {is_valid}")
        return params    
        
    def search_songs(
        self,
        keyword: str,
    ) -> List[Song]:
        """
        Search songs by keyword
        
        Args:
            keyword: Search keyword
            
        Returns:
            List of Song objects
        """
        url = f"{self.base_url}/basic/search/song/get/v2"
        # Prepare bizContent
        biz_content = {
            "keyword": keyword,
            "limit": "5",
            "offset": "0"
        }
        # Add signature
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join(
            [f"{k}={v}" for k, v in params.items()])
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        songs = []
        for item in data["data"]["records"]:            
            # Get artist name(s)
            artist = "无"
            if item["artists"] and len(item["artists"]) > 0:
                artist_names = [artist["name"] for artist in item["artists"]]
                artist = " / ".join(artist_names)
                
            song = Song(
                id=item["id"],
                name=item["name"],
                artist=artist,
                album=item["album"]["name"],
                duration=item["duration"] // 1000,  # Convert to seconds
            )
            songs.append(song)
            
        return songs        
    
    def search_songs_by_artist(
        self,
        keyword: str,
    ) -> List[Song]:
        """
        Search songs by artist keyword
        
        Args:
            keyword: Artist name keyword
            
        Returns:
            List of Song objects
        """
        url = f"{self.base_url}/basic/search/song/byartist/get/v2"
        biz_content = {
            "keyword": keyword,
            "limit": "5",
            "offset": "0"
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
        songs = []
        for item in data["data"]["records"]:
            # Get artist name(s)
            artist = "无"
            if item["artists"] and len(item["artists"]) > 0:
                artist_names = [artist["name"] for artist in item["artists"]]
                artist = " / ".join(artist_names)
                
            song = Song(
                id=item["id"],
                name=item["name"],
                artist=artist,
                album=item["album"]["name"],
                duration=item["duration"] // 1000
            )
            songs.append(song)
            
        return songs

    def search_songs_by_artist_and_name(
        self,
        artistName: str,
        songName: str,
    ) -> List[Song]:
        """
        Search songs by artist name and song name
        
        Args:
            artistName: Artist name
            songName: Song name
            
        Returns:
            List of Song objects
        """
        url = f"{self.base_url}/basic/search/song/by/artist/song/get/v2"
        biz_content = {
            "artistName": artistName,
            "songName": songName,
            "limit": "5",
            "offset": "0"
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
        
        songs = []
        for item in data["data"]["records"]:
            # Get artist name(s)
            artist = "无"
            if item["artists"] and len(item["artists"]) > 0:
                artist_names = [artist["name"] for artist in item["artists"]]
                artist = " / ".join(artist_names)
                
            song = Song(
                id=item["id"],
                name=item["name"],
                artist=artist,
                album=item["album"]["name"],
                duration=item["duration"] // 1000
            )
            songs.append(song)
            
        return songs

    def search_playlists(
        self,
        keyword: str,
    ) -> List[Playlist]:
        """
        Search playlists by keyword
        
        Args:
            keyword: Search keyword
            
        Returns:
            List of Playlist objects
        """
        url = f"{self.base_url}/basic/search/playlist/get/v2"
        biz_content = {
            "keyword": keyword,
            "limit": "5",
            "offset": "0"
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        playlists = []
        for item in data["data"]["records"]:
            playlist = Playlist(
                id=item["id"],
                name=item["name"],
                description=item.get("describe"),
                creator=item.get("creatorNickName"),
                song_count=item["trackCount"],
                play_count=item["playCount"]
            )
            playlists.append(playlist)
            
        return playlists    
    
    def search_albums(
        self,
        keyword: str,
    ) -> List[Album]:
        """
        Search albums by keyword
        
        Args:
            keyword: Search keyword
            
        Returns:
            List of Album objects
        """
        url = f"{self.base_url}/basic/search/album/get/v2"
        biz_content = {
            "keyword": keyword,
            "limit": "5",
            "offset": "0"
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        albums = []
        for item in data["data"]["records"]:
            # Get artist name(s)
            artist = "无"
            if item["artists"] and len(item["artists"]) > 0:
                artist_names = [artist["name"] for artist in item["artists"]]
                artist = " / ".join(artist_names)
                
            album = Album(
                id=item["id"],
                name=item["name"],
                artist=artist,
                description=item.get("briefDesc")
            )
            albums.append(album)
            
        return albums    
        
    def search_albums_by_artist(
        self,
        artistName: str,
        albumName: str,
    ) -> List[Album]:
        """
        Search albums by artist and album name
        
        Args:
            artistName: Artist name
            albumName: Album name
            
        Returns:
            List of Album objects
        """
        url = f"{self.base_url}/basic/search/song/by/album/artist/get/v2"
        biz_content = {
            "artistName": artistName,
            "albumName": albumName,
            "limit": "5",
            "offset": "0"
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
        
        albums = []
        for item in data["data"]["records"]:
            # Get artist name(s)
            artist = "无"
            if item["artists"] and len(item["artists"]) > 0:
                artist_names = [artist["name"] for artist in item["artists"]]
                artist = " / ".join(artist_names)
                
            album = Album(
                id=item["id"],
                name=item["name"],
                artist=artist,
                description=item.get("briefDesc")
            )
            albums.append(album)
            
        return albums

    def search_artists(
        self,
        keyword: str,
        limit: int = 5,
        offset: int = 0
    ) -> List[Artist]:
        """
        Search artists by keyword
        
        Args:
            keyword: Search keyword
            limit: Number of results per page
            offset: Result offset for pagination
            
        Returns:
            List of Artist objects
        """
        url = f"{self.base_url}/basic/search/artists/get/v2"
        biz_content = {
            "keyword": keyword,
            "limit": str(limit),
            "offset": str(offset)
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        artists = []
        for item in data["data"]["records"]:
            artist = Artist(
                id=item["id"],
                name=item["name"],
                alias=item.get("transName"),
                cover_img_url=item.get("coverImgUrl"),
                type=item.get("type")
            )
            artists.append(artist)
            
        return artists

    def get_hot_keywords(self) -> List[str]:
        """
        Get hot search keywords
        
        Returns:
            List of hot keywords
        """
        url = f"{self.base_url}/basic/search/hot/keyword/get/v2"
        biz_content = {}
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        # return [item["keyword"] for item in data["data"]]
        return data["data"]

    def get_suggest_keywords(
        self,
        keyword: str,
        limit: int = 5
    ) -> List[str]:
        """
        Get search keyword suggestions
        
        Args:
            keyword: Input keyword
            limit: Number of suggestions to return
            
        Returns:
            List of suggested keywords
        """
        url = f"{self.base_url}/basic/search/suggest/keyword/get/v2"
        biz_content = {
            "keyword": keyword
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        # API返回格式:
        # data: {
        #     'suggests': [{
        #         'keyword': '关键词',
        #         'highLightInfo': '...',
        #         'iconUrl': '...',
        #         ...
        #     }],
        #     'recs': []
        # }
        return [item["keyword"] for item in data["data"]["suggests"]]

    def get_hi_res(
        self,
        resource_types: List[str],
        with_url: bool = True,
        bitrate: int = 320
    ) -> List[HiResCategory]:
        """
        Get Hi-Res content
        
        Args:
            resource_types: List of resource types: song, playlist, album
            with_url: Whether to include play URLs (default: True)
            bitrate: Bitrate for play URLs (default: 320, use 1999 for Hi-Res)
            
        Returns:
            List of HiResCategory objects
        """
        url = f"{self.base_url}/basic/hi/res/get"
        biz_content = {
            "resourceTypes": resource_types,
            "withUrl": with_url,
            "bitrate": bitrate
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        categories = []
        for item in data["data"]["hiResVOList"]:
            # Convert snake_case to camelCase for response parsing
            songs = [
                SongDetail(
                    id=song["id"],
                    name=song["name"],
                    duration=song["duration"],
                    album_name=song["albumName"],
                    album_id=song["albumId"],
                    album_artist_id=song["albumArtistId"],
                    album_artist_name=song["albumArtistName"],
                    artist_id=song["artistId"],
                    artist_name=song["artistName"],
                    cover_img_url=song.get("coverImgUrl"),
                    mv_id=song.get("mvId"),
                    play_url=song.get("playUrl"),
                    play_flag=song.get("playFlag", False),
                    download_flag=song.get("downloadFlag", False),
                    pay_play_flag=song.get("payPlayFlag", False),
                    pay_download_flag=song.get("payDownloadFlag", False),
                    vip_flag=song.get("vipFlag", False),
                    vip_play_flag=song.get("vipPlayFlag", False),
                    liked=song.get("liked", False),
                    max_br_level=song.get("maxBrLevel"),
                    pl_level=song.get("plLevel"),
                    dl_level=song.get("dlLevel"),
                    song_size=song.get("songSize"),
                    song_md5=song.get("songMd5"),
                    song_tag=song.get("songTag"),
                    song_fee=song.get("songFee"),
                    br=song.get("br"),
                    audio_flag=song.get("audioFlag"),
                    effects=song.get("effects"),
                    visible=song.get("visible", True)
                )
                for song in item.get("songDetailVoList", [])
            ] if "songDetailVoList" in item else None

            playlists = [
                PlaylistDetail(
                    id=pl["id"],
                    name=pl["name"],
                    describe=pl.get("describe"),
                    cover_img_url=pl.get("coverImgUrl"),
                    creator_nick_name=pl["creatorNickName"],
                    play_count=pl["playCount"],
                    subscribed_count=pl["subscribedCount"],
                    tags=pl.get("tags"),
                    creator_id=pl["creatorId"],
                    create_time=pl["createTime"],
                    subed=pl.get("subed", False),
                    track_count=pl.get("trackCount", 0),
                    special_type=pl.get("specialType")
                )
                for pl in item.get("playlistDetailVoList", [])
            ] if "playlistDetailVoList" in item else None

            albums = [
                AlbumDetail(
                    id=album["id"],
                    name=album["name"],
                    language=album.get("language"),
                    cover_img_url=album.get("coverImgUrl"),
                    company=album.get("company"),
                    trans_name=album.get("transName"),
                    alia_name=album.get("aliaName"),
                    genre=album.get("genre"),
                    artists=[
                        AlbumArtist(id=artist["id"], name=artist["name"])
                        for artist in album.get("artists", [])
                    ],
                    brief_desc=album.get("briefDesc"),
                    description=album.get("description"),
                    publish_time=album.get("publishTime")
                )
                for album in item.get("albumDetailVoList", [])
            ] if "albumDetailVoList" in item else None

            category = HiResCategory(
                category=item["category"],
                song_detail_vo_list=songs,
                playlist_detail_vo_list=playlists,
                album_detail_vo_list=albums
            )
            categories.append(category)
            
        return categories

    def get_song_detail(
        self,
        song_id: str,
        with_url: bool = True,
        bitrate: Optional[int] = None,
        effects: Optional[str] = None,
        quality_flag: bool = False,
        language_flag: bool = False
    ) -> SongDetail:
        """
        Get detailed information about a song
        
        Args:
            song_id: The ID of the song
            with_url: Whether to include play URL (default: True)
            bitrate: Play URL bitrate: 128 (standard), 192 (higher), 320 (extreme), 999 (lossless), 1999 (Hi-Res)
                    Defaults to user's maximum allowed bitrate
            effects: Dolby audio effects, use 'eac3' for car audio
            quality_flag: Whether to return quality information (default: False)
            language_flag: Whether to return language information (default: False)
            
        Returns:
            SongDetail object containing detailed song information
        """
        url = f"{self.base_url}/basic/song/detail/get/v2"
        biz_content = {
            "songId": song_id,
            "withUrl": with_url
        }
        
        if bitrate:
            biz_content["bitrate"] = bitrate
        if effects:
            biz_content["effects"] = effects
        if quality_flag:
            biz_content["qualityFlag"] = quality_flag
        if language_flag:
            biz_content["languageFlag"] = language_flag
            
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        logger.info(f"Getting song detail with URL: {url}")
        response = requests.get(url)
        logger.info(f"Response: {response.json()}")
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
        
        song = data.get("data")
        if not song:
            raise Exception("No song data returned from API")

        # Parse FreeTrail if available
        free_trail = None
        if "freeTrail" in song and song["freeTrail"] is not None:
            free_trail = FreeTrail(
                start=song["freeTrail"].get("start", 0),
                end=song["freeTrail"].get("end", 0)
            )
            
        # Parse Qualities if available          
        qualities = None
        if "qualities" in song and song["qualities"]:
            # Parse qualities according to the defined list
            qualities_mapping = {
                'vividMusic': 'vivid_music',
                'skMusic': 'sk_music', 
                'jyMasterMusic': 'jy_master_music',
                'jyEffectMusic': 'jy_effect_music',
                'sqMusic': 'sq_music',
                'hmusic': 'h_music',
                'mmusic': 'm_music',
                'lmusic': 'l_music'
            }
            qualities_kwargs = {
                qualities_mapping[quality]: quality 
                for quality in song["qualities"]
                if quality in qualities_mapping
            }
            qualities = Qualities(**qualities_kwargs)
            
        # Parse artists
        artists = None
        if "artists" in song and song["artists"] is not None:
            artists = [
                Artist(
                    id=artist.get("id", ""),
                    name=artist.get("name", "")
                )
                for artist in song["artists"]
                if artist is not None
            ]
            
        # Parse full artists
        full_artists = None
        if "fullArtists" in song and song["fullArtists"] is not None:
            full_artists = [
                Artist(
                    id=artist.get("id", ""),
                    name=artist.get("name", "")
                )
                for artist in song["fullArtists"]
                if artist is not None
            ]
              # Get required fields with default values to avoid KeyError
        required_fields = {
            "id": song.get("id", ""),
            "name": song.get("name", ""),
            "duration": song.get("duration", 0),
            "album_name": song.get("albumName", ""),
            "album_id": song.get("albumId", ""),
            "album_artist_id": song.get("albumArtistId", ""),
            "album_artist_name": song.get("albumArtistName", ""),
            "artist_id": song.get("artistId", ""),
            "artist_name": song.get("artistName", "")
        }
        
        # Validate required fields
        missing_fields = [k for k, v in required_fields.items() if not v]
        if missing_fields:
            logger.warning(f"Missing required fields: {missing_fields}")
            
        return SongDetail(
            id=required_fields["id"],
            name=required_fields["name"],
            duration=required_fields["duration"],
            album_name=required_fields["album_name"],
            album_id=required_fields["album_id"],
            album_artist_id=required_fields["album_artist_id"],
            album_artist_name=required_fields["album_artist_name"],
            artist_id=required_fields["artist_id"],
            artist_name=required_fields["artist_name"],
            cover_img_url=song.get("coverImgUrl"),
            mv_id=song.get("mvId"),
            play_url=song.get("playUrl"),
            play_flag=song.get("playFlag", True),
            download_flag=song.get("downloadFlag", True),
            pay_play_flag=song.get("payPlayFlag", False),
            pay_download_flag=song.get("payDownloadFlag", False),
            vip_flag=song.get("vipFlag", False),
            vip_play_flag=song.get("vipPlayFlag", False),
            free_trail_flag=song.get("freeTrailFlag", False),
            free_trail=free_trail,
            liked=song.get("liked", False),
            song_max_br=song.get("songMaxBr"),
            user_max_br=song.get("userMaxBr"),
            max_br_level=song.get("maxBrLevel"),
            pl_level=song.get("plLevel"),
            dl_level=song.get("dlLevel"),
            gain=song.get("gain"),
            peak=song.get("peak"),
            level=song.get("level"),
            song_size=song.get("songSize"),
            song_md5=song.get("songMd5"),
            song_tag=song.get("songTag") if song.get("songTag") is not None else None,
            artists=artists,
            full_artists=full_artists,
            song_fee=song.get("songFee", 0),
            br=song.get("br"),
            audio_flag=song.get("audioFlag"),
            effects=song.get("effects"),
            private_cloud_song=song.get("privateCloudSong", False),
            qualities=qualities,
            language=song.get("language"),
            visible=song.get("visible", True)
        )

    def get_playlist_songs(
        self,
        playlist_id: str,
        limit: int = 5,
        offset: int = 0
    ) -> List[SongDetail]:
        """
        Get list of songs in a playlist
        
        Args:
            playlist_id: ID of the playlist
            limit: Number of results per page
            offset: Result offset for pagination
            
        Returns:
            List of SongDetail objects
        """
        url = f"{self.base_url}/basic/playlist/song/list/get/v2"
        #logger.info(url)
        biz_content = {
            "playlistId": playlist_id,
            "limit": str(limit),
            "offset": str(offset)
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        songs = []
        for item in data["data"]:
            song = SongDetail(
                id=item["id"],
                name=item["name"],
                duration=item["duration"],
                album_name="",
                album_id="",
                album_artist_id="",
                album_artist_name="",
                artist_id="",
                artist_name="",
                cover_img_url=item.get("coverImgUrl"),
                mv_id="",
                play_url=item.get("playUrl"),
                play_flag=item.get("playFlag", True),
                download_flag=item.get("downloadFlag", True),
                pay_play_flag=item.get("payPlayFlag", False),
                pay_download_flag=item.get("payDownloadFlag", False),
                vip_flag=item.get("vipFlag", False),
                vip_play_flag=item.get("vipPlayFlag", False),
                liked=item.get("liked", False),
                max_br_level=item.get("maxBrLevel"),
                pl_level=item.get("plLevel"),
                dl_level=item.get("dlLevel"),
                song_size=item.get("songSize"),
                song_md5=item.get("songMd5"),
                song_tag=item.get("songTag"),
                song_fee=item.get("songFee", 0),
                br=item.get("br"),
                audio_flag=item.get("audioFlag"),
                effects=item.get("effects"),
                visible=item.get("visible", True)
            )
            songs.append(song)
            
        return songs

    def get_album_free_songs(
        self,
        album_id: str,
        limit: int = 5,
        offset: int = 0
    ) -> List[SongDetail]:
        """
        Get list of free songs in an album
        
        Args:
            album_id: ID of the album
            limit: Number of results per page
            offset: Result offset for pagination
            
        Returns:
            List of SongDetail objects
        """
        url = f"{self.base_url}/basic/album/song/list/get/v2"
        biz_content = {
            "albumId": album_id,
            "limit": str(limit),
            "offset": str(offset)
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        songs = []
        for item in data["data"]:
            song = SongDetail(
                id=item["id"],
                name=item["name"],
                duration=item["duration"],
                album_name=item["album"]["name"],
                album_id=item["album"]["id"],
                album_artist_id="",
                album_artist_name="",
                artist_id="",
                artist_name="",
                cover_img_url=item.get("coverImgUrl"),
                mv_id=item.get("mvId"),
                play_url=item.get("playUrl"),
                play_flag=item.get("playFlag", True),
                download_flag=item.get("downloadFlag", True),
                pay_play_flag=item.get("payPlayFlag", False),
                pay_download_flag=item.get("payDownloadFlag", False),
                vip_flag=item.get("vipFlag", False),
                vip_play_flag=item.get("vipPlayFlag", False),
                liked=item.get("liked", False),
                max_br_level=item.get("maxBrLevel"),
                pl_level=item.get("plLevel"),
                dl_level=item.get("dlLevel"),
                song_size=item.get("songSize"),
                song_md5=item.get("songMd5"),
                song_tag=item.get("songTag"),
                song_fee=item.get("songFee", 0),
                br=item.get("br"),
                audio_flag=item.get("audioFlag"),
                effects=item.get("effects"),
                visible=item.get("visible", True)
            )
            songs.append(song)
            
        return songs

    def get_album_vip_songs(
        self,
        album_id: str,
        limit: int = 5,
        offset: int = 0
    ) -> List[SongDetail]:
        """
        Get list of VIP songs in an album
        
        Args:
            album_id: ID of the album
            limit: Number of results per page
            offset: Result offset for pagination
            
        Returns:
            List of SongDetail objects
        """
        url = f"{self.base_url}/basic/album/song/list/get/v3"
        biz_content = {
            "albumId": album_id,
            "limit": str(limit),
            "offset": str(offset)
        }
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        songs = []
        for item in data["data"]["records"]:
            song = SongDetail(
                id=item["id"],
                name=item["name"],
                duration=item["duration"],
                album_name=item["album"]["name"],
                album_id=item["album"]["id"],
                album_artist_id="",
                album_artist_name="",
                artist_id="",
                artist_name="",
                cover_img_url=item.get("coverImgUrl"),
                mv_id=item.get("mvId"),
                play_url=item.get("playUrl"),
                play_flag=item.get("playFlag", True),
                download_flag=item.get("downloadFlag", True),
                pay_play_flag=item.get("payPlayFlag", False),
                pay_download_flag=item.get("payDownloadFlag", False),
                vip_flag=item.get("vipFlag", False),
                vip_play_flag=item.get("vipPlayFlag", False),
                liked=item.get("liked", False),
                max_br_level=item.get("maxBrLevel"),
                pl_level=item.get("plLevel"),
                dl_level=item.get("dlLevel"),
                song_size=item.get("songSize"),
                song_md5=item.get("songMd5"),
                song_tag=item.get("songTag"),
                song_fee=item.get("songFee", 0),
                br=item.get("br"),
                audio_flag=item.get("audioFlag"),
                effects=item.get("effects"),
                visible=item.get("visible", True)
            )
            songs.append(song)
            
        return songs

    def get_batch_song_urls(
        self,
        song_ids: List[str],
        bitrate: Optional[int] = 320,
        effects: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量获取歌曲播放URL
        
        Args:
            song_ids: 歌曲ID列表，最多500个
            bitrate: 比特率：128（标准）、320（极高）、999（无损）、1999（Hi-Res）。默认320
            effects: 支持杜比的音效：eac3、ac4
            
        Returns:
            Dict containing song URLs and related information
        """
        if len(song_ids) > 500:
            raise ValueError("最多支持500个歌曲ID")
            
        url = f"{self.base_url}/basic/batch/song/playurl/get"
        biz_content = {
            "songIds": song_ids
        }
        if bitrate:
            biz_content["bitrate"] = bitrate
        if effects:
            biz_content["effects"] = effects
            
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        return data["data"]

    def like_song(
        self,
        song_id: str,
        is_like: bool = True
    ) -> Dict[str, Any]:
        """
        添加或删除红心歌曲
        
        Args:
            song_id: 歌曲ID
            is_like: True表示添加红心，False表示取消红心
            
        Returns:
            Dict containing operation result
        """
        url = f"{self.base_url}/basic/playlist/song/like/v2"
        biz_content = {
            "songId": song_id,
            "isLike": is_like
        }
        
        params = self._generate_signature(biz_content)
        url = f'{url}?' + "&".join([f"{k}={v}" for k, v in params.items()])
        
        #logger.info(url)
        response = requests.get(url)
        #logger.info(response.json())
        response.raise_for_status()
        
        data = response.json()
        if data["code"] != 200:
            raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
            
        return data["data"]
