#!/usr/bin/env python3
"""
POI搜索调试脚本
用于诊断为什么搜索上海POI返回济南结果的问题
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config import get_settings
from src.agents.services.amap_service import AmapService
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_poi_search():
    """测试POI搜索功能"""
    print("🔍 开始POI搜索调试...")
    
    try:
        # 获取配置
        settings = get_settings()
        print(f"✅ 配置加载成功")
        print(f"   高德API密钥: {settings.amap_api_key[:10]}...")
        
        # 创建AmapService实例
        amap_service = AmapService()
        print(f"✅ AmapService实例创建成功")
        
        # 测试1: 搜索上海景点
        print(f"\n🏛️ 测试1: 搜索上海景点...")
        shanghai_pois = await amap_service.search_poi(
            keywords="景点",
            city="上海",
            page_size=5
        )
        
        print(f"✅ 上海景点搜索结果 ({len(shanghai_pois)}个):")
        for i, poi in enumerate(shanghai_pois):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            location = poi.get("location", "未知坐标")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            print(f"     坐标: {location}")
            print()
        
        # 测试2: 搜索上海现代都市景点
        print(f"\n🏙️ 测试2: 搜索上海现代都市景点...")
        modern_pois = await amap_service.search_poi(
            keywords="现代都市 景点",
            city="上海",
            page_size=5
        )
        
        print(f"✅ 上海现代都市景点搜索结果 ({len(modern_pois)}个):")
        for i, poi in enumerate(modern_pois):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            location = poi.get("location", "未知坐标")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            print(f"     坐标: {location}")
            print()
        
        # 测试3: 对比搜索济南景点
        print(f"\n🏛️ 测试3: 对比搜索济南景点...")
        jinan_pois = await amap_service.search_poi(
            keywords="景点",
            city="济南",
            page_size=5
        )
        
        print(f"✅ 济南景点搜索结果 ({len(jinan_pois)}个):")
        for i, poi in enumerate(jinan_pois):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            location = poi.get("location", "未知坐标")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            print(f"     坐标: {location}")
            print()
            
            # 检查是否包含"都市现代园林展区"
            if "都市现代园林展区" in name:
                print(f"🎯 找到问题POI: {name}")
                print(f"   这个POI确实在济南搜索结果中！")
        
        # 测试4: 直接搜索"都市现代园林展区"
        print(f"\n🎯 测试4: 直接搜索'都市现代园林展区'...")
        target_pois = await amap_service.search_poi(
            keywords="都市现代园林展区",
            city="上海",
            page_size=5
        )
        
        print(f"✅ 直接搜索结果 ({len(target_pois)}个):")
        for i, poi in enumerate(target_pois):
            name = poi.get("name", "未知")
            address = poi.get("address", "未知地址")
            location = poi.get("location", "未知坐标")
            print(f"  {i+1}. {name}")
            print(f"     地址: {address}")
            print(f"     坐标: {location}")
            print()
        
    except Exception as e:
        print(f"❌ POI搜索测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_poi_search())
