#!/usr/bin/env python3
"""
Agent系统集成测试

验证AutoPilot AI Agent系统的完整功能，包括：
- 基础Agent功能测试
- 双模型（基础/思考）对比测试
- 对话历史管理测试
- 性能基准测试

运行方式：
python tests/integration/test_agent_system.py
"""
import os
import asyncio
import json
import sys
from pathlib import Path

# 确保能正确导入src模块 (从integration目录需要向上两级)
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 设置环境变量（使用用户提供的智谱AI配置）
# 只在环境变量未设置时才设置，避免覆盖用户配置
if not os.environ.get("REASONING_LLM_MODEL"):
    # 思考模型：用于复杂推理和分析
    os.environ["REASONING_LLM_MODEL"] = "glm-z1-flash"
    os.environ["REASONING_LLM_API_KEY"] = "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
    os.environ["REASONING_LLM_BASE_URL"] = "https://open.bigmodel.cn/api/paas/v4/"

if not os.environ.get("BASIC_LLM_MODEL"):
    # 基础模型：用于一般对话和简单任务
    os.environ["BASIC_LLM_MODEL"] = "glm-4-flash"
    os.environ["BASIC_LLM_API_KEY"] = "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
    os.environ["BASIC_LLM_BASE_URL"] = "https://open.bigmodel.cn/api/paas/v4/"

# 其他配置
if not os.environ.get("LOG_LEVEL"):
    os.environ["LOG_LEVEL"] = "INFO"
if not os.environ.get("LOG_FORMAT"):
    os.environ["LOG_FORMAT"] = "text"


async def test_basic_functionality():
    """测试基本功能"""
    print("🚀 AutoPilot AI Agent 基础功能测试")
    print("=" * 50)
    
    try:
        # 导入agent模块
        from src.agents.simple_assistant import create_simple_assistant, quick_chat
        
        print("✅ 模块导入成功")
        
        # 测试1: 快速对话
        print("\n📞 测试1: 快速对话")
        print("-" * 30)
        
        user_message = "你好！请简单介绍一下你自己。"
        print(f"用户: {user_message}")
        
        reply = await quick_chat(user_message, "test_agent")
        print(f"助手: {reply}")
        
        print("✅ 快速对话测试成功！")
        
        # 测试2: 创建专门的助手
        print("\n🤖 测试2: 创建专门助手")
        print("-" * 30)
        
        assistant = await create_simple_assistant(
            name="专业助手",
            llm_role="basic",
            system_message="你是一个专业的Python编程助手，专门帮助用户解决编程问题。"
        )
        
        # 进行编程相关对话
        programming_question = "如何在Python中实现异步编程？"
        print(f"用户: {programming_question}")
        
        result = await assistant.chat(programming_question)
        print(f"助手: {result['assistant_reply']}")
        
        # 继续对话
        followup = "能举个具体的例子吗？"
        print(f"用户: {followup}")
        
        result2 = await assistant.chat(followup)
        print(f"助手: {result2['assistant_reply']}")
        
        print("✅ 专门助手测试成功！")
        
        # 测试3: 对话摘要
        print("\n📊 测试3: 对话摘要")
        print("-" * 30)
        
        summary = assistant.get_conversation_summary()
        print(f"对话摘要:")
        print(f"- 助手名称: {summary['agent_name']}")
        print(f"- 总轮次: {summary['total_exchanges']}")
        print(f"- 总消息数: {summary['total_messages']}")
        print(f"- 用户平均消息长度: {summary['average_user_message_length']:.1f} 字符")
        print(f"- 助手平均回复长度: {summary['average_assistant_message_length']:.1f} 字符")
        
        print("✅ 对话摘要测试成功！")
        
        # 测试4: 重置对话
        print("\n🔄 测试4: 重置对话")
        print("-" * 30)
        
        await assistant.reset_conversation()
        new_summary = assistant.get_conversation_summary()
        print(f"重置后对话数: {new_summary['total_messages']}")
        
        print("✅ 重置对话测试成功！")
        
        print("\n🎉 所有测试通过！AutoPilot AI Agent 系统工作正常！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def test_basic_model():
    """测试基础模型Agent"""
    print("\n💬 测试基础模型Agent (glm-4-flash)")
    print("=" * 50)
    
    try:
        from src.agents.simple_assistant import create_simple_assistant
        
        # 创建使用基础模型的助手
        basic_assistant = await create_simple_assistant(
            name="基础助手",
            llm_role="basic",
            system_message="你是一个友好的AI助手，能够进行日常对话和回答一般问题。"
        )
        
        # 测试简单对话
        simple_questions = [
            "你好！你能做什么？",
            "今天天气怎么样？",
            "请推荐一本好书"
        ]
        
        for i, question in enumerate(simple_questions, 1):
            print(f"\n问题{i}: {question}")
            result = await basic_assistant.chat(question)
            print(f"基础助手: {result['assistant_reply'][:200]}{'...' if len(result['assistant_reply']) > 200 else ''}")
            
            # 显示模型信息
            if i == 1:  # 只在第一次显示
                print(f"使用模型: {result.get('model_used', '未知')}")
                if result.get('tokens_used'):
                    print(f"Token使用: {result['tokens_used']['total_tokens']}")
        
        print("\n✅ 基础模型测试成功！")
        
    except Exception as e:
        print(f"❌ 基础模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def test_reasoning_agent():
    """测试思考模型Agent"""
    print("\n🧠 测试思考模型Agent (glm-z1-flash)")
    print("=" * 50)
    
    try:
        from src.agents.simple_assistant import create_simple_assistant
        
        # 创建使用思考模型的助手
        reasoning_assistant = await create_simple_assistant(
            name="思考助手",
            llm_role="reasoning",
            system_message="你是一个高级AI助手，善于深度思考和分析问题。请仔细分析用户的问题并给出深入的回答。"
        )
        
        # 测试复杂问题
        complex_question = "请分析一下人工智能在未来10年可能对软件开发行业产生的影响，包括机遇和挑战。"
        print(f"用户: {complex_question}")
        
        result = await reasoning_assistant.chat(complex_question)
        # 显示前300字符，避免输出过长
        reply_preview = result['assistant_reply'][:300] + "..." if len(result['assistant_reply']) > 300 else result['assistant_reply']
        print(f"思考助手: {reply_preview}")
        
        print(f"\n使用模型: {result.get('model_used', '未知')}")
        
        # 显示token使用情况
        if result.get('tokens_used'):
            print(f"Token使用情况: {result['tokens_used']}")
        
        print("\n✅ 思考模型测试成功！")
        
    except Exception as e:
        print(f"❌ 思考模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def test_model_comparison():
    """对比测试两种模型的性能"""
    print("\n⚖️  模型对比测试")
    print("=" * 50)
    
    try:
        from src.agents.simple_assistant import create_simple_assistant
        import time
        
        # 测试问题
        test_question = "请解释什么是机器学习，并举一个简单的例子。"
        
        # 基础模型测试
        print("基础模型 (glm-4-flash) 测试:")
        basic_assistant = await create_simple_assistant("基础对比", "basic")
        
        start_time = time.time()
        basic_result = await basic_assistant.chat(test_question)
        basic_time = time.time() - start_time
        
        print(f"回复长度: {len(basic_result['assistant_reply'])} 字符")
        print(f"响应时间: {basic_time:.2f} 秒")
        if basic_result.get('tokens_used'):
            print(f"Token使用: {basic_result['tokens_used']['total_tokens']}")
        
        # 思考模型测试
        print("\n思考模型 (glm-z1-flash) 测试:")
        reasoning_assistant = await create_simple_assistant("思考对比", "reasoning")
        
        start_time = time.time()
        reasoning_result = await reasoning_assistant.chat(test_question)
        reasoning_time = time.time() - start_time
        
        print(f"回复长度: {len(reasoning_result['assistant_reply'])} 字符")
        print(f"响应时间: {reasoning_time:.2f} 秒")
        if reasoning_result.get('tokens_used'):
            print(f"Token使用: {reasoning_result['tokens_used']['total_tokens']}")
        
        # 对比总结
        print(f"\n📊 对比总结:")
        print(f"基础模型更快: {basic_time < reasoning_time}")
        print(f"思考模型回复更详细: {len(reasoning_result['assistant_reply']) > len(basic_result['assistant_reply'])}")
        
        print("\n✅ 模型对比测试完成！")
        
    except Exception as e:
        print(f"❌ 模型对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def main():
    """主测试函数"""
    print("🎯 AutoPilot AI Agent 综合测试")
    print("=" * 60)
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 运行测试套件
    tests = [
        ("基础功能", test_basic_functionality),
        ("基础模型", test_basic_model),
        ("思考模型", test_reasoning_agent),
        ("模型对比", test_model_comparison)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name}测试 {'='*20}")
        try:
            success = await test_func()
            results[test_name] = "✅ 成功" if success else "❌ 失败"
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results[test_name] = f"❌ 异常: {str(e)[:50]}"
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print("🎯 Agent系统测试总结")
    print(f"{'='*60}")
    
    for test_name, result in results.items():
        print(f"{test_name}测试: {result}")
    
    success_count = len([r for r in results.values() if "✅" in r])
    total_count = len(results)
    
    if success_count == total_count:
        print(f"\n🌟 恭喜！所有Agent测试都成功通过！({success_count}/{total_count})")
        print("系统已经可以正常工作，可以开始开发更复杂的Agent了！")
        print("\n💡 下一步建议：")
        print("- 运行 python tests/integration/test_python_expert_integration.py 测试专家Agent")
        print("- 运行 pytest tests/unit/ 进行完整的单元测试")
        print("- 查看 doc/测试指南.md 了解更多测试方法")
    elif success_count > 0:
        print(f"\n⚠️  部分测试通过 ({success_count}/{total_count})")
        print("请检查失败的测试项目并进行调试")
    else:
        print(f"\n❌ 所有测试失败，请检查配置和依赖")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    asyncio.run(main()) 