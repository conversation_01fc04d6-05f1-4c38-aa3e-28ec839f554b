#!/usr/bin/env python3
"""
测试改造后的TravelPlannerAgent基本功能
"""
import asyncio
import sys
import json
import uuid
import traceback
from unittest.mock import AsyncMock, MagicMock, patch

# 添加项目路径
sys.path.append('.')

from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import TravelPlanRequest
from src.core.logger import get_logger

logger = get_logger(__name__)

class MockUser:
    def __init__(self, user_id):
        self.id = user_id
        self.nickname = "测试用户"
        self.status = "ACTIVE"

class MockMemory:
    def __init__(self, content, keywords, confidence=0.8):
        self.content = content
        self.keywords = keywords
        self.confidence_score = confidence
        self.memory_type = "preference"

class MockSummary:
    def __init__(self):
        self.interests = '["历史", "文化", "美食"]'
        self.travel_style = "文化深度游"
        self.budget_preference = "中等"

async def test_agent_basic_functionality():
    """测试Agent基本功能"""
    print("🔍 测试TravelPlannerAgent基本功能")
    print("=" * 60)
    
    # Mock数据库查询
    mock_user = MockUser("test_user_001")
    mock_memories = [
        MockMemory("用户喜欢历史文化景点", ["历史", "文化"], 0.9),
        MockMemory("用户喜欢当地美食", ["美食", "川菜"], 0.8),
    ]
    mock_summary = MockSummary()
    
    # 创建Agent
    try:
        agent = TravelPlannerAgent()
        print("✅ Agent初始化成功")
    except Exception as e:
        print(f"❌ Agent初始化失败: {str(e)}")
        return False
    
    # 测试用户画像获取功能
    print("\n测试用户画像获取...")
    
    # 配置所有必要的Mock
    patches = [
        patch('src.agents.travel_planner_agent.get_db_cursor'),
        patch('src.agents.travel_planner_agent.user_crud'),
        patch('src.agents.travel_planner_agent.user_memory_crud'),
        patch('src.agents.travel_planner_agent.user_summary_crud'),
        patch('src.agents.travel_planner_agent.itinerary_crud'),
        patch('src.agents.travel_planner_agent.ai_planning_session_crud'),
    ]
    
    events_count = 0
    user_profile_events = 0
    thinking_events = 0
    error_occurred = False
    
    with patches[0] as mock_cursor, \
         patches[1] as mock_user_crud, \
         patches[2] as mock_memory_crud, \
         patches[3] as mock_summary_crud, \
         patches[4] as mock_itinerary_crud, \
         patches[5] as mock_ai_planning_crud:
        
        # 配置Mock返回值
        mock_user_crud.get = AsyncMock(return_value=mock_user)
        mock_memory_crud.get_high_confidence_memories = AsyncMock(return_value=mock_memories)
        mock_summary_crud.get_by_user = AsyncMock(return_value=mock_summary)
        mock_itinerary_crud.get_by_user = AsyncMock(return_value=[])
        mock_ai_planning_crud.create = AsyncMock(return_value=None)
        mock_memory_crud.create = AsyncMock(return_value=None)
        mock_summary_crud.update = AsyncMock(return_value=None)
        mock_summary_crud.create = AsyncMock(return_value=None)
        
        # 模拟异步上下文管理器
        mock_cursor.return_value.__aenter__ = AsyncMock(return_value=MagicMock())
        mock_cursor.return_value.__aexit__ = AsyncMock(return_value=None)
        
        # 创建测试请求
        request = TravelPlanRequest(
            user_id="test_user_001", 
            query="我想去西安玩3天，看看历史文化景点"
        )
        
        # 执行测试
        try:
            async for event in agent.plan_travel(request):
                events_count += 1
                
                if event.event_type.value == "thinking_step":
                    thinking_events += 1
                    content = event.payload.get('content', '')
                    print(f"   💭 {content}")
                    
                    if "用户完整画像" in content or "条记忆" in content:
                        user_profile_events += 1
                        print(f"   ✅ 用户画像事件: {content}")
                
                elif event.event_type.value == "final_itinerary":
                    itinerary = event.payload
                    title = itinerary.get('summary', {}).get('title', '未知')
                    print(f"   ✅ 最终行程: {title}")
                
                # 限制事件数量，避免无限循环
                if events_count > 50:
                    break
                    
        except Exception as e:
            print(f"   ❌ 执行过程中出现错误: {str(e)}")
            error_occurred = True
            traceback.print_exc()
    
    print(f"\n📊 测试结果:")
    print(f"   总事件数: {events_count}")
    print(f"   思考事件数: {thinking_events}")
    print(f"   用户画像事件数: {user_profile_events}")
    print(f"   是否有错误: {'是' if error_occurred else '否'}")
    
    success = events_count > 0 and user_profile_events > 0 and not error_occurred
    return success

async def test_preference_extraction():
    """测试偏好提取功能"""
    print("\n🧠 测试偏好提取功能")
    print("-" * 40)
    
    agent = TravelPlannerAgent()
    
    # 测试记忆偏好提取
    mock_memories = [
        MockMemory("用户特别喜欢博物馆和历史文化景点", ["历史", "文化"], 0.9),
        MockMemory("用户经常选择经济实惠的餐厅", ["经济", "餐厅"], 0.8),
        MockMemory("用户对自然风景很感兴趣", ["自然", "风景"], 0.7),
    ]
    
    preferences = agent._extract_preferences_from_memories(mock_memories)
    print(f"✅ 从记忆中提取的偏好: {preferences}")
    
    # 测试行程偏好提取
    mock_itineraries = [
        type('MockItinerary', (), {'destination_city': '北京', 'days': 3}),
        type('MockItinerary', (), {'destination_city': '上海', 'days': 2}),
        type('MockItinerary', (), {'destination_city': '杭州', 'days': 4}),
    ]
    
    itinerary_prefs = agent._extract_preferences_from_itineraries(mock_itineraries)
    print(f"✅ 从历史行程中提取的偏好: {itinerary_prefs}")
    
    return True

async def main():
    """主测试函数"""
    print("🧪 AutoPilot AI Agent改造功能测试")
    print("=" * 60)
    
    success1 = await test_agent_basic_functionality()
    success2 = await test_preference_extraction()
    
    overall_success = success1 and success2
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 Agent改造功能测试通过！")
        print("✅ 用户画像获取功能正常")
        print("✅ 偏好提取功能正常") 
        print("✅ 事件流生成正常")
        return 0
    else:
        print("❌ Agent改造功能测试失败")
        print("⚠️ 请检查错误信息并修复问题")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⛔ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"💥 程序异常: {str(e)}")
        traceback.print_exc()
        sys.exit(1) 