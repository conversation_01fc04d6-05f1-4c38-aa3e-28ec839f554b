#!/usr/bin/env python3
"""
旅行规划Agent测试脚本

测试旅行规划Agent的各项功能，包括意图理解、工具调用、数据库操作等。
"""
import asyncio
import sys
import os
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.travel_planner import TravelPlanRequest
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.database.mongodb_client import get_mongo_client, close_mongo_client
from src.tools.amap_mcp_client import get_amap_client
from src.memory.memory_manager import get_memory_manager
from src.core.logger import get_logger
from src.core.config import get_settings

logger = get_logger("test_travel_planner")


class TravelPlannerTester:
    """旅行规划Agent测试器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = logger
        
    async def test_configuration(self):
        """测试配置加载"""
        print("🔧 测试配置加载...")
        
        try:
            # 测试基本配置
            assert self.settings.app.name == "AutoPilot AI"
            assert self.settings.mongodb_conf.host == "***********"
            assert self.settings.mongodb_conf.port == 27017
            
            print("✅ 配置加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 配置加载失败: {str(e)}")
            return False
            
    async def test_database_connection(self):
        """测试数据库连接"""
        print("🗄️ 测试数据库连接...")
        
        try:
            # 测试MongoDB连接
            mongo_client = await get_mongo_client()
            
            # 测试基本操作
            test_user = {
                "user_id": "test_user_001",
                "username": "测试用户",
                "preferences": {"budget": "中等", "style": "休闲"},
                "tags": ["文化", "美食"]
            }
            
            user_id = await mongo_client.create_user(test_user)
            print(f"✅ 用户创建成功: {user_id}")
            
            # 获取用户
            user = await mongo_client.get_user("test_user_001")
            assert user is not None
            print("✅ 用户查询成功")
            
            print("✅ 数据库连接测试成功")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接测试失败: {str(e)}")
            return False
            
    async def test_amap_tools(self):
        """测试高德地图工具"""
        print("🗺️ 测试高德地图工具...")
        
        try:
            amap_client = await get_amap_client()
            
            # 测试地理编码
            geo_result = await amap_client.maps_geo("上海市")
            print(f"✅ 地理编码测试成功: {geo_result}")
            
            # 测试天气查询
            weather_result = await amap_client.maps_weather("上海")
            print(f"✅ 天气查询测试成功: {weather_result}")
            
            # 测试POI搜索
            poi_result = await amap_client.maps_text_search("景点", "上海", page=1, offset=5)
            print(f"✅ POI搜索测试成功: 找到 {len(poi_result.get('pois', []))} 个结果")
            
            print("✅ 高德地图工具测试成功")
            return True
            
        except Exception as e:
            print(f"❌ 高德地图工具测试失败: {str(e)}")
            return False
            
    async def test_memory_system(self):
        """测试记忆系统"""
        print("🧠 测试记忆系统...")
        
        try:
            memory_manager = get_memory_manager()
            
            # 存储测试记忆
            memory_id = await memory_manager.store_memory(
                user_id="test_user_001",
                memory_type="preference",
                content="用户喜欢文化景点和美食",
                context={"destination": "上海", "preferences": ["文化", "美食"]},
                importance_score=2.0,
                tags=["文化", "美食"]
            )
            print(f"✅ 记忆存储成功: {memory_id}")
            
            # 获取相关记忆
            memories = await memory_manager.get_relevant_memories(
                user_id="test_user_001",
                query_context={"destination": "上海", "preferences": ["文化"]},
                limit=5
            )
            print(f"✅ 记忆检索成功: 找到 {len(memories)} 条相关记忆")
            
            print("✅ 记忆系统测试成功")
            return True
            
        except Exception as e:
            print(f"❌ 记忆系统测试失败: {str(e)}")
            return False
            
    async def test_travel_planning_agent(self):
        """测试旅行规划Agent"""
        print("🤖 测试旅行规划Agent...")
        
        try:
            agent = TravelPlannerAgent()
            
            # 创建测试请求
            request = TravelPlanRequest(
                user_id="test_user_001",
                query="我想去上海玩3天，喜欢文化景点和美食，预算3000元左右"
            )
            
            print("🚀 开始规划测试...")
            
            # 收集所有事件
            events = []
            async for event in agent.plan_travel(request):
                events.append(event)
                print(f"📝 事件: {event.event_type.value} - {event.payload}")
                
                # 如果是最终行程，验证结果
                if event.event_type.value == "final_itinerary":
                    itinerary = event.payload
                    assert "summary" in itinerary
                    assert "daily_plans" in itinerary
                    print("✅ 最终行程生成成功")
                    
            print(f"✅ 规划完成，共生成 {len(events)} 个事件")
            print("✅ 旅行规划Agent测试成功")
            return True
            
        except Exception as e:
            print(f"❌ 旅行规划Agent测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
    async def test_api_endpoints(self):
        """测试API端点"""
        print("🌐 测试API端点...")
        
        try:
            import httpx
            
            # 测试健康检查
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8000/health")
                if response.status_code == 200:
                    print("✅ 健康检查端点正常")
                else:
                    print(f"⚠️ 健康检查端点返回状态码: {response.status_code}")
                    
            print("✅ API端点测试完成")
            return True
            
        except Exception as e:
            print(f"❌ API端点测试失败: {str(e)}")
            return False
            
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始旅行规划Agent系统测试")
        print("=" * 50)
        
        test_results = []
        
        # 运行各项测试
        tests = [
            ("配置加载", self.test_configuration),
            ("数据库连接", self.test_database_connection),
            ("高德地图工具", self.test_amap_tools),
            ("记忆系统", self.test_memory_system),
            ("旅行规划Agent", self.test_travel_planning_agent),
            ("API端点", self.test_api_endpoints),
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 测试项目: {test_name}")
            print("-" * 30)
            
            try:
                result = await test_func()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ 测试 {test_name} 发生异常: {str(e)}")
                test_results.append((test_name, False))
                
        # 输出测试总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结")
        print("=" * 50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:<20} {status}")
            if result:
                passed += 1
                
        print("-" * 50)
        print(f"总计: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常。")
        else:
            print("⚠️ 部分测试失败，请检查相关配置和服务。")
            
        return passed == total
        
    async def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        try:
            # 关闭数据库连接
            await close_mongo_client()
            print("✅ 数据库连接已关闭")
            
        except Exception as e:
            print(f"⚠️ 清理过程中发生错误: {str(e)}")


async def main():
    """主函数"""
    tester = TravelPlannerTester()
    
    try:
        # 运行所有测试
        success = await tester.run_all_tests()
        
        # 清理
        await tester.cleanup()
        
        # 退出
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        await tester.cleanup()
        sys.exit(1)
        
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {str(e)}")
        import traceback
        traceback.print_exc()
        await tester.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 启动旅行规划Agent系统测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 运行测试
    asyncio.run(main())
