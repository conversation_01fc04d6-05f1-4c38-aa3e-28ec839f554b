/**
 * Playwright测试脚本 - 旅行规划界面功能验证
 * 
 * 测试目标：
 * 1. 验证两阶段工作流程（阶段A意图分析 → 阶段B ICP迭代规划）
 * 2. 检查V3 API架构的正确使用
 * 3. 验证前端界面按照重构文档规范显示
 * 4. 测试详细分析结果显示和立即规划按钮功能
 */

const { test, expect } = require('@playwright/test');

test.describe('旅行规划界面功能测试', () => {
    let page;

    test.beforeEach(async ({ browser }) => {
        page = await browser.newPage();
        
        // 启动本地服务器（如果需要）
        console.log('访问旅行规划页面...');
        await page.goto('http://localhost:8000/static/travel_planner_v3_refactored.html');
        
        // 等待页面加载完成
        await page.waitForLoadState('networkidle');
    });

    test('页面基本结构验证', async () => {
        console.log('验证页面基本结构...');
        
        // 验证页面标题
        await expect(page).toHaveTitle(/AutoPilot AI - 旅行规划系统 V3.0 重构版/);
        
        // 验证左侧分析面板
        const leftPanel = page.locator('.col-md-6').first();
        await expect(leftPanel.locator('h4')).toContainText('AI智能分析');
        await expect(leftPanel.locator('small')).toContainText('透明化展示AI分析思考过程');
        
        // 验证右侧规划面板
        const rightPanel = page.locator('.col-md-6').last();
        await expect(rightPanel.locator('h4')).toContainText('智能规划结果');
        await expect(rightPanel.locator('small')).toContainText('基于AI分析生成的详细行程');
        
        // 验证车辆信息面板
        await expect(page.locator('#vehicleInfoPanel')).toBeVisible();
        await expect(page.locator('#vehicleInfoPanel h6')).toContainText('车辆信息');
        
        // 验证控制按钮
        await expect(page.locator('#startAnalysisBtn')).toBeVisible();
        await expect(page.locator('#startAnalysisBtn')).toContainText('开始分析');
        
        // 验证立即规划按钮（初始应该隐藏）
        await expect(page.locator('#startPlanningBtn')).toBeHidden();
    });

    test('车辆信息表单验证', async () => {
        console.log('验证车辆信息表单...');
        
        // 验证车型选择
        const vehicleModel = page.locator('#vehicleModel');
        await expect(vehicleModel).toBeVisible();
        await vehicleModel.selectOption('特斯拉Model 3');
        
        // 验证续航里程输入
        const vehicleRange = page.locator('#vehicleRange');
        await expect(vehicleRange).toBeVisible();
        await vehicleRange.fill('500');
        
        // 验证充电类型选择
        const chargingType = page.locator('#chargingType');
        await expect(chargingType).toBeVisible();
        await chargingType.selectOption('超充');
        
        // 验证运行模式选择
        const drivingMode = page.locator('#drivingMode');
        await expect(drivingMode).toBeVisible();
        await drivingMode.selectOption('interactive');
    });

    test('用户输入和分析流程测试', async () => {
        console.log('测试用户输入和分析流程...');
        
        // 填写旅行需求
        const userQuery = page.locator('#userQuery');
        await userQuery.fill('我想去北京玩3天，喜欢历史文化景点，需要自驾出行，预算5000元');
        
        // 填写车辆信息
        await page.locator('#vehicleModel').selectOption('特斯拉Model 3');
        await page.locator('#vehicleRange').fill('500');
        await page.locator('#chargingType').selectOption('超充');
        
        // 点击开始分析按钮
        await page.locator('#startAnalysisBtn').click();
        
        // 验证分析开始后的状态变化
        await expect(page.locator('#progressText')).not.toContainText('等待开始分析...');
        
        // 等待分析步骤显示
        await page.waitForTimeout(2000);
        
        // 验证分析步骤容器
        const analysisSteps = page.locator('#analysisSteps');
        await expect(analysisSteps).toBeVisible();
        
        // 检查是否有分析步骤显示
        const analysisStepElements = page.locator('.analysis-step');
        const stepCount = await analysisStepElements.count();
        console.log(`发现 ${stepCount} 个分析步骤`);
        
        // 等待一段时间让分析完成
        await page.waitForTimeout(5000);
        
        // 验证立即规划按钮是否出现
        const startPlanningBtn = page.locator('#startPlanningBtn');
        if (await startPlanningBtn.isVisible()) {
            console.log('立即规划按钮已显示，分析阶段完成');
            await expect(startPlanningBtn).toBeVisible();
        } else {
            console.log('立即规划按钮未显示，可能分析还在进行中或有问题');
        }
    });

    test('API调用验证', async () => {
        console.log('验证API调用...');
        
        // 监听网络请求
        const apiRequests = [];
        page.on('request', request => {
            if (request.url().includes('/api/')) {
                apiRequests.push({
                    url: request.url(),
                    method: request.method(),
                    headers: request.headers()
                });
                console.log(`API请求: ${request.method()} ${request.url()}`);
            }
        });
        
        // 监听SSE连接
        const sseConnections = [];
        page.on('request', request => {
            if (request.headers()['accept'] && request.headers()['accept'].includes('text/event-stream')) {
                sseConnections.push(request.url());
                console.log(`SSE连接: ${request.url()}`);
            }
        });
        
        // 填写表单并开始分析
        await page.locator('#userQuery').fill('我想去上海玩2天，喜欢现代建筑和美食');
        await page.locator('#vehicleModel').selectOption('比亚迪汉');
        await page.locator('#vehicleRange').fill('600');
        
        await page.locator('#startAnalysisBtn').click();
        
        // 等待API调用
        await page.waitForTimeout(3000);
        
        // 验证是否使用了V3 API路由
        const v3ApiCalls = apiRequests.filter(req => req.url.includes('/api/v3/'));
        console.log(`V3 API调用数量: ${v3ApiCalls.length}`);
        
        if (v3ApiCalls.length > 0) {
            console.log('检测到V3 API调用:');
            v3ApiCalls.forEach(call => {
                console.log(`  - ${call.method} ${call.url}`);
            });
        } else {
            console.log('警告: 未检测到V3 API调用，可能使用了旧版路由');
        }
        
        // 验证SSE连接
        if (sseConnections.length > 0) {
            console.log('检测到SSE连接:');
            sseConnections.forEach(url => {
                console.log(`  - ${url}`);
            });
        } else {
            console.log('警告: 未检测到SSE连接');
        }
    });

    test('两阶段工作流程验证', async () => {
        console.log('验证两阶段工作流程...');
        
        // 阶段A: 意图分析
        await page.locator('#userQuery').fill('我想去杭州玩3天，喜欢自然风光和传统文化');
        await page.locator('#vehicleModel').selectOption('蔚来ES6');
        await page.locator('#vehicleRange').fill('450');
        
        await page.locator('#startAnalysisBtn').click();
        
        // 等待分析阶段完成
        await page.waitForTimeout(8000);
        
        // 验证分析结果是否显示
        const analysisSteps = page.locator('.analysis-step');
        const completedSteps = page.locator('.analysis-step.completed');
        
        const totalSteps = await analysisSteps.count();
        const completedCount = await completedSteps.count();
        
        console.log(`总分析步骤: ${totalSteps}, 已完成: ${completedCount}`);
        
        // 验证立即规划按钮状态
        const startPlanningBtn = page.locator('#startPlanningBtn');
        if (await startPlanningBtn.isVisible()) {
            console.log('阶段A完成，进入阶段B');
            
            // 点击立即规划按钮
            await startPlanningBtn.click();
            
            // 验证规划阶段开始
            await page.waitForTimeout(3000);
            
            // 检查右侧规划面板是否有内容更新
            const itineraryView = page.locator('#itineraryView');
            const itineraryContent = await itineraryView.textContent();
            
            if (!itineraryContent.includes('等待规划开始...')) {
                console.log('阶段B已开始，规划内容正在生成');
            } else {
                console.log('阶段B可能未正确启动');
            }
        } else {
            console.log('阶段A可能未完成，立即规划按钮未显示');
        }
    });

    test('错误处理验证', async () => {
        console.log('验证错误处理...');
        
        // 测试空输入
        await page.locator('#startAnalysisBtn').click();
        
        // 等待错误消息
        await page.waitForTimeout(2000);
        
        // 检查是否显示错误消息
        const errorMessage = page.locator('#errorMessage');
        if (await errorMessage.isVisible()) {
            const errorText = await errorMessage.textContent();
            console.log(`错误消息: ${errorText}`);
        }
        
        // 检查进度文本是否显示错误状态
        const progressText = page.locator('#progressText');
        const progressContent = await progressText.textContent();
        console.log(`进度状态: ${progressContent}`);
    });

    test.afterEach(async () => {
        if (page) {
            await page.close();
        }
    });
});
