# 🚀 AutoPilot AI 旅行规划Agent - 快速开始指南

## 📋 系统概述

AutoPilot AI 旅行规划Agent是一个基于AI的智能旅行规划系统，具备强大的个性化能力：

### 🧠 核心特性
- **智能意图理解**：理解自然语言旅行需求
- **个性化画像**：基于用户历史记忆的智能推荐  
- **数据库驱动**：MySQL用户画像系统，MongoDB行程存储
- **高德地图集成**：完整的地理服务和POI信息
- **实时流式输出**：展示AI思考过程和规划进度
- **记忆学习系统**：自动学习用户偏好，持续优化推荐

### 🎯 智能化能力
- 🔍 **意图理解**：准确提取目的地、天数、偏好等关键信息
- 👤 **用户画像**：从历史记忆中分析旅行习惯和偏好
- 🎪 **个性化推荐**：基于用户画像的POI智能评分和排序
- 💾 **记忆沉淀**：自动保存规划过程，形成个性化知识库
- 🔄 **学习优化**：每次规划都会优化下次的推荐效果

## ⚡ 5分钟快速启动

### 1. 环境检查
```bash
# 确保在项目根目录
cd d:\code\pythonWork\autopilotai2

# 激活虚拟环境
.\.venv\Scripts\activate

# 检查Python版本 (需要3.9+)
python --version
```

### 2. 安装依赖
```bash
# 自动安装所有依赖
python install_dependencies.py

# 或手动安装核心依赖
pip install fastapi uvicorn pydantic motor httpx structlog pyyaml python-dotenv openai mysql-connector-python
```

### 3. 数据库连接验证
```bash
# 测试MySQL连接 (用户画像系统)
python -c "
import asyncio
from src.database.mysql_client import get_db_cursor
async def test():
    async with get_db_cursor() as cursor:
        print('✅ MySQL连接成功')
asyncio.run(test())
"

# 测试MongoDB连接 (行程存储)
python -c "
import asyncio
from src.database.mongodb_client import get_mongo_client
async def test():
    client = await get_mongo_client()
    print('✅ MongoDB连接成功')
asyncio.run(test())
"
```

### 4. 启动服务
```bash
# 检查环境配置
python start_server.py --check-only

# 启动开发服务器
python start_server.py --reload
```

### 5. 访问系统
- 🌐 **前端界面**: http://localhost:8000/static/index.html
- 📚 **API文档**: http://localhost:8000/docs
- ❤️ **健康检查**: http://localhost:8000/health

## 🎯 使用示例

### Web界面使用
1. **访问前端页面**: http://localhost:8000/static/index.html
2. **输入旅行需求**：`"我想去西安玩3天，喜欢历史文化景点和美食"`
3. **观察AI思考过程**：
   - 🧠 意图理解和实体提取
   - 👤 用户画像分析（如果是老用户）
   - 🗺️ 地理定位和天气查询
   - 🎪 POI搜索和个性化评分
   - 📋 智能行程编排
   - 💾 记忆保存和画像更新
4. **查看个性化结果**：基于用户历史偏好的推荐行程

### API调用示例
```bash
# 创建规划任务
curl -X POST "http://localhost:8000/api/travel/plan" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "我想去上海玩3天，喜欢文化景点和美食",
    "user_id": "user_001"
  }'

# 获取流式规划进度 (SSE)
curl "http://localhost:8000/api/travel/plan/{trace_id}/stream?user_id=user_001&query=..."
```

### 测试个性化功能
```bash
# 运行用户画像测试
python tests/scripts/test_agent_simple.py

# 测试MySQL模型系统
python tests/scripts/test_mysql_models_integration.py

# 完整Agent功能测试
python tests/scripts/test_agent_database_basic.py
```

## 🏗️ 核心组件

### 1. TravelPlannerAgent (`src/agents/travel_planner_agent.py`)
**四阶段智能规划流程**：
- **Phase 1**: 意图理解与个性化融合
- **Phase 2**: 动态工具规划与并行执行  
- **Phase 3**: 数据综合与智能决策
- **Phase 4**: 结构化结果生成与记忆存储

**个性化核心功能**：
- `_get_user_profile()`: 从数据库读取用户画像和记忆
- `_extract_preferences_from_memories()`: 从记忆中提取偏好
- `_calculate_poi_score()`: 基于用户画像的POI评分
- `_save_user_memory()`: 保存新的用户记忆

### 2. MySQL用户画像系统 (`src/models/mysql_models.py`)
**数据库结构**：
- **dh_user_profile**: 用户基础信息、记忆、画像
- **dh_tripplanner**: AI规划会话、行程、POI数据

**CRUD操作** (`src/models/mysql_crud.py`)：
- 用户管理：`user_crud`
- 记忆管理：`user_memory_crud` 
- 画像管理：`user_summary_crud`
- 行程管理：`itinerary_crud`

### 3. 高德MCP工具 (`src/tools/amap_mcp_client.py`)
- 地理编码/逆地理编码
- POI搜索与详情查询
- 路线规划和导航
- 天气预报服务

### 4. MongoDB行程存储 (`src/database/mongodb_client.py`)
- 完整行程文档存储
- 用户历史行程管理
- 高效的查询和索引

## 📊 已配置的服务

### 数据库连接
- **MySQL**: ***********:19090 (用户画像系统)
  - 数据库: `dh_user_profile`, `dh_tripplanner`
  - 用户: root/Fsit#2024
- **MongoDB**: ***********:27017 (行程存储)
  - 数据库: autopilot_ai
  - 用户: admin/m25uids*g
- **Redis**: ***********:5182 (缓存系统)
  - 密码: kLKe3NFM4RZMgXhA

### AI模型配置
- **推理模型**: glm-z1-flash (智谱AI) - 复杂分析和决策
- **基础模型**: glm-4-flash (智谱AI) - 快速交互和格式化
- **API密钥**: 已配置完整

### 高德地图服务
- **MCP接口**: https://mcp.amap.com/sse
- **API密钥**: e8f742bdb09f99c8c6b035b7f1f04e66

## 🔧 常见问题

### Q: 服务启动失败？
```bash
# 检查端口占用
netstat -ano | findstr :8000

# 检查依赖安装
python -c "import fastapi, uvicorn, motor, mysql.connector; print('依赖正常')"

# 查看详细错误
python start_server.py --log-level debug
```

### Q: MySQL连接失败？
```bash
# 测试MySQL连接
python -c "
import asyncio
from src.database.mysql_client import get_db_cursor
async def test():
    async with get_db_cursor() as cursor:
        print('MySQL连接池状态:', cursor)
asyncio.run(test())
"

# 检查MySQL配置
cat config/default.yaml | grep -A 10 mysql
```

### Q: MongoDB连接失败？
```bash
# 测试MongoDB连接
python -c "
import asyncio
from src.database.mongodb_client import get_mongo_client
async def test():
    client = await get_mongo_client()
    # 测试ping
    await client.admin.command('ping')
    print('MongoDB连接正常')
asyncio.run(test())
"
```

### Q: 用户画像不工作？
```bash
# 测试用户画像系统
python -c "
from src.models.mysql_crud import user_crud, user_memory_crud
print('CRUD系统加载成功')
print('用户CRUD方法:', [m for m in dir(user_crud) if not m.startswith('_')])
"

# 检查数据库表结构
python -c "
import asyncio
from src.database.mysql_client import get_db_cursor
async def test():
    async with get_db_cursor() as cursor:
        cursor.execute('SHOW TABLES')
        tables = cursor.fetchall()
        print('数据库表:', tables)
asyncio.run(test())
"
```

### Q: 高德API调用失败？
```bash
# 测试高德API
python -c "
import asyncio
from src.tools.amap_mcp_client import get_amap_client
async def test():
    client = await get_amap_client()
    result = await client.maps_geo('北京市天安门')
    print('高德API正常:', result['formatted_address'])
asyncio.run(test())
"
```

## 🎨 自定义配置

### 修改AI模型配置
编辑 `config/default.yaml`:
```yaml
reasoning_llm:
  model: "glm-z1-flash"  # 或其他推理模型
  api_key: "your-api-key"
  base_url: "https://open.bigmodel.cn/api/paas/v4"

basic_llm:
  model: "glm-4-flash"   # 或其他基础模型  
  api_key: "your-api-key"
  base_url: "https://open.bigmodel.cn/api/paas/v4"
```

### 修改数据库配置
编辑 `config/default.yaml`:
```yaml
mysql:
  host: "your-mysql-host"
  port: 19090
  user: "your-username"
  password: "your-password"
  databases:
    user_profile: "dh_user_profile"
    trip_planner: "dh_tripplanner"

mongodb:
  host: "your-mongodb-host"
  port: 27017
  username: "your-username"
  password: "your-password"
  database: "autopilot_ai"
```

### 调整个性化参数
编辑 `src/agents/travel_planner_agent.py`:
```python
# POI评分权重调整
def _calculate_poi_score(self, poi: dict, state: AgentState) -> float:
    # 调整这些权重来改变个性化程度
    PROFILE_WEIGHT = 0.4     # 用户画像权重
    MEMORY_WEIGHT = 0.3      # 记忆匹配权重  
    RATING_WEIGHT = 0.3      # 基础评分权重
```

## 📈 监控和日志

### 查看系统日志
```bash
# 实时日志
tail -f logs/app.log

# 错误日志
grep ERROR logs/app.log

# 个性化相关日志
grep "用户画像\|记忆\|个性化" logs/app.log
```

### 性能监控
```bash
# 健康检查
curl http://localhost:8000/health

# 数据库连接状态
curl http://localhost:8000/api/system/db-status

# API响应时间测试
curl -w "@curl-format.txt" http://localhost:8000/api/travel/health
```

### 个性化效果监控
```bash
# 查看用户记忆增长
python -c "
import asyncio
from src.database.mysql_client import get_db_cursor
async def check():
    async with get_db_cursor() as cursor:
        cursor.execute('SELECT COUNT(*) as count FROM user_memories')
        count = cursor.fetchone()
        print(f'总记忆数: {count[0]}')
asyncio.run(check())
"

# 查看用户画像更新情况
python -c "
import asyncio  
from src.database.mysql_client import get_db_cursor
async def check():
    async with get_db_cursor() as cursor:
        cursor.execute('SELECT COUNT(*) as count FROM user_summaries')
        count = cursor.fetchone()
        print(f'用户画像数: {count[0]}')
asyncio.run(check())
"
```

## 🚀 生产部署

### 生产启动
```bash
# 生产模式启动
python start_server.py --host 0.0.0.0 --port 8000 --workers 4

# 使用Gunicorn
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### 数据库优化
```sql
-- MySQL索引优化
CREATE INDEX idx_user_memories_user_confidence ON user_memories(user_id, confidence_score);
CREATE INDEX idx_user_summaries_user ON user_summaries(user_id);
CREATE INDEX idx_itineraries_user_created ON itineraries(user_id, created_at);

-- MongoDB索引优化
db.itineraries.createIndex({user_id: 1, created_at: -1})
db.travel_plans.createIndex({trace_id: 1})
```

### Docker部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY . .

# 安装依赖
RUN pip install -e .

# 数据库健康检查
RUN python -c "from src.core.config import get_settings; print('配置验证通过')"

EXPOSE 8000

# 启动命令
CMD ["python", "start_server.py", "--host", "0.0.0.0", "--port", "8000"]
```

## 📞 技术支持

### 开发团队联系方式
- 📧 邮箱: <EMAIL>
- 💬 微信群: AutoPilot-AI-Support
- 🐛 问题反馈: GitHub Issues

### 文档资源
- 📖 **系统架构**: README.md
- 🗄️ **数据库文档**: src/models/mysql_models.py
- 🧪 **测试指南**: tests/scripts/
- ⚙️ **配置说明**: config/default.yaml
- 📋 **改造总结**: 改造完成总结.md

### 数据库管理工具
- **MySQL**: 推荐使用 phpMyAdmin 或 MySQL Workbench
- **MongoDB**: 推荐使用 MongoDB Compass
- **Redis**: 推荐使用 RedisInsight

---

🎉 **恭喜！你已经成功启动了AutoPilot AI 旅行规划Agent系统！**

现在可以开始体验AI驱动的**个性化**智能旅行规划服务了。系统会自动学习用户偏好，每次规划都比上一次更精准！

💡 **个性化小贴士**：
- 首次使用时系统会收集基础偏好
- 使用次数越多，推荐越精准
- 每次规划都会自动保存用户记忆
- 支持分析用户历史旅行模式
