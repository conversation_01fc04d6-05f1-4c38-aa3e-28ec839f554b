/**
 * AutoPilot AI V3.0 前端应用
 * 
 * 统一架构版本，支持：
 * 1. 两阶段意图分析（框架分析 + 偏好分析）
 * 2. ICP迭代规划流程
 * 3. 实时SSE事件流
 * 4. 透明化UI界面
 */

class AutoPilotAppV3 {
    constructor() {
        this.sessionId = null;
        this.userId = 1;
        this.eventSource = null;
        this.isPlanning = false;
        this.currentTaskId = null;
        
        // V3架构的阶段定义
        this.phases = {
            framework_analysis: {
                name: '核心框架分析',
                description: '分析旅行目的地、天数、主题等核心要素',
                completed: false,
                result: null
            },
            preference_analysis: {
                name: '个性化偏好分析', 
                description: '分析景点、美食、住宿等个性化偏好',
                completed: false,
                result: null
            },
            prepare_context: {
                name: '上下文准备',
                description: '整合分析结果，准备规划上下文',
                completed: false,
                result: null
            },
            icp_planning: {
                name: 'ICP迭代规划',
                description: '基于思考-行动-观察循环的智能规划',
                completed: false,
                result: null
            }
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.resetUI();
        console.log('AutoPilot AI V3.0 初始化完成');
    }
    
    bindEvents() {
        // 绑定表单提交事件
        const form = document.getElementById('planningForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startPlanning();
            });
        }
        
        // 绑定立即规划按钮
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startPlanning();
            });
        }
        
        // 绑定取消按钮
        const cancelBtn = document.getElementById('cancelPlanningBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 绑定视图切换按钮
        const listViewBtn = document.getElementById('viewModeList');
        const mapViewBtn = document.getElementById('viewModeMap');
        
        if (listViewBtn) {
            listViewBtn.addEventListener('click', () => {
                this.switchView('list');
            });
        }
        
        if (mapViewBtn) {
            mapViewBtn.addEventListener('click', () => {
                this.switchView('map');
            });
        }
    }
    
    async startPlanning() {
        if (this.isPlanning) {
            console.log('规划已在进行中');
            return;
        }
        
        const userQuery = document.getElementById('userQuery').value.trim();
        if (!userQuery) {
            alert('请输入您的旅行需求');
            return;
        }
        
        this.isPlanning = true;
        this.resetUI();
        this.showAnalysisView();
        
        try {
            // 生成会话ID
            this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            console.log('开始V3规划流程:', {
                sessionId: this.sessionId,
                userId: this.userId,
                query: userQuery
            });

            // 调用V3 意图分析API - 两阶段流程第一阶段
            const response = await fetch('/api/v3/travel-planner/analyze-intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_query: userQuery,
                    user_id: this.userId.toString()
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            // 处理SSE流响应
            this.processSSEStream(response);

        } catch (error) {
            console.error('启动规划失败:', error);
            this.showError('启动规划失败: ' + error.message);
            this.isPlanning = false;
        }
    }

    async processSSEStream(response) {
        console.log('开始处理SSE流');

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.slice(6));
                            console.log('收到SSE事件:', eventData);
                            this.handleSSEEvent(eventData);
                        } catch (error) {
                            console.error('解析SSE事件失败:', error, line);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('处理SSE流失败:', error);
            this.showError('处理SSE流失败: ' + error.message);
        } finally {
            this.isPlanning = false;
        }
    }
    
    handleSSEEvent(data) {
        const { event, data: eventData, timestamp } = data;
        
        switch (event) {
            case 'start':
                this.handleStartEvent(eventData);
                break;

            case 'node_complete':
                this.handleNodeCompleteEvent(eventData);
                break;

            case 'intent_complete':
                this.handleIntentCompleteEvent(eventData);
                break;

            case 'complete':
                this.handleCompleteEvent(eventData);
                break;

            case 'error':
                this.handleErrorEvent(eventData);
                break;

            default:
                console.log('未知事件类型:', event, eventData);
        }
    }
    
    handleStartEvent(data) {
        console.log('规划开始:', data);
        this.updateAnalysisStatus('开始分析', '正在初始化AI分析流程...');
    }
    
    handleNodeCompleteEvent(data) {
        const { node_name, result } = data;
        console.log('节点完成:', node_name, result);

        // 根据节点名称更新对应的阶段
        if (node_name === 'framework_analysis') {
            this.phases.framework_analysis.completed = true;
            this.phases.framework_analysis.result = result.framework_analysis;
            this.updatePhaseStep('framework_analysis', 'completed');
            this.displayFrameworkAnalysisResult(result.framework_analysis);
        } else if (node_name === 'preference_analysis') {
            this.phases.preference_analysis.completed = true;
            this.phases.preference_analysis.result = result.preference_analysis;
            this.updatePhaseStep('preference_analysis', 'completed');
            this.displayPreferenceAnalysisResult(result.preference_analysis);
        } else if (node_name === 'prepare_context') {
            this.phases.prepare_context.completed = true;
            this.phases.prepare_context.result = result;
            this.updatePhaseStep('prepare_context', 'completed');

            // 意图分析完成，显示开始规划按钮
            this.showStartPlanningButton();
        } else if (node_name === 'icp_planning') {
            this.phases.icp_planning.completed = true;
            this.phases.icp_planning.result = result;
            this.updatePhaseStep('icp_planning', 'completed');

            // 显示最终行程
            if (result.final_itinerary) {
                this.showItinerary(result.final_itinerary);
            }
        }
    }
    
    handleIntentCompleteEvent(data) {
        console.log('意图分析完成:', data);

        // 存储task_id用于后续规划
        if (data.task_id) {
            this.currentTaskId = data.task_id;
        }

        // 显示开始规划按钮
        this.showStartPlanningButton();

        // 关闭SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    handleCompleteEvent(data) {
        console.log('规划完成:', data);
        this.isPlanning = false;
        this.updateAnalysisStatus('规划完成', '您的个性化旅行行程已生成完毕');

        // 关闭SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }
    
    handleErrorEvent(data) {
        const { message, phase } = data;
        console.error('规划错误:', message, phase);
        this.showError(`规划过程中发生错误: ${message}`);
        this.isPlanning = false;

        if (phase && this.phases[phase]) {
            this.updatePhaseStep(phase, 'error');
        }
    }

    updateAnalysisStatus(title, description) {
        const titleEl = document.getElementById('analysisStatusTitle');
        const descEl = document.getElementById('analysisStatusDesc');

        if (titleEl) titleEl.textContent = title;
        if (descEl) descEl.textContent = description;
    }

    updatePhaseStep(phase, status) {
        // 动态创建或更新分析步骤
        const stepsContainer = document.getElementById('analysisSteps');
        if (!stepsContainer) return;

        let stepEl = document.getElementById(`step-${phase}`);

        if (!stepEl) {
            // 创建新的步骤元素
            stepEl = document.createElement('div');
            stepEl.id = `step-${phase}`;
            stepEl.className = 'analysis-step';
            stepEl.innerHTML = `
                <div class="step-indicator">
                    <div class="step-icon">
                        <i class="bi bi-circle"></i>
                    </div>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <h6 class="step-title">${this.phases[phase].name}</h6>
                    <p class="step-description">${this.phases[phase].description}</p>
                    <div class="step-status">等待中...</div>
                </div>
            `;

            // 移除占位符
            const placeholder = stepsContainer.querySelector('.analysis-placeholder');
            if (placeholder) {
                placeholder.remove();
            }

            stepsContainer.appendChild(stepEl);
        }

        // 更新步骤状态
        const iconEl = stepEl.querySelector('.step-icon i');
        const statusEl = stepEl.querySelector('.step-status');

        stepEl.className = `analysis-step step-${status}`;

        switch (status) {
            case 'waiting':
                iconEl.className = 'bi bi-circle';
                statusEl.textContent = '等待中...';
                break;
            case 'running':
                iconEl.className = 'bi bi-arrow-clockwise spin';
                statusEl.textContent = '分析中...';
                break;
            case 'completed':
                iconEl.className = 'bi bi-check-circle-fill';
                statusEl.textContent = '已完成';
                break;
            case 'error':
                iconEl.className = 'bi bi-x-circle-fill';
                statusEl.textContent = '出错了';
                break;
        }
    }

    showAnalysisView() {
        // 隐藏等待视图，显示分析视图
        const waitingView = document.getElementById('waitingView');
        const analysisView = document.getElementById('analysisView');

        if (waitingView) waitingView.style.display = 'none';
        if (analysisView) analysisView.style.display = 'block';
    }

    showItinerary(itinerary) {
        console.log('显示行程:', itinerary);

        // 隐藏分析视图，显示行程视图
        const analysisView = document.getElementById('analysisView');
        const itineraryView = document.getElementById('itineraryView');

        if (analysisView) analysisView.style.display = 'none';
        if (itineraryView) itineraryView.style.display = 'block';

        // 更新行程信息
        this.updateItineraryDisplay(itinerary);
    }

    updateItineraryDisplay(itinerary) {
        // 更新行程标题和描述
        const titleEl = document.getElementById('itineraryTitle');
        const descEl = document.getElementById('itineraryDescription');

        if (titleEl && itinerary.itinerary_summary) {
            titleEl.textContent = itinerary.itinerary_summary.theme || '您的旅行行程';
        }

        if (descEl && itinerary.itinerary_summary) {
            const summary = itinerary.itinerary_summary;
            descEl.textContent = `${summary.destinations?.join('、') || ''} ${summary.total_days || 0}天行程`;
        }

        // 更新统计信息
        this.updateItineraryStats(itinerary);

        // 渲染每日行程
        this.renderDailyItinerary(itinerary);
    }

    updateItineraryStats(itinerary) {
        const totalDaysEl = document.getElementById('totalDays');
        const totalPOIsEl = document.getElementById('totalPOIs');
        const estimatedBudgetEl = document.getElementById('estimatedBudget');

        // 从planning_summary或metadata中获取天数信息
        let totalDays = 0;
        if (itinerary.planning_summary && itinerary.planning_summary.total_days) {
            totalDays = itinerary.planning_summary.total_days;
        } else if (itinerary.metadata && itinerary.metadata.total_days) {
            totalDays = itinerary.metadata.total_days;
        } else if (itinerary.daily_plans) {
            // 从daily_plans的键数量推断天数
            totalDays = Object.keys(itinerary.daily_plans).length;
        }

        if (totalDaysEl) {
            totalDaysEl.textContent = totalDays;
        }

        // 计算总POI数量
        let totalPOIs = 0;
        if (itinerary.daily_plans) {
            Object.values(itinerary.daily_plans).forEach(dayPlan => {
                if (Array.isArray(dayPlan)) {
                    totalPOIs += dayPlan.length;
                }
            });
        }

        if (totalPOIsEl) {
            totalPOIsEl.textContent = totalPOIs;
        }

        // 预算信息
        if (estimatedBudgetEl) {
            if (itinerary.planning_summary && itinerary.planning_summary.estimated_budget) {
                estimatedBudgetEl.textContent = `¥${itinerary.planning_summary.estimated_budget}`;
            } else {
                estimatedBudgetEl.textContent = '¥待计算';
            }
        }
    }

    renderDailyItinerary(itinerary) {
        // 这里可以渲染详细的每日行程
        console.log('渲染每日行程:', itinerary.daily_plans);
    }

    resetUI() {
        // 重置所有阶段状态
        Object.keys(this.phases).forEach(phase => {
            this.phases[phase].completed = false;
            this.phases[phase].result = null;
        });

        // 清空分析步骤容器
        const stepsContainer = document.getElementById('analysisSteps');
        if (stepsContainer) {
            stepsContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <div class="text-center text-muted">
                        <i class="bi bi-clock-history"></i>
                        <p class="mt-2">等待开始分析...</p>
                    </div>
                </div>
            `;
        }

        // 显示等待视图
        const waitingView = document.getElementById('waitingView');
        const analysisView = document.getElementById('analysisView');
        const itineraryView = document.getElementById('itineraryView');

        if (waitingView) waitingView.style.display = 'block';
        if (analysisView) analysisView.style.display = 'none';
        if (itineraryView) itineraryView.style.display = 'none';
    }

    showError(message) {
        console.error('显示错误:', message);
        alert(message); // 简单的错误显示，可以后续优化
    }

    cancelPlanning() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        this.isPlanning = false;
        this.resetUI();
        console.log('规划已取消');
    }

    switchView(viewType) {
        console.log('切换视图:', viewType);
        // 这里可以实现列表/地图视图切换
    }

    displayFrameworkAnalysisResult(frameworkResult) {
        console.log('显示框架分析结果:', frameworkResult);

        const stepEl = document.getElementById('step-framework_analysis');
        if (!stepEl) return;

        const contentEl = stepEl.querySelector('.step-content');
        if (!contentEl) return;

        // 创建结果显示区域
        let resultEl = contentEl.querySelector('.step-result');
        if (!resultEl) {
            resultEl = document.createElement('div');
            resultEl.className = 'step-result mt-2';
            contentEl.appendChild(resultEl);
        }

        // 解析核心框架信息（从嵌套的core_intent对象中提取）
        const coreIntent = frameworkResult.core_intent || {};
        const destinations = coreIntent.destinations || [];
        const totalDays = coreIntent.total_days || 0;
        const theme = coreIntent.theme || '';
        const budget = coreIntent.budget || '';

        resultEl.innerHTML = `
            <div class="result-tags">
                <span class="badge bg-primary me-1">${destinations.join('、')}</span>
                <span class="badge bg-info me-1">${totalDays}天</span>
                ${theme ? `<span class="badge bg-success me-1">${theme}</span>` : ''}
                ${budget ? `<span class="badge bg-warning">${budget}</span>` : ''}
            </div>
        `;
    }

    displayPreferenceAnalysisResult(preferenceResult) {
        console.log('显示偏好分析结果:', preferenceResult);

        const stepEl = document.getElementById('step-preference_analysis');
        if (!stepEl) return;

        const contentEl = stepEl.querySelector('.step-content');
        if (!contentEl) return;

        // 创建结果显示区域
        let resultEl = contentEl.querySelector('.step-result');
        if (!resultEl) {
            resultEl = document.createElement('div');
            resultEl.className = 'step-result mt-2';
            contentEl.appendChild(resultEl);
        }

        // 解析偏好信息（从嵌套对象中提取）
        const attractionPrefs = preferenceResult.attraction_preferences || {};
        const diningPrefs = preferenceResult.dining_preferences || {};
        const accommodationPrefs = preferenceResult.accommodation_preferences || {};

        // 提取偏好类型数组
        const attractions = attractionPrefs.types || [];
        const food = diningPrefs.types || [];
        const accommodation = accommodationPrefs.types || [];

        resultEl.innerHTML = `
            <div class="preference-details">
                ${attractions.length > 0 ? `
                    <div class="preference-item mb-1">
                        <small class="text-muted">景点偏好:</small>
                        <span class="ms-1">${attractions.join('、')}</span>
                    </div>
                ` : ''}
                ${food.length > 0 ? `
                    <div class="preference-item mb-1">
                        <small class="text-muted">美食偏好:</small>
                        <span class="ms-1">${food.join('、')}</span>
                    </div>
                ` : ''}
                ${accommodation.length > 0 ? `
                    <div class="preference-item mb-1">
                        <small class="text-muted">住宿偏好:</small>
                        <span class="ms-1">${accommodation.join('、')}</span>
                    </div>
                ` : ''}
            </div>
        `;
    }

    showStartPlanningButton() {
        console.log('显示开始规划按钮');

        // 更新右侧状态为等待开始规划
        this.updateAnalysisStatus('意图分析完成', '点击下方按钮开始智能规划您的行程');

        // 显示开始规划按钮
        const waitingView = document.getElementById('waitingView');
        const analysisView = document.getElementById('analysisView');

        if (waitingView) waitingView.style.display = 'none';
        if (analysisView) {
            analysisView.style.display = 'block';

            // 添加开始规划按钮
            const statusContent = analysisView.querySelector('.status-content');
            if (statusContent) {
                let buttonContainer = statusContent.querySelector('.planning-buttons');
                if (!buttonContainer) {
                    buttonContainer = document.createElement('div');
                    buttonContainer.className = 'planning-buttons mt-3';
                    buttonContainer.innerHTML = `
                        <button id="startICPPlanningBtn" class="btn btn-primary btn-lg me-2">
                            <i class="bi bi-play-circle"></i> 开始规划
                        </button>
                        <button id="cancelAnalysisBtn" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> 取消
                        </button>
                    `;
                    statusContent.appendChild(buttonContainer);

                    // 绑定按钮事件
                    document.getElementById('startICPPlanningBtn').addEventListener('click', () => {
                        this.startICPPlanning();
                    });

                    document.getElementById('cancelAnalysisBtn').addEventListener('click', () => {
                        this.cancelPlanning();
                    });
                }
            }
        }
    }

    async startICPPlanning() {
        console.log('开始ICP迭代规划');

        // 隐藏开始规划按钮
        const buttonContainer = document.querySelector('.planning-buttons');
        if (buttonContainer) {
            buttonContainer.style.display = 'none';
        }

        // 更新状态显示
        this.updateAnalysisStatus('开始智能规划', 'AI正在为您规划个性化行程...');

        try {
            // 获取用户查询
            const userQuery = document.getElementById('userQuery').value.trim();

            // 调用完整规划API
            const response = await fetch('/api/v3/travel-planner/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_query: userQuery,
                    user_id: this.userId.toString(),
                    execution_mode: 'icp_only'  // 只执行ICP规划阶段
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            // 处理ICP规划的SSE流响应
            this.processSSEStream(response);

        } catch (error) {
            console.error('启动ICP规划失败:', error);
            this.showError('启动ICP规划失败: ' + error.message);
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.autoPilotApp = new AutoPilotAppV3();
});
