# AutoPilot AI Tests 目录结构说明

## 📁 测试分层结构

```
tests/
├── integration/              # 🔗 集成测试 - 测试多个模块协作
│   ├── test_agent_system.py  # Agent系统集成测试
│   ├── test_python_expert_integration.py # Python专家Agent集成测试
│   └── test_autogen_agent_integration.py # AutoGen框架集成测试
├── unit/                     # 🧩 单元测试 - 测试单个类/函数
│   ├── agents/
│   │   ├── test_simple_assistant.py
│   │   └── test_python_expert.py
│   ├── core/
│   │   ├── test_config.py
│   │   ├── test_logger.py
│   │   └── test_llm_manager.py
│   ├── integration/
│   │   └── test_autogen_adapter.py # AutoGen适配器单元测试
│   ├── llms/
│   ├── tools/
│   ├── memory/
│   ├── api/
│   └── templates/
├── scripts/                  # 📜 测试脚本和工具
│   └── test_all_agents.ps1
└── fixtures/                 # 🗂️ 测试数据和配置 (待创建)
    ├── mock_responses.json
    └── test_configs.py
```

## 🎯 测试类型说明

### 集成测试 (Integration Tests)
- **位置**: `tests/integration/`
- **目的**: 测试多个模块协作的完整流程
- **特点**: 使用真实API、测试端到端流程
- **示例**: Agent创建→对话→历史管理的完整流程

#### AutoGen集成测试
- **文件**: `test_autogen_agent_integration.py`
- **目的**: 验证AutoGen框架与现有架构的兼容性
- **特点**: 
  - 使用现有配置系统为AutoGen提供LLM配置
  - 测试AutoGen Agent与自研Agent的共存
  - 验证AutoGen的基础功能（对话、工具调用等）
- **运行**: `python tests/integration/test_autogen_agent_integration.py`

### 单元测试 (Unit Tests)
- **位置**: `tests/unit/`
- **目的**: 测试单个类或函数的功能
- **特点**: 使用Mock模拟、快速执行、高覆盖率
- **示例**: 配置加载、LLM管理器、单个Agent方法

#### AutoGen适配器单元测试
- **文件**: `tests/unit/integration/test_autogen_adapter.py`
- **目的**: 测试AutoGen适配器逻辑的正确性
- **特点**:
  - 纯单元测试，不依赖外部API
  - 模拟AutoGen库以避免依赖问题
  - 测试配置转换和错误处理逻辑
- **运行**: `pytest tests/unit/integration/test_autogen_adapter.py -v`

### 测试脚本 (Test Scripts)
- **位置**: `tests/scripts/`
- **目的**: 自动化测试执行和报告生成
- **特点**: PowerShell脚本、参数化执行、彩色输出

## 🚀 快速运行测试

### 运行全部测试
```powershell
.\tests\scripts\test_all_agents.ps1
```

### 快速测试（跳过覆盖率）
```powershell
.\tests\scripts\test_all_agents.ps1 -Quick
```

### 仅运行AutoGen测试
```powershell
.\tests\scripts\test_all_agents.ps1 -AutoGen
```

### 跳过AutoGen测试
```powershell
.\tests\scripts\test_all_agents.ps1 -SkipAutoGen
```

### 运行特定模块
```powershell
# 核心模块单元测试
pytest tests/unit/core/ -v

# Agent模块单元测试
pytest tests/unit/agents/ -v

# AutoGen适配器测试
pytest tests/unit/integration/test_autogen_adapter.py -v

# 单个集成测试
python tests/integration/test_agent_system.py
python tests/integration/test_autogen_agent_integration.py
```

## 🎨 测试最佳实践

### 1. 测试命名规范
- 单元测试：`test_[功能描述].py`
- 集成测试：`test_[系统名称]_integration.py`
- 测试方法：`test_[具体行为]_[预期结果]()`

### 2. 测试组织原则
- **单一职责**：每个测试只验证一个功能点
- **独立性**：测试之间不能有依赖关系
- **可重复性**：多次运行结果一致
- **快速反馈**：单元测试应在秒级完成

### 3. AutoGen测试特殊考虑
- **兼容性检查**：首先检查AutoGen库是否可用
- **配置隔离**：使用模拟配置避免影响现有系统
- **错误容忍**：API调用失败不影响其他测试
- **共存验证**：确保与现有Agent框架和谐共存

### 4. Mock使用指南
- **外部依赖**：API调用、文件IO、网络请求
- **配置注入**：使用Mock配置避免依赖真实环境变量
- **时间控制**：使用Mock控制时间相关的测试

## 📊 测试覆盖率

### 查看覆盖率报告
```powershell
# 生成HTML覆盖率报告
pytest tests/unit/ --cov=src --cov-report=html:htmlcov

# 在浏览器中查看
start htmlcov/index.html
```

### 覆盖率要求
- **核心模块**：≥90%
- **Agent模块**：≥85%
- **工具模块**：≥80%
- **适配器模块**：≥85%

## 🔧 故障排除

### AutoGen测试问题
1. **ImportError**: 确保安装了正确版本的AutoGen库
2. **API错误**: 检查API密钥配置和网络连接
3. **版本兼容**: 验证AutoGen版本与项目要求一致

### 常见测试失败
1. **配置问题**: 检查`.env`文件和配置项
2. **依赖缺失**: 运行`pip install -e .[test]`
3. **路径问题**: 确保在项目根目录运行测试

### 性能问题
1. **并行测试**: 使用`pytest -n auto`并行执行
2. **测试选择**: 使用标记只运行特定测试
3. **Mock优化**: 减少真实API调用

## 🎯 持续集成

### GitHub Actions
- 自动运行所有测试套件
- 生成覆盖率报告
- 检查代码质量
- 验证AutoGen兼容性

### 本地钩子
```bash
# 安装pre-commit钩子
pre-commit install

# 手动运行钩子
pre-commit run --all-files
```

---

**注意**: AutoGen集成测试是可选的，如果环境中没有AutoGen库，测试会自动跳过相关内容，不会影响现有测试的执行。
