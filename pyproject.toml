[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "autopilot-ai"
version = "0.1.0"
description = "AutoPilot AI - 领航AI多智能体系统"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
keywords = ["ai", "agent", "autogen", "multi-agent"]
authors = [
    {name = "AutoPilot AI Team"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    # Core AutoGen framework
    "autogen-agentchat>=0.4.0",
    "autogen-core>=0.4.0",
    
    # Web framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # Configuration management
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "pyyaml>=6.0.1",
    
    # Memory & State management
    "redis>=5.0.0",
    
    # Prompt templating
    "jinja2>=3.1.0",
    
    # Logging
    "structlog>=23.2.0",
    
    # HTTP client for tools
    "httpx>=0.25.0",
    
    # Utility libraries
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    
    # Code quality
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.6.0",
    
    # Development tools
    "pre-commit>=3.5.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
]

[project.urls]
Homepage = "https://github.com/autopilot-ai/autopilot-ai"
Repository = "https://github.com/autopilot-ai/autopilot-ai"
Documentation = "https://github.com/autopilot-ai/autopilot-ai/blob/main/README.md"

[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "-x",
]
testpaths = ["tests"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning:autogen",
    "ignore::UserWarning:openai",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
    "*Test",
]
python_functions = [
    "test_*",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.pytest_cache
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.coverage.run]
branch = true
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
] 