"""
简单的高德API测试

直接测试高德API的连接和基本功能
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.Amap.map_tool import MapTool, Location


def test_amap_api_direct():
    """直接测试高德API"""
    print("=" * 60)
    print("高德API直接连接测试")
    print("=" * 60)
    
    api_key = "cd978c562fe54dd9a11117bfd4a2a3f1"
    base_url = "https://restapi.amap.com/v3"
    
    # 测试1: 地理编码API
    print("1. 测试地理编码API...")
    try:
        geocode_url = f"{base_url}/geocode/geo"
        params = {
            "key": api_key,
            "address": "北京市天安门广场",
            "output": "json"
        }
        
        response = requests.get(geocode_url, params=params, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   API状态: {data.get('status')}")
            print(f"   信息: {data.get('info')}")
            
            if data.get('status') == '1' and data.get('geocodes'):
                geocode = data['geocodes'][0]
                print(f"   ✓ 地理编码成功")
                print(f"     坐标: {geocode.get('location')}")
                print(f"     详细地址: {geocode.get('formatted_address')}")
            else:
                print(f"   ✗ 地理编码失败: {data.get('info')}")
        else:
            print(f"   ✗ HTTP请求失败: {response.status_code}")
            print(f"     响应内容: {response.text}")
            
    except Exception as e:
        print(f"   ✗ 地理编码异常: {str(e)}")
    
    # 测试2: POI搜索API
    print("\n2. 测试POI搜索API...")
    try:
        poi_url = f"{base_url}/place/text"
        params = {
            "key": api_key,
            "keywords": "景点",
            "city": "北京",
            "output": "json",
            "page": 1,
            "offset": 5
        }
        
        response = requests.get(poi_url, params=params, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   API状态: {data.get('status')}")
            print(f"   信息: {data.get('info')}")
            
            if data.get('status') == '1' and data.get('pois'):
                pois = data['pois']
                print(f"   ✓ POI搜索成功，找到 {len(pois)} 个结果")
                for i, poi in enumerate(pois[:3]):
                    print(f"     {i+1}. {poi.get('name')} - {poi.get('type')}")
            else:
                print(f"   ✗ POI搜索失败: {data.get('info')}")
        else:
            print(f"   ✗ HTTP请求失败: {response.status_code}")
            print(f"     响应内容: {response.text}")
            
    except Exception as e:
        print(f"   ✗ POI搜索异常: {str(e)}")
    
    # 测试3: 天气查询API
    print("\n3. 测试天气查询API...")
    try:
        weather_url = f"{base_url}/weather/weatherInfo"
        params = {
            "key": api_key,
            "city": "110000",  # 北京市adcode
            "output": "json",
            "extensions": "base"
        }
        
        response = requests.get(weather_url, params=params, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   API状态: {data.get('status')}")
            print(f"   信息: {data.get('info')}")
            
            if data.get('status') == '1' and data.get('lives'):
                weather = data['lives'][0]
                print(f"   ✓ 天气查询成功")
                print(f"     城市: {weather.get('city')}")
                print(f"     天气: {weather.get('weather')}")
                print(f"     温度: {weather.get('temperature')}°C")
                print(f"     湿度: {weather.get('humidity')}%")
            else:
                print(f"   ✗ 天气查询失败: {data.get('info')}")
        else:
            print(f"   ✗ HTTP请求失败: {response.status_code}")
            print(f"     响应内容: {response.text}")
            
    except Exception as e:
        print(f"   ✗ 天气查询异常: {str(e)}")


def test_map_tool_wrapper():
    """测试MapTool包装器"""
    print("\n" + "=" * 60)
    print("MapTool包装器测试")
    print("=" * 60)
    
    try:
        map_tool = MapTool()
        
        # 测试地理编码
        print("1. 测试MapTool地理编码...")
        try:
            result = map_tool.geocode_address("北京市亦庄大族广场")
            print(f"   ✓ 地理编码成功")
            print(f"     状态: {result.get('status')}")
            if result.get('geocodes'):
                geocode = result['geocodes'][0]
                print(f"     坐标: {geocode.get('location')}")
                print(f"     地址: {geocode.get('formatted_address')}")
        except Exception as e:
            print(f"   ✗ 地理编码失败: {str(e)}")
        
        # 测试POI搜索
        print("\n2. 测试MapTool POI搜索...")
        try:
            pois = map_tool.search_pois(
                keywords="历史文化景点",
                city="北京",
                page_size=3
            )
            print(f"   ✓ POI搜索成功，找到 {len(pois)} 个结果")
            for i, poi in enumerate(pois):
                print(f"     {i+1}. {poi.name} - {poi.type}")
        except Exception as e:
            print(f"   ✗ POI搜索失败: {str(e)}")
        
        # 测试天气查询
        print("\n3. 测试MapTool天气查询...")
        try:
            weather = map_tool.get_weather_info("110000")
            print(f"   ✓ 天气查询成功")
            print(f"     状态: {weather.get('status')}")
            if weather.get('forecasts'):
                forecast = weather['forecasts'][0]
                print(f"     城市: {forecast.get('city')}")
        except Exception as e:
            print(f"   ✗ 天气查询失败: {str(e)}")
            
    except Exception as e:
        print(f"✗ MapTool初始化失败: {str(e)}")


def test_network_connectivity():
    """测试网络连接"""
    print("\n" + "=" * 60)
    print("网络连接测试")
    print("=" * 60)
    
    test_urls = [
        "https://www.baidu.com",
        "https://restapi.amap.com",
        "https://www.google.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            print(f"✓ {url}: {response.status_code}")
        except requests.exceptions.Timeout:
            print(f"⚠️ {url}: 超时")
        except requests.exceptions.ConnectionError:
            print(f"✗ {url}: 连接失败")
        except Exception as e:
            print(f"✗ {url}: {str(e)}")


def main():
    """主函数"""
    print("AutoPilot AI - 高德API连接诊断")
    print("=" * 60)
    
    # 测试网络连接
    test_network_connectivity()
    
    # 测试高德API直接调用
    test_amap_api_direct()
    
    # 测试MapTool包装器
    test_map_tool_wrapper()
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
