"""
旅行规划API V3.0 - 统一架构版 (FastAPI)

基于统一架构的SSE流式API端点，支持：
1. 两阶段工作流（意图分析 + ICP规划）
2. 实时事件流
3. 统一状态管理
4. 错误处理和恢复
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.database.redis_client import get_redis_client

logger = logging.getLogger(__name__)

# 创建FastAPI Router
router = APIRouter(prefix="/api/v3/travel-planner", tags=["travel-planner-v3"])


class PlanRequest(BaseModel):
    """旅行规划请求模型"""
    user_query: str
    user_id: Optional[str] = None
    execution_mode: str = "automatic"
    user_profile: Optional[Dict[str, Any]] = None
    continue_from_analysis: Optional[bool] = False
    task_id: Optional[str] = None
    vehicle_info: Optional[Dict[str, Any]] = None


class AnalyzeIntentRequest(BaseModel):
    """意图分析请求模型"""
    user_query: str
    user_id: Optional[str] = None
    user_profile: Optional[Dict[str, Any]] = None
    execution_mode: str = "intent_analysis_only"
    vehicle_info: Optional[Dict[str, Any]] = None


class StartPlanningRequest(BaseModel):
    """开始规划请求模型"""
    task_id: str
    user_id: Optional[str] = None


@router.post("/plan")
async def plan_travel(request: PlanRequest):
    """
    旅行规划API端点 (V3.0)
    
    基于统一架构的两阶段工作流，支持SSE流式响应
    
    Args:
        request: 旅行规划请求
        
    Returns:
        SSE流式响应，包含实时规划进度和结果
    """
    try:
        # 根据continue_from_analysis参数决定执行模式
        if request.continue_from_analysis and request.task_id:
            execution_mode = "icp_planning_only"
            task_id = request.task_id
            logger.info(f"继续从分析阶段执行ICP规划，task_id: {task_id}")
        else:
            execution_mode = request.execution_mode
            task_id = f"task_{int(datetime.utcnow().timestamp() * 1000)}{int(datetime.utcnow().microsecond / 1000)}"
            logger.info(f"执行完整工作流，新task_id: {task_id}")

        # 准备输入数据
        input_data = {
            "user_query": request.user_query,
            "user_id": request.user_id or f'web_user_{int(datetime.utcnow().timestamp())}',
            "execution_mode": execution_mode,
            "user_profile": request.user_profile or {},
            "task_id": task_id,
            "vehicle_info": request.vehicle_info or {}
        }
        
        # 创建SSE响应生成器
        async def generate_sse_stream():
            import asyncio
            import json as json_lib

            try:
                # 创建事件总线
                redis_client = await get_redis_client()
                event_bus = UnifiedEventBus(redis_client)

                # 创建图实例
                graph = TravelPlannerGraphV3(event_bus=event_bus)

                # 发送开始事件
                start_event = {
                    'event': 'start',
                    'data': {'task_id': input_data['task_id']},
                    'timestamp': datetime.utcnow().isoformat()
                }
                yield f"data: {json.dumps(start_event, ensure_ascii=False)}\n\n"

                # 创建Redis订阅来监听实时事件
                task_id = input_data['task_id']
                channel = f"task_channel:{task_id}"

                # 获取Redis实例
                if hasattr(redis_client, '_redis') and redis_client._redis is not None:
                    redis_instance = redis_client._redis
                elif hasattr(redis_client, 'client'):
                    redis_instance = redis_client.client
                else:
                    redis_instance = redis_client

                pubsub = redis_instance.pubsub()
                await pubsub.subscribe(channel)

                # 启动后台任务执行工作流
                async def run_workflow():
                    try:
                        # 根据执行模式选择相应的流程
                        if execution_mode == "icp_planning_only":
                            # 只执行ICP规划阶段
                            async for step in graph.stream_icp_planning(input_data):
                                # 转换步骤为SSE事件
                                for node_name, node_result in step.items():
                                    if isinstance(node_result, dict):
                                        # 发送节点完成事件
                                        event_data = {
                                            "event": "node_complete",
                                            "data": {
                                                "node_name": node_name,
                                                "result": node_result,
                                                "timestamp": datetime.utcnow().isoformat()
                                            }
                                        }
                                        await redis_instance.publish(channel, json.dumps(event_data, ensure_ascii=False))
                        else:
                            # 执行完整工作流
                            async for step in graph.stream(input_data):
                                # 转换步骤为SSE事件
                                for node_name, node_result in step.items():
                                    if isinstance(node_result, dict):
                                        # 发送节点完成事件
                                        event_data = {
                                            "event": "node_complete",
                                            "data": {
                                                "node_name": node_name,
                                                "result": node_result,
                                                "timestamp": datetime.utcnow().isoformat()
                                            }
                                        }
                                        # 通过事件总线发布，这样可以被Redis订阅捕获
                                        await event_bus._publish(task_id, event_data)

                        # 发送完成事件
                        complete_event = {
                            'event': 'complete',
                            'timestamp': datetime.utcnow().isoformat()
                        }
                        await event_bus._publish(task_id, complete_event)

                        # 发送结束事件
                        eos_event = {
                            'event': 'eos',
                            'timestamp': datetime.utcnow().isoformat()
                        }
                        await event_bus._publish(task_id, eos_event)
                    except Exception as e:
                        logger.error(f"Workflow execution failed: {str(e)}")
                        error_event = {
                            "event": "error",
                            "data": {
                                "message": f"Workflow execution failed: {str(e)}",
                                "error_code": "WORKFLOW_ERROR",
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        }
                        await event_bus._publish(task_id, error_event)
                        await event_bus._publish(task_id, {"event": "eos", "timestamp": datetime.utcnow().isoformat()})

                # 启动工作流任务
                workflow_task = asyncio.create_task(run_workflow())

                # 监听Redis事件并转发给SSE
                try:
                    while True:
                        message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
                        if message and message['data']:
                            try:
                                # 处理不同类型的消息数据
                                message_data = message['data']
                                if isinstance(message_data, bytes):
                                    message_data = message_data.decode('utf-8')
                                elif not isinstance(message_data, str):
                                    message_data = str(message_data)

                                event_data = json_lib.loads(message_data)
                                yield f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"

                                # 如果收到结束事件，退出循环
                                if event_data.get('event') == 'eos':
                                    break
                            except (json_lib.JSONDecodeError, AttributeError) as e:
                                logger.warning(f"Failed to decode Redis message: {message['data']}, error: {e}")

                        # 检查工作流任务是否完成
                        if workflow_task.done():
                            # 等待一小段时间确保所有事件都被处理
                            await asyncio.sleep(0.5)
                            # 检查是否还有未处理的消息
                            final_message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=0.1)
                            if final_message and final_message['data']:
                                try:
                                    # 处理不同类型的消息数据
                                    final_message_data = final_message['data']
                                    if isinstance(final_message_data, bytes):
                                        final_message_data = final_message_data.decode('utf-8')
                                    elif not isinstance(final_message_data, str):
                                        final_message_data = str(final_message_data)

                                    event_data = json_lib.loads(final_message_data)
                                    yield f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"
                                except (json_lib.JSONDecodeError, AttributeError):
                                    pass
                            break

                        await asyncio.sleep(0.01)
                finally:
                    await pubsub.unsubscribe(channel)
                    if not workflow_task.done():
                        workflow_task.cancel()
                
            except Exception as e:
                logger.error(f"SSE stream generation failed: {str(e)}")
                error_event = {
                    "event": "error",
                    "data": {
                        "message": f"Stream generation failed: {str(e)}",
                        "error_code": "STREAM_ERROR",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }
                yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                yield f"data: {json.dumps({'event': 'eos', 'timestamp': datetime.utcnow().isoformat()}, ensure_ascii=False)}\n\n"
        
        # 返回SSE响应
        return StreamingResponse(
            generate_sse_stream(),
            media_type='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
            }
        )
        
    except Exception as e:
        logger.error(f"Plan travel failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/status/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务状态信息
    """
    try:
        # 创建事件总线
        redis_client = get_redis_client()
        event_bus = UnifiedEventBus(redis_client)
        
        # 获取任务状态
        status = await event_bus.get_task_status(task_id)
        
        if status is None:
            raise HTTPException(
                status_code=404, 
                detail=f"Task not found: {task_id}"
            )
        
        return {
            "task_id": task_id,
            "status": status,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get task status failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/health")
async def health_check():
    """
    健康检查端点
    
    Returns:
        服务健康状态
    """
    try:
        # 检查Redis连接
        redis_client = get_redis_client()
        
        # 检查工具注册表
        from src.tools.unified_registry import unified_registry
        tool_info = unified_registry.get_tool_info()
        
        return {
            "status": "healthy",
            "version": "3.0",
            "architecture": "unified",
            "components": {
                "redis": "connected",
                "tool_registry": tool_info,
                "event_bus": "available"
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Service unhealthy: {str(e)}"
        )


@router.get("/tools")
async def get_available_tools():
    """
    获取可用工具列表
    
    Returns:
        工具注册表信息
    """
    try:
        from src.tools.unified_registry import unified_registry
        
        tool_info = unified_registry.get_tool_info()
        action_schemas = unified_registry.get_all_action_schemas()
        
        return {
            "tool_registry": tool_info,
            "action_schemas": action_schemas,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Get tools failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/analyze")
async def analyze_intent(request: AnalyzeIntentRequest):
    """
    意图分析API端点 (V3.0 - 两阶段流程第一阶段)

    只执行意图分析阶段，包括框架分析和偏好分析

    Args:
        request: 意图分析请求

    Returns:
        SSE流式响应，包含意图分析进度和结果
    """
    try:
        # 准备输入数据
        input_data = {
            "user_query": request.user_query,
            "user_id": request.user_id or f'web_user_{int(datetime.utcnow().timestamp())}',
            "execution_mode": request.execution_mode,
            "user_profile": request.user_profile or {},
            "task_id": f"task_{int(datetime.utcnow().timestamp() * 1000)}{int(datetime.utcnow().microsecond / 1000)}",
            "vehicle_info": request.vehicle_info or {}
        }

        # 创建SSE响应生成器
        async def generate_intent_analysis_stream():
            try:
                # 创建事件总线
                redis_client = await get_redis_client()
                event_bus = UnifiedEventBus(redis_client)

                # 创建图实例
                graph = TravelPlannerGraphV3(event_bus=event_bus)

                # 发送开始事件
                start_event = {
                    'event': 'start',
                    'data': {'task_id': input_data['task_id'], 'phase': 'intent_analysis'},
                    'timestamp': datetime.utcnow().isoformat()
                }
                yield f"data: {json.dumps(start_event, ensure_ascii=False)}\n\n"

                # 只执行意图分析阶段（前三个节点）
                async for step in graph.stream_intent_analysis(input_data):
                    # 转换步骤为SSE事件
                    for node_name, node_result in step.items():
                        if isinstance(node_result, dict):
                            # 发送节点完成事件
                            event_data = {
                                "event": "node_complete",
                                "data": {
                                    "node_name": node_name,
                                    "result": node_result,
                                    "timestamp": datetime.utcnow().isoformat()
                                }
                            }
                            yield f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"

                # 发送意图分析完成事件
                intent_complete_event = {
                    'event': 'intent_complete',
                    'data': {'task_id': input_data['task_id']},
                    'timestamp': datetime.utcnow().isoformat()
                }
                yield f"data: {json.dumps(intent_complete_event, ensure_ascii=False)}\n\n"

                # 发送结束事件
                eos_event = {
                    'event': 'eos',
                    'timestamp': datetime.utcnow().isoformat()
                }
                yield f"data: {json.dumps(eos_event, ensure_ascii=False)}\n\n"

            except Exception as e:
                logger.error(f"Intent analysis stream generation failed: {str(e)}")
                error_event = {
                    "event": "error",
                    "data": {
                        "message": f"Intent analysis failed: {str(e)}",
                        "error_code": "INTENT_ANALYSIS_ERROR",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }
                yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                yield f"data: {json.dumps({'event': 'eos', 'timestamp': datetime.utcnow().isoformat()}, ensure_ascii=False)}\n\n"

        # 返回SSE响应
        return StreamingResponse(
            generate_intent_analysis_stream(),
            media_type='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
            }
        )

    except Exception as e:
        logger.error(f"Analyze intent failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
