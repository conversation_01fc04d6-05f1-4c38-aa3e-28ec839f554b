import logging
from fastapi import UploadFile, HTTPException
from typing import List
from src.database.minio_client import MinioClient
from src.tools.comfyui.comfyui import load_workflow, upload_image, send_prompt, wait_for_image
import json
import uuid
import traceback

logger = logging.getLogger("image_to_image")
minio_client = MinioClient()
server_name = ""

async def download_and_upload_to_minio(image_url: str, object_prefix: str = "image") -> str:
    import httpx
    async with httpx.AsyncClient() as client:
        resp = await client.get(image_url)
        resp.raise_for_status()
        content = resp.content
        object_name = f"{object_prefix}/{uuid.uuid4()}.png"
        minio_url = await minio_client.upload_file_content(content, object_name, content_type="image/jpeg")
        return minio_url

def build_image_to_image_workflow(prompt: str) -> dict:
    """构建图生图工作流"""
    try:
        workflow = load_workflow("magic_camera.json")
        if "17" not in workflow:
            logger.error("工作流中找不到节点17")
            raise HTTPException(status_code=500, detail="Node 17 not found in workflow")
        workflow["17"]["inputs"]["image"] = prompt
        logger.info(f"设置图片: {prompt}")
        if "63" not in workflow:
            logger.error("工作流中找不到节点63")
            raise HTTPException(status_code=500, detail="Node 63 not found in workflow")
        return workflow, "63"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

async def image_to_image_comfyui(file: UploadFile):
    try:
        logger.info(f"\n========== 开始转换图片 ==========")
        logger.info(f"文件名: {file.filename}")
        uploaded_filename = await upload_image(server_name, file.file, "待转换图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")
        workflow, output_node_id = build_image_to_image_workflow(uploaded_filename)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的图片URL: {image_url}")
        minio_url = await download_and_upload_to_minio(image_url)
        return minio_url
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用image_to_image接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

async def image_to_image(files: List[UploadFile]) -> List[str]:
    if not files:
        return []
    urls = []
    for file in files:
        if not file.filename or file.size == 0:
            print(f'Skipping empty file: {file.filename}, size: {file.size}')
            continue
        url = await image_to_image_comfyui(file)
        urls.append(url)
    print(urls)
    return urls 