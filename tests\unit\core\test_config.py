"""
配置管理系统的单元测试

测试 src.core.config 模块的功能。
"""
import os
import tempfile
import pytest
from unittest.mock import patch, mock_open
from pydantic import ValidationError


class TestLLMConfig:
    """测试 LLMConfig 数据模型"""
    
    def test_llm_config_creation_with_required_fields(self):
        """测试使用必需字段创建 LLMConfig"""
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/"
        )
        
        assert config.model == "glm-z1-flash"
        assert config.api_key == "test_key"
        assert config.base_url == "https://open.bigmodel.cn/api/paas/v4/"
        assert config.api_version is None
    
    def test_llm_config_with_optional_fields(self):
        """测试包含可选字段的 LLMConfig"""
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            api_version="v1"
        )
        
        assert config.api_version == "v1"
    
    def test_llm_config_missing_required_field_raises_error(self):
        """测试缺少必需字段时抛出验证错误"""
        from src.core.config import LLMConfig
        
        with pytest.raises(ValidationError):
            LLMConfig(model="glm-z1-flash")  # 缺少 api_key


class TestSettings:
    """测试 Settings 配置类"""
    
    def test_settings_creation_with_env_vars(self):
        """测试从环境变量创建 Settings"""
        # 模拟环境变量
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "test_basic_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "REDIS_URL": "redis://localhost:6379"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            from src.core.config import Settings
            
            settings = Settings()
            
            # 验证 reasoning_llm 配置
            assert settings.reasoning_llm.model == "glm-z1-flash"
            assert settings.reasoning_llm.api_key == "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
            assert settings.reasoning_llm.base_url == "https://open.bigmodel.cn/api/paas/v4/"
            
            # 验证 basic_llm 配置
            assert settings.basic_llm.model == "glm-4-flash"
            assert settings.basic_llm.api_key == "test_basic_key"
            
            # 验证其他配置
            assert settings.redis_url == "redis://localhost:6379"
    
    def test_get_llm_config_by_role(self):
        """测试按角色获取LLM配置"""
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "reasoning_key",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "basic_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            from src.core.config import Settings
            
            settings = Settings()
            
            # 测试获取 reasoning 角色配置
            reasoning_config = settings.get_llm_config_by_role("reasoning")
            assert reasoning_config.model == "glm-z1-flash"
            assert reasoning_config.api_key == "reasoning_key"
            
            # 测试获取 basic 角色配置
            basic_config = settings.get_llm_config_by_role("basic")
            assert basic_config.model == "glm-4-flash"
            assert basic_config.api_key == "basic_key"
    
    def test_get_llm_config_invalid_role_raises_error(self):
        """测试无效角色抛出错误"""
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "test_key",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "test_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            from src.core.config import Settings
            
            settings = Settings()
            
            with pytest.raises(ValueError, match="Unknown LLM role: invalid"):
                settings.get_llm_config_by_role("invalid")
    
    def test_settings_singleton_behavior(self):
        """测试全局 settings 单例的行为"""
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "test_key",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "test_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            from src.core.config import settings
            
            # settings 应该是一个可用的实例
            assert hasattr(settings, 'reasoning_llm')
            assert hasattr(settings, 'basic_llm')
            assert hasattr(settings, 'get_llm_config_by_role')


class TestConfigIntegration:
    """集成测试"""
    
    def test_config_model_dump_for_autogen_compatibility(self):
        """测试配置能正确转换为 AutoGen 需要的格式"""
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "basic_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            from src.core.config import Settings
            
            settings = Settings()
            reasoning_config = settings.get_llm_config_by_role("reasoning")
            
            # 转换为字典格式（供 AutoGen 使用）
            config_dict = reasoning_config.model_dump()
            
            assert config_dict["model"] == "glm-z1-flash"
            assert config_dict["api_key"] == "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
            assert config_dict["base_url"] == "https://open.bigmodel.cn/api/paas/v4/"
            assert isinstance(config_dict, dict) 