"""
Phase 3和Phase 4的格式化工具
"""

from typing import Dict, Any
from src.tools import register_tool
import json


# === Phase 3 工具：数据综合与智能决策 ===

@register_tool
def define_poi_scoring_models() -> str:
    """Phase 3: POI综合评分模型的格式定义"""
    schema = {
        "phase": 3,
        "name": "POI综合评分模型",
        "description": "建立不同类型POI的加权评分模型，筛选出Top N推荐",
        "scoring_models": {
            "停车场评分": {
                "formula": "Score = w1 * (1/价格) + w2 * (1/步行距离) + w3 * 评分",
                "weights": {"w1": 0.3, "w2": 0.4, "w3": 0.3},
                "output_format": {
                    "top_recommendations": {
                        "type": "array",
                        "maxItems": 3,
                        "items": {
                            "type": "object",
                            "properties": {
                                "poi_id": {"type": "string"},
                                "name": {"type": "string"},
                                "score": {"type": "number"},
                                "price_per_hour": {"type": "number"},
                                "walking_distance_meters": {"type": "integer"},
                                "rating": {"type": "number"}
                            }
                        }
                    }
                }
            },
            "餐厅评分": {
                "formula": "Score = w1 * 评分 + w2 * (1/人均消费) + w3 * 标签匹配度",
                "weights": {"w1": 0.4, "w2": 0.2, "w3": 0.4}
            },
            "景点评分": {
                "formula": "Score = w1 * 评分 + w2 * 标签匹配度 + w3 * (1/门票价格)",
                "weights": {"w1": 0.5, "w2": 0.3, "w3": 0.2}
            }
        }
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


# === Phase 4 工具：结构化结果生成 ===

@register_tool  
def define_final_itinerary_schema() -> str:
    """Phase 4: 最终行程JSON的完整格式定义"""
    schema = {
        "type": "object",
        "description": "符合PRD要求的完整行程JSON格式",
        "properties": {
            "trace_id": {"type": "string"},
            "user_id": {"type": "string"},
            "status": {"type": "string", "enum": ["completed", "in_progress", "failed"]},
            "raw_user_query": {"type": "string"},
            "summary": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "days": {"type": "integer"},
                    "destination_city": {"type": "string"},
                    "tags": {"type": "array", "items": {"type": "string"}},
                    "description": {"type": "string"}
                },
                "required": ["title", "days", "destination_city"]
            },
            "daily_plans": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "day": {"type": "integer"},
                        "theme": {"type": "string"},
                        "pois": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "poi_instance_id": {"type": "string"},
                                    "name": {"type": "string"},
                                    "category": {"type": "string", "enum": ["景点", "美食", "酒店", "购物", "其他"]},
                                    "address": {"type": "string"},
                                    "location": {
                                        "type": "object",
                                        "properties": {
                                            "longitude": {"type": "number"},
                                            "latitude": {"type": "number"}
                                        }
                                    }
                                },
                                "required": ["poi_instance_id", "name", "category"]
                            }
                        }
                    },
                    "required": ["day", "pois"]
                }
            }
        },
        "required": ["trace_id", "user_id", "status", "summary", "daily_plans"]
    }
    return json.dumps(schema, ensure_ascii=False, indent=2)


@register_tool
def format_llm_prompt_with_schema(base_prompt: str, schema_tool_name: str, context: Dict[str, Any] = None) -> str:
    """
    格式化LLM提示词，包含Function Call Schema
    
    Args:
        base_prompt: 基础提示词
        schema_tool_name: Schema工具名称
        context: 上下文信息
        
    Returns:
        包含Schema的完整提示词
    """
    from src.tools import tool_registry
    
    try:
        # 获取Schema
        schema_func = tool_registry._tools.get(schema_tool_name)
        if not schema_func:
            return f"{base_prompt}\n\n[ERROR: Schema工具 '{schema_tool_name}' 未找到]"
            
        # 调用Schema工具获取格式定义
        if context:
            schema_json = schema_func(**context)
        else:
            schema_json = schema_func()
            
        formatted_prompt = f"""
{base_prompt}

请严格按照以下JSON Schema格式返回结果：

{schema_json}

要求：
1. 必须返回有效的JSON格式
2. 严格遵循Schema中定义的字段类型和约束
3. 如果某些信息无法确定，使用null值
4. 确保所有required字段都有值
"""
        return formatted_prompt
        
    except Exception as e:
        return f"{base_prompt}\n\n[ERROR: 无法获取Schema格式: {str(e)}]" 