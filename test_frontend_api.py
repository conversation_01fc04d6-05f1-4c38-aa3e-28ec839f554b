#!/usr/bin/env python3
"""
前端API测试脚本

测试前端调用的API是否返回正确的数据
"""

import asyncio
import json
import aiohttp
from datetime import datetime

async def test_v3_api():
    """测试V3 API是否返回正确的莆田数据"""
    print("🌐 前端API测试")
    print("=" * 60)
    
    # 测试用例：用户的真实输入
    test_query = "我在福州闽东大厦，这周末要去莆田玩两天"
    
    print(f"📝 用户输入: {test_query}")
    print()
    
    # 测试V3 API
    url = "http://localhost:8000/api/v3/travel-planner/plan"
    
    payload = {
        "user_query": test_query,
        "user_id": "test_user_frontend",
        "execution_mode": "full_planning",
        "user_profile": {},
        "vehicle_info": {}
    }
    
    print("🚀 调用V3 API...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
    print()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                print(f"📊 响应状态: {response.status}")
                
                if response.status != 200:
                    text = await response.text()
                    print(f"❌ API调用失败: {text}")
                    return
                
                print("✅ 开始接收SSE流...")
                print("-" * 40)
                
                # 收集所有事件
                events = []
                itinerary_updates = []
                
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    
                    if line_str.startswith('data: '):
                        try:
                            event_data = json.loads(line_str[6:])
                            events.append(event_data)
                            
                            # 打印所有事件
                            event_type = event_data.get('event')
                            print(f"📨 {event_type}: {event_data.get('data', {}).get('message', '')}")

                            # 收集行程更新事件
                            if event_type == 'ITINERARY_UPDATE':
                                itinerary_updates.append(event_data)

                                # 检查POI信息
                                data = event_data.get('data', {})
                                activity = data.get('activity', {})
                                poi_name = activity.get('name', '')
                                poi_address = activity.get('address', '')

                                print(f"  📍 POI: {poi_name}")
                                print(f"  📍 地址: {poi_address}")

                                # 检查是否是莆田的POI
                                if "莆田" in poi_address or any(keyword in poi_name for keyword in ["湄洲", "妈祖", "绶溪", "凤凰山"]):
                                    print(f"  ✅ 这是莆田的POI")
                                elif "北京" in poi_address or any(keyword in poi_name for keyword in ["天安门", "故宫", "颐和园"]):
                                    print(f"  ❌ 这是北京的POI - 数据错误！")
                                else:
                                    print(f"  ❓ 城市不明确")
                                print()
                        
                        except json.JSONDecodeError as e:
                            print(f"⚠️ JSON解析失败: {line_str}")
                
                print("-" * 40)
                print("📊 测试结果分析")
                print("-" * 40)
                
                print(f"总事件数: {len(events)}")
                print(f"行程更新事件数: {len(itinerary_updates)}")
                
                # 分析行程更新事件中的城市信息
                putian_pois = 0
                beijing_pois = 0
                unknown_pois = 0
                
                for update in itinerary_updates:
                    activity = update.get('data', {}).get('activity', {})
                    poi_name = activity.get('name', '')
                    poi_address = activity.get('address', '')
                    
                    if "莆田" in poi_address or any(keyword in poi_name for keyword in ["湄洲", "妈祖", "绶溪", "凤凰山"]):
                        putian_pois += 1
                    elif "北京" in poi_address or any(keyword in poi_name for keyword in ["天安门", "故宫", "颐和园"]):
                        beijing_pois += 1
                    else:
                        unknown_pois += 1
                
                print(f"莆田POI数量: {putian_pois}")
                print(f"北京POI数量: {beijing_pois}")
                print(f"未知城市POI数量: {unknown_pois}")
                
                if putian_pois > 0 and beijing_pois == 0:
                    print("✅ API返回了正确的莆田数据")
                elif beijing_pois > 0:
                    print("❌ API返回了错误的北京数据")
                    print("🔧 问题在后端API")
                else:
                    print("❓ 数据不明确，需要进一步检查")
                
                # 检查最终规划结果
                planning_completed_events = [e for e in events if e.get('event') == 'PLANNING_COMPLETED']
                if planning_completed_events:
                    final_event = planning_completed_events[-1]
                    final_data = final_event.get('data', {})
                    print(f"\n📋 最终规划结果:")
                    print(json.dumps(final_data, ensure_ascii=False, indent=2))
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

async def main():
    """主函数"""
    print("🚨 前端API数据一致性测试")
    print("=" * 80)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    await test_v3_api()
    
    print("\n" + "=" * 80)
    print("🎯 测试总结")
    print("=" * 80)
    print("如果API返回了正确的莆田数据，问题在前端显示逻辑")
    print("如果API返回了错误的北京数据，问题在后端处理逻辑")
    print("请根据测试结果进行相应的修复")

if __name__ == "__main__":
    asyncio.run(main())
