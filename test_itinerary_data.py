#!/usr/bin/env python3
"""
测试行程数据格式和内容
"""
import asyncio
import httpx
import json
import re

async def test_itinerary_data():
    """测试行程数据格式"""
    url = "http://localhost:8000/api/v3/travel-planner/plan"
    
    payload = {
        "user_query": "我想去北京玩3天，喜欢历史文化景点，需要自驾出行，预算中等",
        "user_id": "1",
        "execution_mode": "interactive",
        "vehicle_info": {
            "model": "特斯拉Model 3",
            "range": 500,
            "chargingType": "快充",
            "executionMode": "interactive"
        }
    }
    
    print("🚀 开始测试行程数据...")
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(url, json=payload)
            
            if response.status_code == 200:
                print("✅ API请求成功")
                
                # 解析SSE流
                content = response.text
                lines = content.split('\n')
                
                itinerary_updates = []
                planning_logs = []
                
                for line in lines:
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])  # 去掉 'data: ' 前缀
                            
                            if data.get('event') == 'ITINERARY_UPDATE':
                                itinerary_updates.append(data['data'])
                                
                            elif data.get('event') == 'PLANNING_LOG':
                                planning_logs.append(data['data'])
                                
                        except json.JSONDecodeError:
                            continue
                
                print(f"\n📊 收到 {len(itinerary_updates)} 个行程更新事件")
                print(f"📊 收到 {len(planning_logs)} 个规划日志事件")
                
                # 分析行程更新数据
                if itinerary_updates:
                    print("\n🔍 行程更新数据分析:")
                    
                    # 按天分组
                    days_data = {}
                    for update in itinerary_updates:
                        day = update.get('day')
                        if day not in days_data:
                            days_data[day] = []
                        days_data[day].append(update['activity'])
                    
                    for day in sorted(days_data.keys()):
                        activities = days_data[day]
                        print(f"\n  📅 第{day}天 ({len(activities)}个活动):")
                        
                        for i, activity in enumerate(activities, 1):
                            name = activity.get('name', '未知')
                            activity_type = activity.get('type', '未知')
                            start_time = activity.get('start_time', '')
                            end_time = activity.get('end_time', '')
                            duration = activity.get('duration', '')
                            
                            print(f"    {i}. {name} ({activity_type})")
                            if start_time and end_time:
                                print(f"       ⏰ {start_time} - {end_time} ({duration})")
                            elif duration:
                                print(f"       ⏰ 时长: {duration}")
                            
                            # 检查是否有餐饮安排
                            if activity_type == 'dining':
                                meal_type = activity.get('meal_type', '')
                                if meal_type:
                                    print(f"       🍽️ 餐饮类型: {meal_type}")
                
                # 分析规划日志
                if planning_logs:
                    print(f"\n🧠 规划思考过程:")
                    for log in planning_logs:
                        step = log.get('reasoning_step', 0)
                        message = log.get('message', '')
                        print(f"  步骤{step}: {message}")
                
                # 验证时间安排合理性
                print(f"\n✅ 时间安排合理性检查:")
                for day in sorted(days_data.keys()):
                    activities = days_data[day]
                    
                    # 检查是否有午餐
                    has_lunch = any(act.get('meal_type') == 'lunch' or 
                                  (act.get('type') == 'dining' and 
                                   '12:' in act.get('start_time', '')) 
                                  for act in activities)
                    
                    # 检查是否有晚餐
                    has_dinner = any(act.get('meal_type') == 'dinner' or 
                                   (act.get('type') == 'dining' and 
                                    '18:' in act.get('start_time', '')) 
                                   for act in activities)
                    
                    print(f"  第{day}天: 午餐{'✅' if has_lunch else '❌'} 晚餐{'✅' if has_dinner else '❌'}")
                    
                    # 检查时间冲突
                    timed_activities = [act for act in activities if act.get('start_time')]
                    if len(timed_activities) > 1:
                        # 简单检查时间顺序
                        times = [act.get('start_time', '') for act in timed_activities]
                        sorted_times = sorted(times)
                        if times == sorted_times:
                            print(f"    ⏰ 时间安排顺序正确")
                        else:
                            print(f"    ⚠️ 时间安排可能有问题")
                
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
    except Exception as e:
        print(f"💥 测试异常: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_itinerary_data())
