<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoPilot AI - 智能旅行规划助手 V3.0</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        /* V3.0 特定样式 */
        .v3-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .progress-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .phase-progress {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .phase-step {
            flex: 1;
            text-align: center;
            padding: 15px;
            margin: 0 5px;
            border-radius: 8px;
            background: #fff;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .phase-step.running {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .phase-step.completed {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .phase-step.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
        }
        
        .phase-step.running .step-icon {
            background: #007bff;
        }
        
        .phase-step.completed .step-icon {
            background: #28a745;
        }
        
        .phase-step.error .step-icon {
            background: #dc3545;
        }
        
        .step-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .step-status {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .current-phase {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .phase-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .phase-description {
            color: #6c757d;
        }
        
        .thinking-log, .action-log {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .log-content {
            max-height: 200px;
            overflow-y: auto;
            background: #fff;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid #f1f3f4;
            font-size: 0.9em;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .analysis-results, .itinerary-display {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .day-plan {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .activity-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .activity-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .activity-details {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .input-section {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            resize: vertical;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .task-id {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="v3-container">
        <header>
            <h1>AutoPilot AI - 智能旅行规划助手</h1>
            <p class="version-info">V3.0 统一架构版 | 支持两阶段工作流和ICP迭代规划</p>
        </header>
        
        <main>
            <!-- 输入区域 -->
            <section class="input-section">
                <h2>告诉我您的旅行需求</h2>
                <div class="input-group">
                    <label for="user-query">旅行需求描述</label>
                    <textarea 
                        id="user-query" 
                        placeholder="例如：我想去北京玩三天，喜欢历史文化景点，预算中等，有两个小孩..."
                    ></textarea>
                </div>
                <div class="button-group">
                    <button id="start-planning" class="btn btn-primary">开始智能规划</button>
                    <button id="stop-planning" class="btn btn-secondary">停止规划</button>
                </div>
            </section>
            
            <!-- 进度指示器 -->
            <section id="progress-container" class="progress-container">
                <!-- 由JavaScript动态生成 -->
            </section>
            
            <!-- 当前阶段显示 -->
            <section id="phase-display">
                <!-- 由JavaScript动态生成 -->
            </section>
            
            <!-- 结果显示 -->
            <section id="result-display">
                <!-- 由JavaScript动态生成 -->
            </section>
        </main>
        
        <footer>
            <p>&copy; 2024 AutoPilot AI. 基于统一架构的智能旅行规划系统。</p>
        </footer>
    </div>
    
    <script src="{{ url_for('static', filename='js/app-v3.js') }}"></script>
</body>
</html>
