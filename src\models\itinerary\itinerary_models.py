from datetime import date, datetime
from typing import List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field

class BaseResponse(BaseModel):
    code: int = Field(200, description="状态码")
    message: str = Field("success", description="提示信息")

class Pagination(BaseModel):
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")
    total: int = Field(0, ge=0, description="总数")

class POIType(BaseModel):
    id: int = Field(..., description="类型ID")
    type_key: str = Field(..., description="类型键名")
    display_name: str = Field(..., description="显示名称")
    icon_name: Optional[str] = Field(None, description="图标名称")

class POILocation(BaseModel):
    latitude: float = Field(..., ge=-90, le=90, description="纬度")
    longitude: float = Field(..., ge=-180, le=180, description="经度")

class POIBase(BaseModel):
    id: int = Field(..., description="POI ID")
    name: str = Field(..., max_length=100, description="名称")
    type_id: int = Field(..., description="类型ID")
    address: Optional[str] = Field(None, max_length=255, description="地址")

class POIDetail(POIBase):
    latitude: float = Field(..., description="纬度")
    longitude: float = Field(..., description="经度")
    description: Optional[str] = Field(None, description="详细描述")
    images: Optional[List[str]] = Field(None, description="图片URL列表")
    rating: Optional[float] = Field(None, ge=0, le=5, description="评分")
    opening_hours: Optional[str] = Field(None, description="营业时间")
    phone_number: Optional[str] = Field(None, description="联系电话")
    distance: Optional[float] = Field(None, ge=0, description="距离(米)")

class NearbyPOIQuery(BaseModel):
    latitude: float = Field(..., ge=-90, le=90, description="纬度")
    longitude: float = Field(..., ge=-180, le=180, description="经度")
    type_id: Optional[int] = Field(None, description="POI类型ID")
    radius: int = Field(1000, ge=100, le=5000, description="搜索半径(米)")
    keyword: Optional[str] = Field(None, max_length=50, description="搜索关键词")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=50, description="每页数量")

class POIListResponse(BaseResponse):
    data: List[POIDetail]

class RoutePOI(BaseModel):
    poi_id: int = Field(..., description="POI ID")
    sequence: int = Field(..., ge=1, description="顺序")

class TransportMode(str, Enum):
    WALKING = "walking"
    DRIVING = "driving"
    TRANSIT = "transit"
    BICYCLING = "bicycling"

class RouteRequest(BaseModel):
    origin: POILocation = Field(..., description="起点坐标")
    pois: List[RoutePOI] = Field(..., min_items=1, description="POI列表")
    transport_mode: TransportMode = Field(
        TransportMode.WALKING, description="交通方式"
    )

class RouteSegment(BaseModel):
    poi_id: int = Field(..., description="POI ID")
    name: str = Field(..., description="POI名称")
    distance_from_previous: float = Field(..., ge=0, description="与上一站距离(米)")
    duration_from_previous: int = Field(..., ge=0, description="与上一站时间(秒)")
    path: List[List[float]] = Field(..., description="路径坐标点列表")

class BoundingBox(BaseModel):
    northeast: POILocation = Field(..., description="东北角坐标")
    southwest: POILocation = Field(..., description="西南角坐标")

class RouteResponse(BaseModel):
    total_distance: float = Field(..., ge=0, description="总距离(米)")
    total_duration: int = Field(..., ge=0, description="总时间(秒)")
    route: List[RouteSegment] = Field(..., description="路径分段信息")
    bounds: BoundingBox = Field(..., description="路径边界框")

class RoutePlanResponse(BaseResponse):
    data: RouteResponse

class ItineraryTag(BaseModel):
    id: int = Field(..., description="标签ID")
    name: str = Field(..., max_length=50, description="标签名称")

class ItineraryDayPOI(BaseModel):
    id: int = Field(..., description="POI ID")
    sequence: int = Field(..., ge=1, description="当天顺序")
    name: str = Field(..., description="POI 名称")
    poi_type: Optional[POIType] = Field(None, description="POI 类型")
    user_notes: Optional[str] = Field(None, description="用户针对这一站点的个人备注")

class ItineraryWeather(BaseModel):
    condition: str = Field(..., description="天气状况")
    high_temp: float = Field(..., description="最高温度")
    low_temp: float = Field(..., description="最低温度")

class ItineraryDayDetail(BaseModel):
    day_number: int = Field(..., ge=1, description="第几天")
    summary: Optional[str] = Field(None, description="当日摘要")
    weather: Optional[ItineraryWeather] = Field(None, description="天气信息")
    pois: List[ItineraryDayPOI] = Field(..., description="当天POI列表")

class ItineraryDays(BaseModel):
    user_id: int = Field(..., description="用户ID")
    days: Optional[List[ItineraryDayDetail]] = Field(None, description="每日详情")

class ItineraryBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    title: str = Field(..., description="行程标题")
    city_name: str = Field(..., description="城市名称")
    total_days: int = Field(..., ge=1, description="总天数")
    start_date: Optional[date] = Field(None, description="开始日期")
    cover_image_url: Optional[str] = Field(None, description="行程封面图URL")
    notes: Optional[str] = Field(None, description="行程备注或注意事项")
    tags: Optional[List[ItineraryTag]] = Field(None, description="标签列表")

class ItineraryDetail(ItineraryBase):
    id: int = Field(..., description="行程ID")
    days: Optional[List[ItineraryDayDetail]] = Field(None, description="每日详情")

class ItineraryResponse(BaseResponse):
    data: ItineraryDetail

class VlogStyle(BaseModel):
    id: int = Field(..., description="风格ID")
    name: str = Field(..., max_length=50, description="风格名称")
    preview_url: str = Field(..., description="预览URL")

class VlogMediaItem(BaseModel):
    media_id: int = Field(..., description="媒体ID")
    type: str = Field(..., pattern="^(IMAGE|VIDEO)$", description="媒体类型")

class VlogCreate(BaseModel):
    itinerary_id: int = Field(..., description="关联行程ID")
    style_id: int = Field(..., description="风格ID")
    template_id: Optional[int] = Field(None, description="模板ID")
    aspect_ratio: str = Field(
        "16:9", pattern="^(16:9|9:16|1:1|4:3)$", 
        description="视频比例"
    )
    duration: int = Field(180, ge=30, le=600, description="视频时长(秒)")
    user_text: Optional[str] = Field(None, description="用户输入文字")
    media_items: List[VlogMediaItem] = Field(..., description="媒体素材列表")

class VlogStatus(str, Enum):
    QUEUED = "QUEUED"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class VlogStatusResponse(BaseModel):
    id: int = Field(..., description="Vlog ID")
    status: VlogStatus = Field(..., description="当前状态")
    progress: int = Field(0, ge=0, le=100, description="进度百分比")
    final_video_url: Optional[str] = Field(None, description="视频URL")

class VlogResponse(BaseResponse):
    data: VlogStatusResponse

class UserStats(BaseModel):
    trip_count: int = Field(0, ge=0, description="出行次数")
    city_count: int = Field(0, ge=0, description="城市数量")
    total_mileage: float = Field(0.0, ge=0, description="总里程(公里)")
    total_days: int = Field(0, ge=0, description="总天数")

class UserSettings(BaseModel):
    narration_enabled: bool = Field(False, description="是否开启解说")
    # narration_trigger_distance: int = Field(
    #     500, ge=100, le=1000, description="触发解说的距离(米)"
    # )
    in_poi_guide_enabled: bool = Field(
        False, description="是否推送景点内攻略"
    )
    proactive_recommend_enabled: bool = Field(
        False, description="是否开启主动推荐"
    )
    # proactive_recommend_mode: str = Field(
    #     "holidays_and_weekends", 
    #     description="推荐模式: once/holidays_and_weekends/custom"
    # )

class UserStatsResponse(BaseResponse):
    data: UserStats

class UserSettingsResponse(BaseResponse):
    data: UserSettings