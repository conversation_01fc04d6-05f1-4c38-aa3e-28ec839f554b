#!/usr/bin/env python3
"""
数据流传递诊断脚本

测试从意图分析到ICP规划的完整数据传递链路
"""

import asyncio
import json
from datetime import datetime
from src.core.logger import get_logger
from src.agents.services.analysis_service import AnalysisService
from src.tools.travel_planner.icp_tools import select_next_action

logger = get_logger("debug_data_flow")

async def test_complete_data_flow():
    """测试完整的数据流传递"""
    print("🔄 数据流传递诊断")
    print("=" * 60)
    
    # 测试用例：用户的真实输入
    test_query = "我在福州闽东大厦，这周末要去莆田玩两天"
    user_id = "test_user_1"
    
    print(f"📝 用户输入: {test_query}")
    print()
    
    try:
        # 步骤1: 意图分析
        print("🎯 步骤1: 意图分析")
        print("-" * 40)
        
        analysis_service = AnalysisService()
        core_intent = await analysis_service.analyze_core_intent(
            original_query=test_query,
            user_id=user_id,
            user_profile={}
        )
        
        preference_analysis = await analysis_service.analyze_preferences(
            core_intent=core_intent,
            user_profile={},
            user_memories=[]
        )
        
        # 模拟整合过程
        consolidated_intent = {
            "destinations": core_intent.get("destinations", []),
            "days": core_intent.get("days", 2),
            "travel_theme": core_intent.get("travel_theme", "leisure"),
            "preferences": preference_analysis.get("preferences", {}),
            "transportation": core_intent.get("transportation", {}),
            "budget": core_intent.get("budget", {}),
            "travelers": core_intent.get("travelers", {})
        }
        
        print("✅ 整合后的意图:")
        print(json.dumps(consolidated_intent, ensure_ascii=False, indent=2))
        
        destinations = consolidated_intent.get("destinations", [])
        print(f"\n🎯 目的地: {destinations}")
        
        # 步骤2: 测试ICP规划的数据接收
        print("\n🤖 步骤2: ICP规划数据接收测试")
        print("-" * 40)
        
        # 模拟ICP规划的初始状态
        icp_state = {
            "consolidated_intent": consolidated_intent,
            "daily_plans": {},
            "current_phase": "planning",
            "planning_log": []
        }
        
        print("✅ ICP状态中的consolidated_intent:")
        icp_consolidated = icp_state.get("consolidated_intent", {})
        icp_destinations = icp_consolidated.get("destinations", [])
        print(f"🎯 ICP接收到的目的地: {icp_destinations}")
        
        if "莆田" in icp_destinations:
            print("✅ ICP正确接收了莆田作为目的地")
        else:
            print("❌ ICP未能正确接收莆田作为目的地")
            print("⚠️ 数据传递出现问题！")
        
        # 步骤3: 测试行动规划
        print("\n📋 步骤3: 行动规划测试")
        print("-" * 40)
        
        # 模拟规划思考
        planning_thought = {
            "current_situation": "开始规划莆田2日游",
            "suggested_action": {
                "action_type": "search_poi",
                "reason": "需要搜索莆田的景点信息"
            },
            "confidence": 0.9
        }
        
        # 调用行动规划
        action_plan = select_next_action(
            thought_result=planning_thought,
            available_tools=["search_poi", "search_accommodation"],
            current_state=icp_state
        )
        
        print("✅ 生成的行动计划:")
        print(json.dumps(action_plan, ensure_ascii=False, indent=2))
        
        # 检查行动计划中的城市参数
        selected_action = action_plan.get("selected_action", {})
        parameters = selected_action.get("parameters", {})
        action_city = parameters.get("city", "未知")
        
        print(f"\n🏙️ 行动计划中的城市: {action_city}")
        
        if action_city == "莆田":
            print("✅ 行动计划正确使用了莆田作为搜索城市")
        else:
            print(f"❌ 行动计划使用了错误的城市: {action_city}")
            print("⚠️ 这就是问题所在！")
        
        # 步骤4: 模拟POI搜索执行
        print("\n🔍 步骤4: POI搜索执行测试")
        print("-" * 40)
        
        if selected_action.get("tool_name") == "search_poi":
            from src.tools.travel_planner.amap_poi_tools import search_poi
            
            search_params = parameters
            print(f"🔍 执行POI搜索，参数: {search_params}")
            
            # 执行搜索
            search_results = await search_poi(
                keywords=search_params.get("keywords", "景点"),
                city=search_params.get("city", "北京"),
                limit=3
            )
            
            print(f"✅ 搜索结果 ({len(search_results)}个):")
            for i, poi in enumerate(search_results):
                name = poi.get("name", "未知")
                address = poi.get("address", "未知地址")
                print(f"  {i+1}. {name} - {address}")
            
            # 分析搜索结果
            result_addresses = [poi.get("address", "") for poi in search_results]
            putian_results = any("莆田" in addr for addr in result_addresses)
            beijing_results = any("北京" in addr for addr in result_addresses)
            
            if putian_results:
                print("✅ 搜索结果包含莆田的POI")
            elif beijing_results:
                print("❌ 搜索结果包含北京的POI - 这说明使用了错误的城市参数")
            else:
                print("⚠️ 搜索结果城市不明确")
        
        return {
            "consolidated_intent": consolidated_intent,
            "action_plan": action_plan,
            "search_city": action_city
        }
        
    except Exception as e:
        print(f"❌ 数据流测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

async def test_fallback_scenarios():
    """测试各种回退场景"""
    print("\n🔄 回退场景测试")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "正常情况",
            "consolidated_intent": {"destinations": ["莆田"]},
            "expected_city": "莆田"
        },
        {
            "name": "空目的地列表",
            "consolidated_intent": {"destinations": []},
            "expected_city": "北京"
        },
        {
            "name": "None目的地",
            "consolidated_intent": {"destinations": None},
            "expected_city": "北京"
        },
        {
            "name": "缺少destinations字段",
            "consolidated_intent": {},
            "expected_city": "北京"
        },
        {
            "name": "空字符串目的地",
            "consolidated_intent": {"destinations": [""]},
            "expected_city": "北京"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        print(f"输入: {case['consolidated_intent']}")
        
        # 模拟ICP工具中的逻辑
        consolidated_intent = case["consolidated_intent"]
        destinations = consolidated_intent.get("destinations", ["北京"])
        current_destination = destinations[0] if destinations and destinations[0] else "北京"
        
        print(f"结果: {current_destination}")
        print(f"预期: {case['expected_city']}")
        
        if current_destination == case["expected_city"]:
            print("✅ 回退逻辑正确")
        else:
            print("❌ 回退逻辑有问题")

async def main():
    """主函数"""
    print("🚨 数据流传递完整诊断")
    print("=" * 80)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行完整数据流测试
    result = await test_complete_data_flow()
    
    # 执行回退场景测试
    await test_fallback_scenarios()
    
    print("\n" + "=" * 80)
    print("🎯 诊断总结")
    print("=" * 80)
    
    if result:
        search_city = result.get("search_city")
        if search_city == "莆田":
            print("✅ 数据流传递完全正确")
            print("❓ 问题可能在前端显示或其他环节")
        else:
            print(f"❌ 数据流传递有问题，最终使用了错误的城市: {search_city}")
            print("🔧 需要检查ICP规划阶段的数据处理逻辑")
    else:
        print("❌ 数据流测试失败")
        print("🔧 需要检查整个数据传递链路")
    
    print("\n📋 下一步行动:")
    print("1. 如果数据流正确，检查前端显示逻辑")
    print("2. 如果数据流有问题，修复ICP规划阶段")
    print("3. 添加更多的日志记录来跟踪数据传递")
    print("4. 确保所有默认值都使用正确的城市")

if __name__ == "__main__":
    asyncio.run(main())
