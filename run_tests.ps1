#!/usr/bin/env powershell
<#
.SYNOPSIS
    AutoPilot AI 快速测试脚本

.DESCRIPTION
    简化的测试运行脚本，自动切换到正确目录并运行测试

.PARAMETER Type
    测试类型：agent, expert, unit, all (默认: all)

.PARAMETER Quick
    快速测试模式

.EXAMPLE
    .\run_tests.ps1
    # 运行所有测试

.EXAMPLE
    .\run_tests.ps1 -Type agent
    # 只运行Agent测试

.EXAMPLE
    .\run_tests.ps1 -Quick
    # 快速测试模式
#>

param(
    [ValidateSet("agent", "expert", "unit", "all")]
    [string]$Type = "all",
    [switch]$Quick
)

Write-Host "🚀 AutoPilot AI 测试运行器" -ForegroundColor Cyan
Write-Host "=" * 50

# 检查虚拟环境
if (-not $env:VIRTUAL_ENV) {
    Write-Host "⚠️ 未检测到虚拟环境，尝试激活..." -ForegroundColor Yellow
    if (Test-Path ".venv\Scripts\Activate.ps1") {
        & .\.venv\Scripts\Activate.ps1
        Write-Host "✅ 虚拟环境已激活" -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到虚拟环境，请先创建：python -m venv .venv" -ForegroundColor Red
        exit 1
    }
}

Write-Host "📁 当前工作目录: $PWD" -ForegroundColor Cyan
Write-Host "🐍 虚拟环境: $env:VIRTUAL_ENV" -ForegroundColor Cyan

try {
    switch ($Type) {
        "agent" {
            Write-Host "`n🤖 运行Agent集成测试..." -ForegroundColor Blue
            python tests/integration/test_agent_system.py
        }
        "expert" {
            Write-Host "`n🐍 运行Python专家测试..." -ForegroundColor Blue
            python tests/integration/test_python_expert_integration.py
        }
        "unit" {
            Write-Host "`n🧪 运行单元测试..." -ForegroundColor Blue
            if ($Quick) {
                pytest tests/unit/agents/ -v
            } else {
                pytest tests/unit/agents/ -v --cov=src/agents --cov-report=term-missing
            }
        }
        "all" {
            Write-Host "`n🔄 运行完整测试套件..." -ForegroundColor Blue
            if ($Quick) {
                & .\tests\scripts\test_all_agents.ps1 -Quick
            } else {
                & .\tests\scripts\test_all_agents.ps1
            }
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✅ 测试完成！" -ForegroundColor Green
    } else {
        Write-Host "`n❌ 测试失败，退出码: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
    
} catch {
    Write-Host "`n❌ 测试运行出错: $_" -ForegroundColor Red
    exit 1
} 