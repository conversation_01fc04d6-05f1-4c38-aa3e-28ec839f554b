<mxfile host="Electron" modified="2024-01-15T10:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" etag="AutoPilot-AI-Multimodal-Flow" version="22.1.16" type="device">
  <diagram name="车端多模态车云协同流程" id="autopilot-ai-multimodal-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 车端系统层 -->
        <mxCell id="vehicle_layer" value="车端系统层" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#e8f5e8;strokeColor=#4caf50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="1050" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="vehicle_sensing" value="🚗 车端智能感知触发&#xa;・DMS/OMS异常检测&#xa;・CV模型物体识别&#xa;・用户主动请求" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontStyle=1;" vertex="1" parent="vehicle_layer">
          <mxGeometry x="20" y="40" width="180" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="vehicle_capture" value="📸 关键帧捕获与预处理&#xa;・智能抽帧策略&#xa;・图像质量评估&#xa;・WebP格式压缩" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ff9800;fontStyle=1;" vertex="1" parent="vehicle_layer">
          <mxGeometry x="220" y="40" width="180" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="vehicle_auth" value="🔐 上传凭证申请&#xa;・车辆身份验证&#xa;・媒体文件元信息&#xa;・安全权限检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#e91e63;fontStyle=1;" vertex="1" parent="vehicle_layer">
          <mxGeometry x="420" y="40" width="180" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="vehicle_upload" value="☁️ 异步文件上传&#xa;・分块传输机制&#xa;・断点续传支持&#xa;・网络自适应" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#2196f3;fontStyle=1;" vertex="1" parent="vehicle_layer">
          <mxGeometry x="620" y="40" width="180" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="vehicle_api" value="📤 元数据API请求&#xa;・上下文信息封装&#xa;・媒体引用标识&#xa;・业务请求发送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#9c27b0;fontStyle=1;" vertex="1" parent="vehicle_layer">
          <mxGeometry x="820" y="40" width="180" height="90" as="geometry" />
        </mxCell>
        
        <!-- 云端认证层 -->
        <mxCell id="auth_layer" value="云端认证层" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#fce4ec;strokeColor=#e91e63;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="350" y="230" width="250" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="auth_service" value="🛡️ 安全认证服务&#xa;・Token验证&#xa;・权限管理&#xa;・预签名URL生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#e91e63;fontStyle=1;" vertex="1" parent="auth_layer">
          <mxGeometry x="20" y="40" width="210" height="50" as="geometry" />
        </mxCell>
        
        <!-- 对象存储层 -->
        <mxCell id="storage_layer" value="对象存储层" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#e3f2fd;strokeColor=#2196f3;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="650" y="230" width="280" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="storage_service" value="💾 分布式对象存储&#xa;・高可用存储(S3/OSS/MinIO)&#xa;・CDN全球加速&#xa;・生命周期管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#2196f3;fontStyle=1;" vertex="1" parent="storage_layer">
          <mxGeometry x="20" y="40" width="240" height="50" as="geometry" />
        </mxCell>
        
        <!-- 云端智能处理层 -->
        <mxCell id="processing_layer" value="云端智能处理层" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#f3e5f5;strokeColor=#9c27b0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="150" y="360" width="850" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="central_hub" value="🎯 中枢智能路由&#xa;・请求分析&#xa;・任务分类&#xa;・负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#9c27b0;fontStyle=1;" vertex="1" parent="processing_layer">
          <mxGeometry x="330" y="40" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="route_decision" value="⚡ 处理路径选择" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#ffc107;strokeWidth=3;fontStyle=1;" vertex="1" parent="processing_layer">
          <mxGeometry x="360" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="fast_system" value="🏃 快系统处理&#xa;・基础CV分析&#xa;・标准化响应&#xa;・低延迟处理&#xa;响应时间: &lt;500ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontStyle=1;" vertex="1" parent="processing_layer">
          <mxGeometry x="150" y="120" width="180" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="slow_system" value="🧠 慢系统处理&#xa;・多模态大模型&#xa;・深度场景理解&#xa;・个性化推理&#xa;・复杂决策规划" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#f44336;fontStyle=1;" vertex="1" parent="processing_layer">
          <mxGeometry x="520" y="120" width="180" height="70" as="geometry" />
        </mxCell>
        
        <!-- 结果处理与返回层 -->
        <mxCell id="result_layer" value="结果处理与返回层" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#f1f8e9;strokeColor=#8bc34a;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="250" y="590" width="650" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="result_format" value="📋 结果标准化&#xa;・统一响应格式&#xa;・执行指令生成&#xa;・清理策略制定" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#8bc34a;fontStyle=1;" vertex="1" parent="result_layer">
          <mxGeometry x="50" y="40" width="200" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="result_present" value="📱 车端结果呈现&#xa;・语音播报&#xa;・屏幕显示&#xa;・HUD投影&#xa;・执行动作" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#009688;fontStyle=1;" vertex="1" parent="result_layer">
          <mxGeometry x="400" y="40" width="200" height="70" as="geometry" />
        </mxCell>
        
        <!-- 主流程连接线 -->
        <mxCell id="flow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#4caf50;" edge="1" parent="1" source="vehicle_sensing" target="vehicle_capture">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#ff9800;" edge="1" parent="1" source="vehicle_capture" target="vehicle_auth">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#e91e63;" edge="1" parent="1" source="vehicle_auth" target="auth_service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow4" value="返回预签名URL" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2196f3;dashed=1;" edge="1" parent="1" source="auth_service" target="vehicle_upload">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="525" y="200" as="sourcePoint" />
            <mxPoint x="710" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="525" y="200" />
              <mxPoint x="710" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2196f3;" edge="1" parent="1" source="vehicle_upload" target="storage_service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow6" value="上传成功确认" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#9c27b0;dashed=1;" edge="1" parent="1" source="storage_service" target="vehicle_api">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="790" y="200" as="sourcePoint" />
            <mxPoint x="910" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="790" y="200" />
              <mxPoint x="910" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#9c27b0;" edge="1" parent="1" source="vehicle_api" target="central_hub">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="910" y="200" as="sourcePoint" />
            <mxPoint x="570" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="910" y="340" />
              <mxPoint x="570" y="340" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#9c27b0;" edge="1" parent="1" source="central_hub" target="route_decision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow9" value="简单/实时任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#4caf50;" edge="1" parent="1" source="route_decision" target="fast_system">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow10" value="复杂/深度任务" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#f44336;" edge="1" parent="1" source="route_decision" target="slow_system">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#8bc34a;" edge="1" parent="1" source="fast_system" target="result_format">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="390" y="560" as="sourcePoint" />
            <mxPoint x="350" y="630" as="targetPoint" />
            <Array as="points">
              <mxPoint x="390" y="580" />
              <mxPoint x="350" y="580" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#8bc34a;" edge="1" parent="1" source="slow_system" target="result_format">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="760" y="560" as="sourcePoint" />
            <mxPoint x="350" y="630" as="targetPoint" />
            <Array as="points">
              <mxPoint x="760" y="580" />
              <mxPoint x="350" y="580" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#009688;" edge="1" parent="1" source="result_format" target="result_present">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 媒体文件获取连接线 -->
        <mxCell id="media_flow1" value="获取媒体文件" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#2196f3;dashed=1;" edge="1" parent="1" source="fast_system" target="storage_service">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="390" y="480" as="sourcePoint" />
            <mxPoint x="790" y="330" as="targetPoint" />
            <Array as="points">
              <mxPoint x="390" y="350" />
              <mxPoint x="790" y="350" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="media_flow2" value="获取媒体文件" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1.5;strokeColor=#2196f3;dashed=1;" edge="1" parent="1" source="slow_system" target="storage_service">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="760" y="480" as="sourcePoint" />
            <mxPoint x="790" y="330" as="targetPoint" />
            <Array as="points">
              <mxPoint x="760" y="350" />
              <mxPoint x="790" y="350" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 标题 -->
        <mxCell id="title" value="车端多模态车云协同流程架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#1976d2;" vertex="1" parent="1">
          <mxGeometry x="400" y="10" width="350" height="30" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend" value="图例说明" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=20;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="50" y="740" width="1050" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="legend_content" value="━━━ 主流程连接    ┅┅┅ 异步响应    ┅┅┅ 媒体文件获取    🚗 车端处理    🛡️ 认证服务    💾 存储服务    🎯 路由决策    ⚡ 分支选择    🏃 快系统    🧠 慢系统    📋 结果处理    📱 车端呈现" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="legend">
          <mxGeometry x="20" y="25" width="1010" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 