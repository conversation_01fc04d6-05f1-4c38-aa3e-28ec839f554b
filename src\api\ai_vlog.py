from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Path, Request, Depends, Query
from fastapi.responses import JSONResponse
from typing import List, Optional
import httpx
import os
import tempfile
import traceback
from src.database.minio_client import MinioClient
from src.database.mysql_client import get_db
from src.models.mysql_crud import ai_vlog_crud
from sqlalchemy.orm import Session
import logging
import json
import aiofiles
import uuid
import shutil
from datetime import datetime, timedelta
from src.tools.comfyui.image_to_image import image_to_image
from src.tools.comfyui.image_to_text import image_to_text
from src.core.config import settings

router = APIRouter(prefix="/api/ai_vlog", tags=["旅行规划"])
logger = logging.getLogger("ai_vlog")
minio_client = MinioClient()

MONEY_PRINTER_API = settings.money_printer_api
MULTIMODAL_API_URL = settings.multimodal_api_url

async def save_files(files: List[UploadFile], file_type: str) -> List[str]:
    """Save multiple files and return their URLs"""
    if not files:
        return []
    urls = []
    for file in files:
        # 过滤掉空文件或无效文件
        if not file.filename or file.size == 0:
            print(f'Skipping empty file: {file.filename}, size: {file.size}')
            continue
        url = await minio_client.save_file_to_minio(file, file_type)
        urls.append(url)
    return urls


@router.post("/generate")
async def generate_ai_vlog(
    topic: str = Form(..., description="视频主题"),
    script: str = Form(..., description="视频文案"),
    magic_camera: bool = Form(False, description="是否使用魔法相机（图生图）"),
    images: Optional[List[UploadFile]] = File(None, description="图片文件列表"),
    audios: Optional[List[UploadFile]] = File(None, description="音频文件列表"),
    videos: Optional[List[UploadFile]] = File(None, description="视频文件列表"),
    user_id: int = Form(1, description="用户ID，默认为1")
):
    try:
        # Save all media files to MinIO and get their URLs
        if magic_camera and images:\
            # 魔法相机
            image_urls = await image_to_image(images)
        else:
            image_urls = await save_files(images, "image")
        audio_urls = await save_files(audios, "audio")
        video_urls = await save_files(videos, "video")
        # if images:
        #     scripts = await image_to_text(images)
        #     script = "用户输入文案\n：" + script + "\n图片识别内容：\n" + scripts
        if image_urls:
            scripts = await image_to_text_llm(image_urls)
            script = "用户输入文案\n：" + script + "\n图片识别内容：\n" + scripts
        data = {
            "video_subject": topic,
            "video_script": script,
            # "video_terms": "",  # 可扩展为前端输入
            "video_aspect": "16:9",
            "video_concat_mode": "random",
            "video_transition_mode": "None",
            "video_clip_duration": 2,
            "video_count": 1,
            "video_source": "local",
            "video_materials": [
                {
                    "provider": "minio",#"minio","local",
                    "url": url,
                    # "url": "./storage/local_videos/1750fb81-e22c-4a0e-be67-c098d4236573_微信图片_20250625101558.jpg",
                    # "url": "http://************:8202/view?filename=d3ae7b38_00001_.png&subfolder=&type=output",
                    "duration": 0
                } for url in (image_urls + video_urls)
            ],
            "video_language": "",
            "voice_name": "zh-CN-liaoning-XiaobeiNeural-Female",
            "voice_volume": 1.0,
            "voice_rate": 1.0,
            "bgm_type": "minio" if audio_urls else "random",
            "bgm_file": audio_urls[0] if audio_urls else "",
            "bgm_volume": 0.2,
            "subtitle_enabled": True,
            "subtitle_position": "bottom",
            "custom_position": 70.0,
            "font_name": "MicrosoftYaHeiBold.ttc",
            "text_fore_color": "#FFFFFF",
            "text_background_color": True,
            "font_size": 60,
            "stroke_color": "#000000",
            "stroke_width": 1.5,
            "n_threads": 2,
            "paragraph_number": 1
        }
        print("MONEY_PRINTER_API ", MONEY_PRINTER_API)
        print("post data ", json.dumps(data, ensure_ascii=False, indent=2))
        async with httpx.AsyncClient(timeout=600) as client:
            response = await client.post(MONEY_PRINTER_API, json=data)
            if response.status_code != 200:
                logger.error(f"视频生成失败: {response.text}")
                raise HTTPException(status_code=500, detail=f"视频生成失败: {response.text}")
            
            try:
                result = response.json()
                task_id = result.get("task_id") or result.get("data", {}).get("task_id")
                if task_id:
                    # 创建数据库记录
                    vlog_config = {
                        "topic": topic,
                        "script": script,
                        "magic_camera": magic_camera,
                        "image_urls": image_urls,
                        "audio_urls": audio_urls,
                        "video_urls": video_urls,
                        "external_api_data": data,
                        "task_id": task_id
                    }
                    
                    async with get_db() as db:
                        vlog_record = await ai_vlog_crud.create_vlog_task(
                            db,
                            user_id=user_id,
                            title=topic,
                            config=vlog_config,
                            external_task_id=task_id
                        )
                    
                    return JSONResponse({
                        "code": 200, 
                        "task_id": task_id
                    })
                else:
                    logger.error(f"创建任务失败: {result}")
                    return JSONResponse({"code": 500, "detail": "创建任务失败", "raw": result}, status_code=500)
            except Exception as e:
                logger.error(f"解析视频生成API返回失败: {e}\n{traceback.format_exc()}")
                return JSONResponse({"code": 500, "detail": "视频生成API返回非JSON格式", "raw": response.text}, status_code=500)

    except Exception as e:
        logger.error(f"AI Vlog生成接口异常: {e}\n{traceback.format_exc()}")
        return JSONResponse({"code": 500, "detail": str(e), "trace": traceback.format_exc()}, status_code=500)

@router.get("/task_status/{task_id}", summary="查询AI视频任务状态")
async def get_ai_vlog_task_status(
    task_id: str = Path(..., description="任务ID")
):
    """查询MoneyPrinterTurbo生成视频任务的状态和结果"""
    try:
        async with get_db() as db:
            vlog_record = await ai_vlog_crud.get_by_external_task_id(db, external_task_id=task_id)
        # 如果本地已完成，直接返回本地/minio信息
        if vlog_record and vlog_record.status_id in (7, 8):
            minio_signed_url = None
            if vlog_record.final_video_url and vlog_record.final_video_url.startswith("minio/"):
                minio_object = vlog_record.final_video_url[len("minio/"):]
                minio_signed_url = minio_client.client.presigned_get_object(minio_client.bucket_name, minio_object, expires=timedelta(days=7),
                response_headers={"response-content-disposition": "inline"})
            return JSONResponse({
                "code": 200,
                "data": {
                    "task_id": task_id,
                    "progress": 100,
                    "status_id": vlog_record.status_id,
                    "final_video_url": minio_signed_url
                }
            })
        # 否则继续远程get
        url = f"{MONEY_PRINTER_API.rsplit('/videos', 1)[0]}/tasks/{task_id}"
        async with httpx.AsyncClient(timeout=30) as client:
            resp = await client.get(url)
        if resp.status_code != 200:
            return JSONResponse({"code": resp.status_code, "detail": "查询失败", "data": resp.text}, status_code=500)
        result = resp.json()
        external_data = result.get("data", {})
        # 如果找到了数据库记录，更新状态
        if vlog_record:
            external_status = external_data.get("state", "")
            status_mapping = {
                4: 6,   # PROCESSING
                1: 7,    # COMPLETED
                -1: 8        # FAILED
            }
            new_status_id = status_mapping.get(external_status, vlog_record.status_id)
            final_video_url = None
            if isinstance(external_data.get("videos"), list) and external_data["videos"]:
                final_video_url = external_data["videos"][0]
            # 下载并上传到minio，仅在数据库未保存minio路径时执行
            minio_path = vlog_record.final_video_url if vlog_record.final_video_url and vlog_record.final_video_url.startswith("minio/") else None
            if final_video_url and new_status_id == 7 and not minio_path:
                # 下载视频到本地
                video_resp = await httpx.AsyncClient().get(final_video_url)
                if video_resp.status_code == 200:
                    tmp_dir = os.path.join(os.path.dirname(__file__), 'tmp')
                    os.makedirs(tmp_dir, exist_ok=True)
                    local_path = os.path.join(tmp_dir, f"{task_id}.mp4")
                    with open(local_path, "wb") as f:
                        f.write(video_resp.content)
                    # 上传到minio
                    minio_object = f"ai_vlog_videos/{task_id}.mp4"
                    minio_url = await minio_client.upload_file(local_path, minio_object)
                    # 保存minio路径到数据库
                    async with get_db() as db:
                        await ai_vlog_crud.update_vlog_status(
                            db=db,
                            vlog_id=vlog_record.id,
                            status_id=new_status_id,
                            final_video_url=f"minio/{minio_object}"
                        )
                    # 用完删除本地文件
                    try:
                        os.remove(local_path)
                    except Exception:
                        pass
            elif new_status_id != vlog_record.status_id:
                async with get_db() as db:
                    await ai_vlog_crud.update_vlog_status(
                        db=db,
                        vlog_id=vlog_record.id,
                        status_id=new_status_id,
                        final_video_url=vlog_record.final_video_url
                    )
        # 查询时如final_video_url为minio路径，生成签名URL
        minio_signed_url = None
        if vlog_record and vlog_record.final_video_url and vlog_record.final_video_url.startswith("minio/"):
            minio_object = vlog_record.final_video_url[len("minio/"):]
            minio_signed_url = minio_client.client.presigned_get_object(minio_client.bucket_name, minio_object, expires=timedelta(days=7),
                response_headers={"response-content-disposition": "inline"})
        # 返回数据
        return JSONResponse({
            "code": 200,
            "data": {
                "task_id": task_id,
                "progress": external_data.get("progress"),
                "status_id": new_status_id,
                "final_video_url": minio_signed_url
            }
        })
    except Exception as e:
        logger.error(f"查询AI视频任务状态异常: {e}\n{traceback.format_exc()}")
        return JSONResponse({"code": 500, "detail": str(e), "raw": traceback.format_exc()}, status_code=500)

@router.get("/user_vlogs", summary="查询用户Vlog列表")
async def get_user_vlogs(
    user_id: int = Query(1, description="用户ID，默认为1"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """查询当前用户的Vlog列表"""
    try:
        skip = (page - 1) * page_size
        
        # 获取用户的Vlog列表
        async with get_db() as db:
            vlogs = await ai_vlog_crud.get_user_vlogs(
                db=db,
                user_id=user_id,
                skip=skip,
                limit=page_size
            )
            total_count = await ai_vlog_crud.count_user_vlogs(db=db, user_id=user_id)
            
            # 格式化返回数据
            vlog_list = []
            for vlog in vlogs:
                vlog_data = {
                    "id": vlog.id,
                    "external_task_id": vlog.external_task_id,
                    "title": vlog.title,
                    "status_id": vlog.status_id,
                    "final_video_url": vlog.final_video_url,
                    "created_at": vlog.created_at.isoformat(),
                    "completed_at": vlog.completed_at.isoformat() if vlog.completed_at else None,
                    "config": vlog.config
                }
                # 新增：如final_video_url为minio路径，生成签名URL
                if vlog.final_video_url and vlog.final_video_url.startswith("minio/"):
                    minio_object = vlog.final_video_url[len("minio/"):]
                    vlog_data["final_video_url"] = minio_client.client.presigned_get_object(
                        minio_client.bucket_name, minio_object, expires=timedelta(days=7),
                            response_headers={"response-content-disposition": "inline"}
                    )
                # 新增：如果状态不是完成，调用get_ai_vlog_task_status更新
                if vlog.status_id != 7 and vlog.external_task_id:
                    status_resp = await get_ai_vlog_task_status(vlog.external_task_id)
                    # status_resp 是 JSONResponse
                    if hasattr(status_resp, "body"):
                        import json
                        status_data = json.loads(status_resp.body.decode())
                        if status_data.get("code") == 200 and "data" in status_data:
                            vlog_data.update(status_data["data"])
                vlog_list.append(vlog_data)
        
        return JSONResponse({
            "code": 200,
            "data": {
                "vlogs": vlog_list,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total_count,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
            }
        })
    except Exception as e:
        logger.error(f"查询用户Vlog列表异常: {e}\n{traceback.format_exc()}")
        return JSONResponse({"code": 500, "detail": str(e), "raw": traceback.format_exc()}, status_code=500)

async def analyze_image(image_url: str):
    """分析旅游或日常照片，输出景点、天气、氛围、人物等要素，生成适合社交平台分享的简短文案。"""
    try:
        # 构造系统提示词，适配旅游/日常照片社交文案生成
        system_prompt = (
            "你是一个图片文案生成助手。\n"
            "用户会上传旅游或日常生活拍摄的照片。\n"
            "你的任务是：分析图片内容，识别其中的景点、天气、氛围、人物等要素，\n"
            "并用简短精炼的一句话生成适合分享到社交平台的中文文案。\n"
            "要求：\n"
            "1. 只输出一句话，20字以内，简洁有吸引力。\n"
            "2. 不要出现'这是一张...'等描述性开头，直接给出文案。\n"
            "3. 可以适当加入情感色彩或氛围词汇。\n"
            "4. 不要出现与图片无关的内容。\n"
            "5. 如无法识别图片内容，输出'美好瞬间，值得分享'。\n"
            "示例：\n"
            "- 碧海蓝天，假日好心情\n"
            "- 雨中漫步，感受城市温柔\n"
            "- 与好友共赏落日余晖\n"
            "- 笑容定格在旅途的每一刻\n"
            "- 樱花树下，春日浪漫\n"
        )
        payload = {
            "max_tokens": 100,
            "messages": [
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请为这张照片生成一句适合社交平台分享的文案。"},
                        {"type": "image_url", "image_url": {"url": image_url}}
                    ]
                }
            ]
        }
        async with httpx.AsyncClient(timeout=60) as client:
            response = await client.post(MULTIMODAL_API_URL, json=payload)
            if response.status_code != 200:
                logger.error(f"多模态API请求失败: {response.text}")
                raise HTTPException(status_code=500, detail=f"多模态API请求失败: {response.text}")
            data = response.json()
            answer = data.get("choices", [{}])[0].get("message", {}).get("content", "")
            logger.info(f"多模态API请求返回: {answer}")
        return answer
    except Exception as e:
        logger.error(f"图片分析异常: {e}\n{traceback.format_exc()}")
        return ""

async def image_to_text_llm(image_urls: List[str]) -> List[str]:
    if not image_urls:
        return []
    script_from_images = []
    for image_url in image_urls:
        script_from_image = await analyze_image(image_url)
        script_from_images.append(script_from_image)
    return "\n".join(script_from_images)

@router.post("/delete/{vlog_id}", summary="删除指定Vlog（逻辑删除，状态设为9）")
async def delete_vlog(
    vlog_id: int = Path(..., description="Vlog主键ID")
):
    """逻辑删除指定Vlog，将其状态设为9"""
    try:
        async with get_db() as db:
            vlog = await ai_vlog_crud.get(db, vlog_id)
            if not vlog:
                return JSONResponse({"code": 404, "detail": f"Vlog {vlog_id} 不存在"}, status_code=404)
            await ai_vlog_crud.update_vlog_status(db=db, vlog_id=vlog_id, status_id=9)
        return JSONResponse({"code": 200, "msg": f"Vlog {vlog_id} 已删除（状态=9）"})
    except Exception as e:
        logger.error(f"删除Vlog异常: {e}\n{traceback.format_exc()}")
        return JSONResponse({"code": 500, "detail": str(e), "trace": traceback.format_exc()}, status_code=500)

@router.post("/regenerate/{vlog_id}", summary="重新生成指定Vlog（重新请求视频生成API）")
async def regenerate_vlog(
    vlog_id: int = Path(..., description="Vlog主键ID")
):
    """重新生成指定Vlog，重新请求视频生成API，并更新external_task_id和任务状态"""
    try:
        async with get_db() as db:
            vlog = await ai_vlog_crud.get(db, vlog_id)
            if not vlog:
                return JSONResponse({"code": 404, "detail": f"Vlog {vlog_id} 不存在"}, status_code=404)
            # 读取config字段中的external_api_data
            config = vlog.config or {}
            external_api_data = config.get("external_api_data")
            if not external_api_data:
                return JSONResponse({"code": 400, "detail": "未找到external_api_data，无法重新生成"}, status_code=400)
            # 请求MONEY_PRINTER_API
            async with httpx.AsyncClient(timeout=600) as client:
                response = await client.post(MONEY_PRINTER_API, json=external_api_data)
                if response.status_code != 200:
                    logger.error(f"重新生成视频失败: {response.text}")
                    return JSONResponse({"code": 500, "detail": f"重新生成视频失败: {response.text}"}, status_code=500)
                result = response.json()
                task_id = result.get("task_id") or result.get("data", {}).get("task_id")
                if not task_id:
                    logger.error(f"重新生成任务失败: {result}")
                    return JSONResponse({"code": 500, "detail": "重新生成任务失败", "raw": result}, status_code=500)
                # 更新external_task_id和状态为5（PENDING）
                vlog.external_task_id = task_id
                vlog.status_id = 5
                vlog.completed_at = None
                # config中也更新external_api_data（可选）
                config["task_id"] = task_id
                vlog.config = config
                db.add(vlog)
                await db.commit()
                await db.refresh(vlog)
            return JSONResponse({"code": 200, "msg": f"Vlog {vlog_id} 已重新生成", "task_id": task_id})
    except Exception as e:
        logger.error(f"重新生成Vlog异常: {e}\n{traceback.format_exc()}")
        return JSONResponse({"code": 500, "detail": str(e), "trace": traceback.format_exc()}, status_code=500)