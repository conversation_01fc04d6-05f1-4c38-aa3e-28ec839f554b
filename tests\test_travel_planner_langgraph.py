"""
TravelPlannerAgent LangGraph集成测试

测试重构后的LangGraph实现是否与前端接口100%兼容。
"""

import pytest
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List

from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
from src.agents.travel_planner_langgraph import create_initial_state, UserProfile, VehicleInfo


class TestTravelPlannerLangGraph:
    """TravelPlannerAgent LangGraph测试类"""
    
    @pytest.fixture
    async def agent(self):
        """创建Agent实例"""
        return TravelPlannerAgentLangGraph()
    
    @pytest.fixture
    def sample_user_profile(self):
        """示例用户画像"""
        return {
            "user_id": "test_user_001",
            "age_group": "30-40",
            "travel_style": "深度游",
            "budget_level": "中等",
            "interests": ["文化历史", "美食", "自然风光"],
            "dietary_restrictions": [],
            "accessibility_needs": [],
            "preferred_languages": ["中文"]
        }
    
    @pytest.fixture
    def sample_vehicle_info(self):
        """示例车辆信息"""
        return {
            "vehicle_type": "electric",
            "brand": "特斯拉",
            "model": "Model 3",
            "nominal_range_km": 500,
            "charge_type": "fast",
            "vehicle_size": "mid"
        }
    
    @pytest.mark.asyncio
    async def test_basic_travel_planning(self, agent, sample_user_profile):
        """测试基本旅行规划功能"""
        query = "我想去北京玩3天，主要想看历史文化景点"
        user_id = "test_user_001"
        
        try:
            result = await agent.plan_travel(
                user_id=user_id,
                query=query,
                user_profile=sample_user_profile
            )
            
            # 验证响应结构
            assert "session_id" in result
            assert "user_id" in result
            assert result["user_id"] == user_id
            assert "status" in result
            assert "core_intent" in result or result["status"] == "failed"
            
            # 如果成功，验证核心意图
            if result["status"] == "completed":
                core_intent = result["core_intent"]
                assert "destinations" in core_intent
                assert "北京" in core_intent["destinations"]
                assert core_intent["days"] == 3
                
                # 验证行程结构
                if "daily_itineraries" in result:
                    daily_itineraries = result["daily_itineraries"]
                    assert len(daily_itineraries) == 3
                    
                    for day_itinerary in daily_itineraries:
                        assert "day" in day_itinerary
                        assert "items" in day_itinerary
                        assert isinstance(day_itinerary["items"], list)
            
            print(f"基本规划测试完成 - 状态: {result['status']}")
            
        except Exception as e:
            pytest.fail(f"基本旅行规划测试失败: {str(e)}")
    
    @pytest.mark.asyncio
    async def test_multi_city_planning(self, agent, sample_user_profile):
        """测试多城市规划功能"""
        query = "我想去北京和上海玩5天，自驾出行"
        user_id = "test_user_002"
        
        try:
            result = await agent.plan_travel(
                user_id=user_id,
                query=query,
                user_profile=sample_user_profile
            )
            
            # 验证多城市策略
            if result["status"] == "completed" and "multi_city_strategy" in result:
                strategy = result["multi_city_strategy"]
                assert "strategy_type" in strategy
                assert "recommended_order" in strategy
                assert len(strategy["recommended_order"]) == 2
                assert "北京" in strategy["recommended_order"]
                assert "上海" in strategy["recommended_order"]
            
            print(f"多城市规划测试完成 - 状态: {result['status']}")
            
        except Exception as e:
            pytest.fail(f"多城市规划测试失败: {str(e)}")
    
    @pytest.mark.asyncio
    async def test_driving_context_planning(self, agent, sample_user_profile, sample_vehicle_info):
        """测试驾驶情境规划功能"""
        query = "我想开电动车去杭州玩2天"
        user_id = "test_user_003"
        
        try:
            result = await agent.plan_travel(
                user_id=user_id,
                query=query,
                user_profile=sample_user_profile,
                vehicle_info=sample_vehicle_info
            )
            
            # 验证驾驶情境分析
            if result["status"] == "completed" and "driving_context" in result:
                context = result["driving_context"]
                assert "driving_strategy" in context
                assert context["driving_strategy"] in ["range_aware", "general_assistance"]
                
                # 如果是精准续航规划模式
                if context["driving_strategy"] == "range_aware":
                    assert "range_planning" in context
                    assert "charging_strategy" in context
            
            print(f"驾驶情境规划测试完成 - 状态: {result['status']}")
            
        except Exception as e:
            pytest.fail(f"驾驶情境规划测试失败: {str(e)}")
    
    @pytest.mark.asyncio
    async def test_stream_planning(self, agent, sample_user_profile):
        """测试流式规划功能"""
        query = "我想去成都玩3天，主要想吃美食"
        user_id = "test_user_004"
        
        try:
            events = []
            session_id = None
            
            async for sse_event in agent.plan_travel_stream(
                user_id=user_id,
                query=query,
                user_profile=sample_user_profile
            ):
                events.append(sse_event)
                
                # 提取session_id
                if "session_id" in sse_event and not session_id:
                    try:
                        # 简单解析获取session_id
                        if "data:" in sse_event:
                            data_line = [line for line in sse_event.split('\n') if line.startswith('data:')][0]
                            data_json = data_line[5:].strip()  # 移除"data:"前缀
                            data = json.loads(data_json)
                            session_id = data.get("session_id")
                    except:
                        pass
            
            # 验证事件流
            assert len(events) > 0
            
            # 验证事件格式
            for event in events:
                assert "id:" in event
                assert "event:" in event
                assert "data:" in event
                assert event.endswith("\n\n")
            
            # 验证包含必要的事件类型
            event_types = []
            for event in events:
                lines = event.split('\n')
                for line in lines:
                    if line.startswith('event:'):
                        event_types.append(line[6:].strip())
            
            assert "stream_start" in event_types
            assert "stream_end" in event_types or "error" in event_types
            
            print(f"流式规划测试完成 - 事件数量: {len(events)}")
            
        except Exception as e:
            pytest.fail(f"流式规划测试失败: {str(e)}")
    
    @pytest.mark.asyncio
    async def test_planning_status(self, agent, sample_user_profile):
        """测试规划状态查询功能"""
        query = "我想去西安玩2天"
        user_id = "test_user_005"
        
        try:
            # 先启动一个规划
            result = await agent.plan_travel(
                user_id=user_id,
                query=query,
                user_profile=sample_user_profile
            )
            
            session_id = result.get("session_id")
            if session_id:
                # 查询状态
                status = await agent.get_planning_status(session_id)
                
                assert "session_id" in status
                assert status["session_id"] == session_id
                assert "status" in status
                assert status["status"] in ["completed", "processing", "error", "not_found"]
                
                print(f"状态查询测试完成 - 状态: {status['status']}")
            
        except Exception as e:
            pytest.fail(f"规划状态测试失败: {str(e)}")
    
    def test_graph_visualization(self, agent):
        """测试图形可视化功能"""
        try:
            mermaid_graph = agent.get_graph_visualization()
            
            assert isinstance(mermaid_graph, str)
            assert "graph TD" in mermaid_graph
            assert "core_intent_analyzer" in mermaid_graph
            assert "END" in mermaid_graph
            
            print("图形可视化测试完成")
            
        except Exception as e:
            pytest.fail(f"图形可视化测试失败: {str(e)}")
    
    def test_state_creation(self):
        """测试状态创建功能"""
        try:
            user_profile = UserProfile(
                user_id="test_user",
                age_group="25-35",
                travel_style="休闲游",
                interests=["美食", "购物"]
            )
            
            vehicle_info = VehicleInfo(
                vehicle_type="electric",
                nominal_range_km=400
            )
            
            state = create_initial_state(
                session_id="test_session",
                user_id="test_user",
                original_query="测试查询",
                user_profile=user_profile,
                vehicle_info=vehicle_info
            )
            
            assert state["session_id"] == "test_session"
            assert state["user_id"] == "test_user"
            assert state["original_query"] == "测试查询"
            assert state["user_profile"] is not None
            assert state["vehicle_info"] is not None
            assert state["is_completed"] is False
            assert state["has_error"] is False
            
            print("状态创建测试完成")
            
        except Exception as e:
            pytest.fail(f"状态创建测试失败: {str(e)}")


# 运行测试的辅助函数
async def run_integration_tests():
    """运行集成测试"""
    print("开始运行TravelPlannerAgent LangGraph集成测试...")
    
    agent = TravelPlannerAgentLangGraph()
    
    # 基本功能测试
    print("\n=== 基本功能测试 ===")
    try:
        result = await agent.plan_travel(
            user_id="integration_test_user",
            query="我想去北京玩3天，看看故宫和长城",
            user_profile={
                "user_id": "integration_test_user",
                "age_group": "30-40",
                "travel_style": "文化游",
                "interests": ["历史文化"]
            }
        )
        print(f"✓ 基本规划功能正常 - 状态: {result.get('status')}")
    except Exception as e:
        print(f"✗ 基本规划功能异常: {str(e)}")
    
    # 流式功能测试
    print("\n=== 流式功能测试 ===")
    try:
        event_count = 0
        async for event in agent.plan_travel_stream(
            user_id="integration_test_user_stream",
            query="我想去上海玩2天",
            user_profile={"user_id": "integration_test_user_stream"}
        ):
            event_count += 1
            if event_count > 20:  # 限制测试事件数量
                break
        print(f"✓ 流式规划功能正常 - 接收到 {event_count} 个事件")
    except Exception as e:
        print(f"✗ 流式规划功能异常: {str(e)}")
    
    print("\n集成测试完成！")


if __name__ == "__main__":
    # 直接运行集成测试
    asyncio.run(run_integration_tests())
