import logging
from fastapi import UploadFile, HTTPException
from typing import List
from src.tools.comfyui.comfyui import load_workflow, upload_image, send_prompt, wait_for_text
import json
import traceback

logger = logging.getLogger("image_to_text")
server_name = ""

def build_image_to_text_workflow(image_path: str):
    """构建图片识别文本工作流"""
    try:
        workflow = load_workflow("image_to_text.json")
        if "3" not in workflow:
            logger.error("工作流中找不到节点3")
            raise HTTPException(status_code=500, detail="Node 3 not found in workflow")
        workflow["3"]["inputs"]["image"] = image_path
        logger.info(f"设置图片: {image_path}")
        if "4" not in workflow:
            logger.error("工作流中找不到节点4")
            raise HTTPException(status_code=500, detail="Node 4 not found in workflow")
        return workflow, "4"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

async def image_to_text_comfyui(file: UploadFile):
    try:
        logger.info(f"\n========== 开始图片识别输出文本 ==========")
        logger.info(f"文件名: {file.filename}")
        uploaded_filename = await upload_image(server_name, file.file, "待识别图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")
        workflow, output_node_id = build_image_to_text_workflow(uploaded_filename)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")
        text = await wait_for_text(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"识别出的文本: {text}")
        return text
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用image_to_text接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

async def image_to_text(files: List[UploadFile]) -> str:
    if not files:
        return ""
    script_from_images = []
    for idx, file in enumerate(files):
        if not file.filename or (hasattr(file, 'size') and file.size == 0):
            continue
        script_from_image = await image_to_text_comfyui(file)
        script_from_images.append(script_from_image)
    return "\n".join(script_from_images) 