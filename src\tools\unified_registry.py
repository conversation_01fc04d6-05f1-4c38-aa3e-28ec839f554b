"""
统一工具注册表 (V3.0 - 统一架构版)

实现中央工具注册表，负责管理所有Action Tools和Planner Tools，
支持动态注册、Schema生成和统一执行。
"""

import inspect
import json
import time
import logging
from typing import Dict, Any, Callable, List, Optional, Union
from functools import wraps

logger = logging.getLogger(__name__)


class UnifiedToolRegistry:
    """统一工具注册表
    
    核心功能：
    1. Action Tools: 负责与外部世界交互的具体动作
    2. Planner Tools: 负责辅助Agent思考和推理的内部工具
    3. 自动Schema生成: 支持OpenAI Function Calling格式
    4. 事件集成: 与UnifiedEventBus集成，自动发布工具执行事件
    """
    
    def __init__(self):
        # Action Tools存储
        self._action_tools: Dict[str, Callable] = {}
        self._action_schemas: Dict[str, Dict] = {}
        
        # Planner Tools存储
        self._planner_tools: Dict[str, Callable] = {}
        
        # 事件总线实例（延迟注入）
        self._event_bus: Optional[Any] = None
        
        logger.info("UnifiedToolRegistry initialized")
    
    def set_event_bus(self, event_bus):
        """设置事件总线实例"""
        self._event_bus = event_bus
        logger.info("Event bus connected to UnifiedToolRegistry")
    
    def register_action_tool(self, func: Callable) -> Callable:
        """注册Action Tool装饰器
        
        Args:
            func: 要注册的函数
            
        Returns:
            原函数（支持链式调用）
        """
        tool_name = func.__name__
        schema = self._generate_openai_schema(func)
        
        self._action_tools[tool_name] = func
        self._action_schemas[tool_name] = schema
        
        logger.info(f"Action tool registered: {tool_name}")
        return func
    
    def register_planner_tool(self, func: Callable) -> Callable:
        """注册Planner Tool装饰器
        
        Args:
            func: 要注册的函数
            
        Returns:
            原函数（支持链式调用）
        """
        tool_name = func.__name__
        self._planner_tools[tool_name] = func
        
        logger.info(f"Planner tool registered: {tool_name}")
        return func
    
    def _generate_openai_schema(self, func: Callable) -> Dict[str, Any]:
        """生成OpenAI Function Calling格式的Schema
        
        Args:
            func: 要分析的函数
            
        Returns:
            OpenAI Function Calling格式的Schema
        """
        sig = inspect.signature(func)
        properties = {}
        required = []
        
        for param_name, param in sig.parameters.items():
            # 跳过特殊参数
            if param_name in ['self', 'cls', 'task_id']:
                continue
                
            param_info = {
                "type": self._get_type_string(param.annotation),
                "description": f"Parameter {param_name}"
            }
            
            # 从docstring中提取参数描述
            if func.__doc__:
                param_desc = self._extract_param_description(func.__doc__, param_name)
                if param_desc:
                    param_info["description"] = param_desc
            
            if param.default == inspect.Parameter.empty:
                required.append(param_name)
            
            properties[param_name] = param_info
        
        return {
            "type": "function",
            "function": {
                "name": func.__name__,
                "description": func.__doc__ or f"Execute {func.__name__}",
                "parameters": {
                    "type": "object",
                    "properties": properties,
                    "required": required
                }
            }
        }
    
    def _get_type_string(self, annotation) -> str:
        """将Python类型转换为JSON Schema类型"""
        if annotation == str or annotation == "str":
            return "string"
        elif annotation == int or annotation == "int":
            return "integer"
        elif annotation == float or annotation == "float":
            return "number"
        elif annotation == bool or annotation == "bool":
            return "boolean"
        elif annotation == list or annotation == "list":
            return "array"
        elif annotation == dict or annotation == "dict":
            return "object"
        else:
            # 处理复杂类型，如Optional[str], List[Dict]等
            return "string"  # 默认为string
    
    def _extract_param_description(self, docstring: str, param_name: str) -> Optional[str]:
        """从docstring中提取参数描述"""
        try:
            lines = docstring.split('\n')
            in_args_section = False
            
            for line in lines:
                line = line.strip()
                if line.startswith('Args:'):
                    in_args_section = True
                    continue
                elif line.startswith('Returns:') or line.startswith('Yields:'):
                    in_args_section = False
                    continue
                
                if in_args_section and line.startswith(f'{param_name}:'):
                    return line.split(':', 1)[1].strip()
            
            return None
        except Exception:
            return None
    
    def get_all_action_schemas(self) -> List[Dict[str, Any]]:
        """获取所有Action Tools的Schema列表"""
        return list(self._action_schemas.values())
    
    def get_action_tool_names(self) -> List[str]:
        """获取所有Action Tool的名称列表"""
        return list(self._action_tools.keys())
    
    async def execute_action_tool(self, name: str, task_id: str = None, **kwargs) -> Any:
        """执行Action Tool并发布事件
        
        Args:
            name: 工具名称
            task_id: 任务ID（用于事件发布）
            **kwargs: 工具参数
            
        Returns:
            工具执行结果
        """
        if name not in self._action_tools:
            raise ValueError(f"Action tool '{name}' not found. Available tools: {list(self._action_tools.keys())}")
        
        tool_func = self._action_tools[name]
        
        # 发布工具开始事件
        if self._event_bus and task_id:
            await self._event_bus.notify_tool_execution(
                task_id, name, "tool_start", 
                parameters=kwargs,
                description=tool_func.__doc__ or f"Execute {name}"
            )
        
        try:
            start_time = time.time()
            
            # 执行工具
            if inspect.iscoroutinefunction(tool_func):
                result = await tool_func(**kwargs)
            else:
                result = tool_func(**kwargs)
            
            execution_time = time.time() - start_time
            
            # 发布工具结束事件
            if self._event_bus and task_id:
                await self._event_bus.notify_tool_execution(
                    task_id, name, "tool_end",
                    status="success",
                    result=result,
                    execution_time=execution_time
                )
            
            logger.info(f"Action tool '{name}' executed successfully in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            # 发布工具错误事件
            if self._event_bus and task_id:
                await self._event_bus.notify_tool_execution(
                    task_id, name, "tool_end",
                    status="error",
                    error_message=str(e)
                )
            
            logger.error(f"Action tool '{name}' execution failed: {str(e)}")
            raise
    
    def get_planner_tool(self, name: str) -> Optional[Callable]:
        """获取Planner Tool
        
        Args:
            name: 工具名称
            
        Returns:
            工具函数或None
        """
        return self._planner_tools.get(name)
    
    def get_all_planner_tools(self) -> Dict[str, Callable]:
        """获取所有Planner Tools"""
        return self._planner_tools.copy()

    def get_action_tool_names(self) -> List[str]:
        """获取所有Action Tool名称列表"""
        return list(self._action_tools.keys())

    def get_all_action_schemas(self) -> Dict[str, Dict]:
        """获取所有Action Tool的Schema"""
        return self._action_schemas.copy()

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具注册表的统计信息"""
        return {
            "action_tools_count": len(self._action_tools),
            "planner_tools_count": len(self._planner_tools),
            "action_tools": list(self._action_tools.keys()),
            "planner_tools": list(self._planner_tools.keys()),
            "event_bus_connected": self._event_bus is not None
        }


# 创建全局实例
unified_registry = UnifiedToolRegistry()

# 导出装饰器，方便使用
register_action_tool = unified_registry.register_action_tool
register_planner_tool = unified_registry.register_planner_tool
