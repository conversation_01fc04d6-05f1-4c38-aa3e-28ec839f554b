"""
用户数据CRUD操作 (MySQL)

提供对MySQL中 "users" 表的读取操作。
"""
from typing import Optional, Dict, Any
from .mysql_client import get_db_cursor
from src.core.logger import get_logger

logger = get_logger(__name__)

async def get_user_by_id_from_mysql(user_id: str) -> Optional[Dict[str, Any]]:
    """
    根据用户ID从MySQL数据库中查找用户。

    Args:
        user_id: 用户的唯一标识符 (对应于表中的'id'或'user_id'字段)。

    Returns:
        如果找到用户，则返回包含用户数据的字典；否则返回None。
    """
    # 注意：根据截图，用户表的主键是'id'，并且是int类型。
    # 我们需要确认传入的user_id是字符串还是数字。这里假设传入的是字符串，
    # 但数据库中可能是数字ID。我们将尝试进行转换。
    try:
        # 假设user_id可以转换为整数，对应数据库的id字段
        query_id = int(user_id)
    except ValueError:
        # 如果不能转换，可能需要按其他字段查询，或认为ID无效
        logger.warning(f"传入的user_id '{user_id}' 不是有效的整数ID。")
        # 暂时假设user_id也可能是一个字符串字段，例如 'user_001'
        # 在实际应用中，需要明确查询哪个字段。这里我们优先查'id'。
        # 如果你的用户ID不是数字，请修改这里的逻辑。
        # 这里我们先假设按数字id查
        query = "SELECT * FROM users WHERE id = %s"
        params = (user_id,)
        # 为了演示，我们优先使用可转换的数字ID
        return None # 如果不能转成数字，就直接返回None

    query = "SELECT * FROM users WHERE id = %s"
    params = (query_id,)
    
    try:
        async with get_db_cursor() as cursor:
            await cursor.execute(query, params)
            user_data = await cursor.fetchone()
            if user_data:
                logger.info(f"成功从MySQL获取用户数据: user_id={user_id}")
                return user_data
            else:
                logger.warning(f"在MySQL中未找到用户: user_id={user_id}")
                return None
    except Exception as e:
        logger.error(f"从MySQL查询用户失败: {e}", exc_info=True)
        return None 