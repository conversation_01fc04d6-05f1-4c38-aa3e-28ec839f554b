# AutoPilot AI 工具包 (Tools Package)

这个目录包含了 AutoPilot AI 系统中所有的原子能力工具，这些工具可以被 AutoGen 代理使用来完成各种功能。

## 📁 目录结构

```
tools/
├── __init__.py                 # 工具包初始化文件
├── README.md                   # 本文档
├── NetEaseCloudMusic/          # 网易云音乐 API 工具
│   ├── __init__.py
│   ├── music_tool.py           # 核心音乐工具类
│   ├── music_function_tools.py # AutoGen 功能工具定义
│   ├── music_models.py         # 数据模型定义
│   ├── music_api.json          # API 配置文件
│   └── sign_utils.py           # 签名工具
└── Amap/                       # 高德地图 API 工具
    ├── map_tool.py             # 地图工具类
    └── mcp_test.py             # 测试文件
```

## 🎵 网易云音乐工具 (NetEaseCloudMusic)

基于网易云音乐开放 API 实现的音乐相关功能工具集。

### 主要功能

1. **歌曲搜索**
   - `search_songs()` - 通过关键词搜索歌曲
   - `search_songs_by_artist()` - 通过艺术家名称搜索歌曲
   - `search_songs_by_artist_and_name()` - 通过艺术家和歌曲名精确搜索

2. **音乐资源搜索**
   - `search_playlists()` - 搜索歌单
   - `search_albums()` - 搜索专辑
   - `search_albums_by_artist()` - 通过艺术家搜索专辑
   - `search_artists()` - 搜索艺术家

3. **内容推荐与发现**
   - `get_hot_keywords()` - 获取热门搜索关键词
   - `get_suggest_keywords()` - 获取搜索建议关键词
   - `get_hi_res()` - 获取 Hi-Res 高品质音乐内容

4. **详细信息获取**
   - `get_song_detail()` - 获取歌曲详细信息，包括播放链接、音质等

### 数据模型

- `Song` - 歌曲基本信息
- `SongDetail` - 歌曲详细信息
- `Album` / `AlbumDetail` - 专辑信息
- `Artist` - 艺术家信息
- `Playlist` / `PlaylistDetail` - 歌单信息
- `Qualities` - 音质信息
- `HiResCategory` - 高品质音乐分类

### 使用示例

```python
from tools.NetEaseCloudMusic import MusicTool
from tools.NetEaseCloudMusic.music_function_tools import create_music_function_tools

# 直接使用工具类
music_tool = MusicTool()
songs = music_tool.search_songs("周杰伦")

# 或者作为 AutoGen 功能工具使用
music_tools = create_music_function_tools()
```

## 🗺️ 高德地图工具 (Amap)

基于高德地图 Web API 实现的地理位置和地图功能工具。

### 主要功能

1. **兴趣点搜索 (POI Search)**
   - `search_pois()` - 搜索餐厅、酒店、景点等兴趣点
   - 支持按距离、评分等排序
   - 支持价格范围筛选
   - 支持分页查询

2. **路径规划**
   - `get_route()` - 获取两点间路径规划
   - 支持多种交通方式：驾车、步行、公交、骑行
   - 支持途经点设置

### 数据模型

- `Location` - 位置信息（经纬度、名称、地址）
- `POIResult` - 兴趣点搜索结果（包含位置、类型、评分、价格等）

### 使用示例

```python
from tools.Amap.map_tool import MapTool, Location

# 初始化工具（需要高德地图 API Key）
map_tool = MapTool(api_key="your_api_key")

# 搜索附近的餐厅
location = Location(latitude=39.908823, longitude=116.397470)
restaurants = map_tool.search_pois(
    keywords="餐厅", 
    city="北京",
    location=location,
    radius=1000
)

# 获取路径规划
origin = Location(latitude=39.908823, longitude=116.397470)
destination = Location(latitude=39.918823, longitude=116.407470)
route = map_tool.get_route(origin, destination, transport_mode="driving")
```

## 🔧 技术特性

### 1. AutoGen 集成
所有工具都支持作为 AutoGen `FunctionTool` 使用，可以轻松集成到 AI 代理工作流中。

### 2. 错误处理
- 完善的 API 错误处理机制
- 详细的日志记录
- 异常情况的优雅降级

### 3. 数据模型
- 使用 `dataclass` 定义清晰的数据结构
- 类型提示支持，便于开发和调试
- 统一的接口设计

### 4. 安全性
- RSA 签名认证（网易云音乐 API）
- 环境变量配置敏感信息
- 参数验证和清理

## 🚀 快速开始

1. **安装依赖**
```bash
pip install requests cryptography python-dotenv autogen
```

2. **配置环境变量**
```bash
# 网易云音乐 API（如果使用）
export NETEASE_APP_ID="your_app_id"
export NETEASE_PRIVATE_KEY="your_private_key"

# 高德地图 API（如果使用）
export AMAP_API_KEY="your_api_key"
```

3. **导入使用**
```python
from tools.NetEaseCloudMusic.music_function_tools import create_music_function_tools
from tools.Amap.map_tool import MapTool

# 使用工具...
```

## 📝 开发指南

### 添加新工具

1. 在 `tools/` 目录下创建新的子目录
2. 实现工具类，遵循现有的接口设计模式
3. 定义相应的数据模型
4. 创建 AutoGen `FunctionTool` 包装器
5. 更新本 README 文档

### 最佳实践

- 保持工具的原子性和单一职责
- 提供清晰的错误消息和日志
- 使用类型提示提高代码可读性
- 编写完整的文档字符串
- 遵循统一的命名规范

## 🔗 相关链接

- [网易云音乐开放平台](https://developer.music.163.com/)
- [高德地图开放平台](https://lbs.amap.com/)
- [AutoGen 文档](https://microsoft.github.io/autogen/)
