# 开发环境设置指南

**版本**: 1.0

## 1. 核心依赖

*   **Python**: 3.12+
*   **数据库**:
    *   `Redis 7.0+`: 用于L1短期/会话记忆和缓存。
    *   `MySQL 8.0+`: 用于L3用户画像/语义记忆。
    *   `Weaviate` / `Milvus` / `ChromaDB` (待定): 用于L2长期/情景记忆的向量数据库。
*   **包管理**: `pip` with `venv` (或 `conda`)

## 2. 环境搭建步骤

### 2.1. 克隆项目
```bash
git clone <your-repository-url>
cd autopilotai
```

### 2.2. 创建并激活Python虚拟环境

**使用 venv (推荐):**
```bash
# For Linux/macOS
python3 -m venv .venv
source .venv/bin/activate

# For Windows
python -m venv .venv
.venv\Scripts\activate
```

**使用 conda:**
```bash
conda create -n autopilotai python=3.11
conda activate autopilotai
```

### 2.3. 安装Python依赖包

核心的 `autogen` 库及其与OpenAI集成的扩展是首要依赖。
```bash
# 核心依赖 (autogen, openai, fastapi, pydantic, etc.)
pip install "autogen-agentchat" "autogen-ext[openai]"
pip install fastapi uvicorn pydantic-settings python-dotenv structlog prometheus-client tenacity

# 数据库驱动
pip install redis mysql-connector-python SQLAlchemy

# 可以在项目根目录创建 requirements.txt 文件来管理这些依赖
```

### 2.4. 配置环境变量
由于您使用远程的Redis和MySQL服务，请确保在 `.env` 文件中正确配置它们的主机地址、端口和凭证。这是启动应用前的**关键一步**。

首先，复制模板 (如果尚未创建):
```bash
cp .env.example .env
```
然后，编辑 `.env` 文件，填入您远程服务的真实信息:
```dotenv
# .env

# OpenAI API
OPENAI_API_KEY="sk-..."

# Database Connections - 请替换为您的远程服务地址
REDIS_HOST="your-remote-redis-host"
REDIS_PORT=6379 # 如果不是默认端口，请修改
# REDIS_PASSWORD="your-redis-password" # 如果有密码，请取消注释并设置

MYSQL_HOST="your-remote-mysql-host"
MYSQL_PORT=3306 # 如果不是默认端口，请修改
MYSQL_USER="your-remote-mysql-user"
MYSQL_PASSWORD="your-remote-mysql-password"
MYSQL_DB="autopilot_ai"

# JWT Secret Key
JWT_SECRET_KEY="a_very_secret_and_long_random_string"
JWT_ALGORITHM="HS256"
```
**注意**: `.env` 文件包含敏感信息，已在 `.gitignore` 中配置，**严禁**提交到版本库。

## 3. 运行项目
```bash
# 启动 FastAPI 应用
uvicorn src.main:app --reload
```
现在，应用应该在 `http://127.0.0.1:8000` 上运行。

## 4. 开发工具

*   **代码风格**: 项目使用 `black` 进行格式化，`isort` 进行import排序。建议配置IDE在保存时自动执行。
*   **代码检查**: 使用 `flake8` 或 `ruff` 进行静态代码检查。
*   **pre-commit**: 建议配置 `pre-commit` hooks 在提交代码前自动运行检查和格式化。 