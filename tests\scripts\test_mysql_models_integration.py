#!/usr/bin/env python3
"""
MySQL模型和CRUD操作集成测试
测试新建的mysql_models.py和mysql_crud.py的功能
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from datetime import datetime, date
from typing import Dict, Any
import json

# 模拟SQLAlchemy Session（因为我们没有设置真正的数据库连接）
class MockSession:
    """模拟数据库Session"""
    def __init__(self):
        self.data = {}
        self.committed = False
    
    def query(self, model):
        return MockQuery(model, self.data)
    
    def add(self, obj):
        pass
    
    def commit(self):
        self.committed = True
    
    def refresh(self, obj):
        pass
    
    def delete(self, obj):
        pass

class MockQuery:
    """模拟查询对象"""
    def __init__(self, model, data):
        self.model = model
        self.data = data
    
    def filter(self, *args):
        return self
    
    def order_by(self, *args):
        return self
    
    def offset(self, n):
        return self
    
    def limit(self, n):
        return self
    
    def first(self):
        return None
    
    def all(self):
        return []

def test_mysql_models():
    """测试MySQL模型定义"""
    print("=" * 60)
    print("测试MySQL模型定义")
    print("=" * 60)
    
    try:
        from src.models.mysql_models import DhTripPlanner, DhUserProfile
        print("[成功] 成功导入MySQL模型")
        
        # 测试DhTripPlanner模型
        print("\n测试dh_tripplanner数据库模型:")
        
        # 测试AI规划会话模型
        session_data = {
            "id": "test-session-id",
            "user_id": 1,
            "status": DhTripPlanner.PlanningSessionStatus.PENDING,
            "user_input": {"destination": "北京", "days": 3},
            "created_at": datetime.now()
        }
        ai_session = DhTripPlanner.AIPlanningSession(**session_data)
        print(f"  [OK] AIPlanningSession: {ai_session.id}, 状态: {ai_session.status}")
        
        # 测试行程模型
        itinerary_data = {
            "id": 1,
            "user_id": 1,
            "title": "北京三日游",
            "city_name": "北京", 
            "total_days": 3,
            "start_date": date.today(),
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        itinerary = DhTripPlanner.Itinerary(**itinerary_data)
        print(f"  [OK] Itinerary: {itinerary.title}, 城市: {itinerary.city_name}")
        
        # 测试POI模型
        poi_data = {
            "id": 1,
            "name": "故宫博物院",
            "type_id": 1,
            "address": "北京市东城区景山前街4号",
            "latitude": 39.9163,
            "longitude": 116.3972,
            "rating": 4.8
        }
        poi = DhTripPlanner.POI(**poi_data)
        print(f"  [OK] POI: {poi.name}, 坐标: ({poi.latitude}, {poi.longitude})")
        
        # 测试DhUserProfile模型
        print("\n测试dh_user_profile数据库模型:")
        
        # 测试用户模型
        user_data = {
            "id": 1,
            "nickname": "张三",
            "status": DhUserProfile.UserStatus.ACTIVE,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        user = DhUserProfile.User(**user_data)
        print(f"  [OK] User: {user.nickname}, 状态: {user.status}")
        
        # 测试用户记忆模型
        memory_data = {
            "id": 1,
            "user_id": 1,
            "memory_content": "用户喜欢历史文化景点",
            "confidence": 0.9,
            "created_at": datetime.now()
        }
        memory = DhUserProfile.UserMemory(**memory_data)
        print(f"  [OK] UserMemory: {memory.memory_content[:20]}..., 置信度: {memory.confidence}")
        
        # 测试用户画像模型
        summary_data = {
            "user_id": 1,
            "summary": "热爱历史文化的旅行者",
            "keywords": ["历史", "文化", "博物馆"],
            "updated_at": datetime.now()
        }
        summary = DhUserProfile.UserSummary(**summary_data)
        print(f"  [OK] UserSummary: {summary.summary}, 关键词: {summary.keywords}")
        
        print("\n[总结] 所有MySQL模型测试通过 ✓")
        
    except Exception as e:
        print(f"[错误] MySQL模型测试失败: {e}")
        return False
    
    return True

def test_mysql_crud():
    """测试MySQL CRUD操作"""
    print("\n" + "=" * 60)
    print("测试MySQL CRUD操作")
    print("=" * 60)
    
    try:
        from src.models.mysql_crud import (
            ai_planning_session_crud, itinerary_crud, poi_crud,
            user_crud, user_memory_crud, user_summary_crud,
            CRUDAIPlanningSession, CRUDItinerary, CRUDPOI,
            CRUDUser, CRUDUserMemory, CRUDUserSummary
        )
        print("[成功] 成功导入MySQL CRUD操作")
        
        # 创建模拟Session
        mock_db = MockSession()
        
        # 测试AI规划会话CRUD
        print("\n测试AI规划会话CRUD:")
        sessions = ai_planning_session_crud.get_by_user(mock_db, user_id=1)
        print(f"  [OK] 获取用户会话: {len(sessions)} 条记录")
        
        pending_sessions = ai_planning_session_crud.get_by_status(mock_db, status="PENDING")
        print(f"  [OK] 获取待处理会话: {len(pending_sessions)} 条记录")
        
        # 测试行程CRUD
        print("\n测试行程CRUD:")
        user_itineraries = itinerary_crud.get_by_user(mock_db, user_id=1)
        print(f"  [OK] 获取用户行程: {len(user_itineraries)} 条记录")
        
        templates = itinerary_crud.get_templates(mock_db)
        print(f"  [OK] 获取模板行程: {len(templates)} 条记录")
        
        beijing_trips = itinerary_crud.search_by_city(mock_db, city_name="北京")
        print(f"  [OK] 搜索北京行程: {len(beijing_trips)} 条记录")
        
        # 测试POI CRUD
        print("\n测试POI CRUD:")
        search_results = poi_crud.search(mock_db, keyword="故宫", type_id=1)
        print(f"  [OK] 搜索故宫POI: {len(search_results)} 条记录")
        
        nearby_pois = poi_crud.get_nearby(mock_db, latitude=39.9163, longitude=116.3972)
        print(f"  [OK] 获取附近POI: {len(nearby_pois)} 条记录")
        
        # 测试用户CRUD
        print("\n测试用户CRUD:")
        active_users = user_crud.get_active_users(mock_db)
        print(f"  [OK] 获取活跃用户: {len(active_users)} 条记录")
        
        user_by_nickname = user_crud.get_by_nickname(mock_db, nickname="张三")
        print(f"  [OK] 根据昵称查找用户: {'找到' if user_by_nickname else '未找到'}")
        
        # 测试用户记忆CRUD
        print("\n测试用户记忆CRUD:")
        user_memories = user_memory_crud.get_by_user(mock_db, user_id=1)
        print(f"  [OK] 获取用户记忆: {len(user_memories)} 条记录")
        
        search_memories = user_memory_crud.search_memories(mock_db, user_id=1, keyword="历史")
        print(f"  [OK] 搜索历史相关记忆: {len(search_memories)} 条记录")
        
        high_conf_memories = user_memory_crud.get_high_confidence_memories(mock_db, user_id=1)
        print(f"  [OK] 获取高置信度记忆: {len(high_conf_memories)} 条记录")
        
        # 测试用户画像CRUD
        print("\n测试用户画像CRUD:")
        user_summary = user_summary_crud.get_by_user(mock_db, user_id=1)
        print(f"  [OK] 获取用户画像: {'存在' if user_summary else '不存在'}")
        
        print("\n[总结] 所有MySQL CRUD测试通过 ✓")
        
    except Exception as e:
        print(f"[错误] MySQL CRUD测试失败: {e}")
        return False
    
    return True

def test_model_validation():
    """测试模型验证功能"""
    print("\n" + "=" * 60)
    print("测试模型验证功能")
    print("=" * 60)
    
    try:
        from src.models.mysql_models import DhTripPlanner, DhUserProfile
        from pydantic import ValidationError
        
        print("测试模型字段验证:")
        
        # 测试必填字段验证
        try:
            # 缺少必填字段应该报错
            incomplete_user = DhUserProfile.User(nickname="测试用户")  # 缺少id, created_at, updated_at
            print("  [警告] 缺少必填字段但未报错，可能需要调整模型定义")
        except ValidationError as e:
            print("  [OK] 缺少必填字段正确报错")
        
        # 测试枚举值验证
        try:
            valid_status = DhUserProfile.User(
                id=1,
                nickname="测试",
                status=DhUserProfile.UserStatus.ACTIVE,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            print("  [OK] 有效枚举值验证通过")
        except ValidationError as e:
            print(f"  [错误] 有效枚举值验证失败: {e}")
        
        # 测试JSON字段处理
        try:
            planning_session = DhTripPlanner.AIPlanningSession(
                id="test-123",
                user_id=1, 
                user_input={"destination": "上海", "days": 2},
                created_at=datetime.now()
            )
            print("  [OK] JSON字段处理正确")
        except Exception as e:
            print(f"  [错误] JSON字段处理失败: {e}")
        
        print("\n[总结] 模型验证测试通过 ✓")
        
    except Exception as e:
        print(f"[错误] 模型验证测试失败: {e}")
        return False
    
    return True

def test_model_usage_examples():
    """测试模型使用示例"""
    print("\n" + "=" * 60)
    print("测试模型使用示例")  
    print("=" * 60)
    
    try:
        from src.models.mysql_models import DhTripPlanner, DhUserProfile, TripPlannerModels, UserProfileModels
        
        print("测试别名导入:")
        print(f"  [OK] TripPlannerModels == DhTripPlanner: {TripPlannerModels == DhTripPlanner}")
        print(f"  [OK] UserProfileModels == DhUserProfile: {UserProfileModels == DhUserProfile}")
        
        print("\n测试实际使用场景:")
        
        # 场景1: 创建新用户
        new_user = DhUserProfile.User(
            id=999,
            nickname="新用户",
            avatar_url="https://example.com/avatar.jpg",
            status=DhUserProfile.UserStatus.ACTIVE,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        print(f"  [OK] 创建新用户: {new_user.nickname} ({new_user.status})")
        
        # 场景2: 创建AI规划会话
        planning_request = {
            "destination": "杭州",
            "days": 2,
            "travelers": 2,
            "budget": 2000,
            "interests": ["西湖", "灵隐寺", "美食"]
        }
        
        ai_session = DhTripPlanner.AIPlanningSession(
            id="session-" + str(datetime.now().timestamp()),
            user_id=new_user.id,
            status=DhTripPlanner.PlanningSessionStatus.PENDING,
            user_input=planning_request,
            created_at=datetime.now()
        )
        print(f"  [OK] 创建AI规划会话: {ai_session.id}")
        
        # 场景3: 创建用户记忆
        memory = DhUserProfile.UserMemory(
            id=1,
            user_id=new_user.id,
            memory_content="用户偏好江南古典园林和传统文化",
            source_session_id=ai_session.id,
            confidence=0.85,
            created_at=datetime.now()
        )
        print(f"  [OK] 创建用户记忆: 置信度 {memory.confidence}")
        
        # 场景4: 创建行程
        itinerary = DhTripPlanner.Itinerary(
            id=1,
            user_id=new_user.id,
            title="杭州二日游",
            city_name="杭州",
            total_days=2,
            start_date=date.today(),
            notes="包含西湖和文化古迹的休闲行程",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        print(f"  [OK] 创建行程: {itinerary.title}")
        
        # 场景5: 输出为字典（模拟API响应）
        user_dict = new_user.model_dump()
        print(f"  [OK] 用户数据字典化: {len(user_dict)} 个字段")
        
        itinerary_dict = itinerary.model_dump()
        print(f"  [OK] 行程数据字典化: {len(itinerary_dict)} 个字段")
        
        print("\n[总结] 模型使用示例测试通过 ✓")
        
    except Exception as e:
        print(f"[错误] 模型使用示例测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("AutoPilot AI MySQL模型和CRUD集成测试")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("MySQL模型定义", test_mysql_models),
        ("MySQL CRUD操作", test_mysql_crud), 
        ("模型验证功能", test_model_validation),
        ("模型使用示例", test_model_usage_examples)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n执行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！MySQL模型和CRUD系统可以正常使用。")
        print("\n接下来可以:")
        print("1. 将这些模型集成到实际的数据库连接中")
        print("2. 在API层使用这些CRUD操作")
        print("3. 为Travel Planner Agent添加数据库操作支持")
        print("4. 实现用户记忆和画像管理功能")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查错误信息并修复。")
    
    return passed == total

if __name__ == "__main__":
    main() 