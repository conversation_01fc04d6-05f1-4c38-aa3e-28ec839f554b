# 旅行规划Agent - 工作流程与设计文档

本文档旨在定义一个集成了高德地图（Amap）MCP工具集、具备用户记忆和个性化能力的智能旅行规划Agent。Agent的目标是理解用户的复杂、口语化的旅行需求，通过调用外部工具获取实时信息，并结合用户偏好，最终生成结构化、可执行的旅行计划。

---

### 1. Agent 定位与目标

- **定位**: 一个懂旅行、懂用户的智能规划专家。
- **核心目标**:
    1.  **意图精准理解**: 准确解析用户输入，识别出发点、目的地、时间、预算、成员、兴趣点（如"亲子"、"网红打卡地"）等信息。
    2.  **个性化推荐**: 结合用户的历史行为、画像标签（存储于MongoDB中），提供千人千面的规划结果。
    3.  **动态信息整合**: 实时调用高德地图API获取天气、路况、POI信息，确保计划的时效性和准确性。
    4.  **结构化输出**: 生成标准化的JSON格式行程单，与前端（Web/小程序）解耦，易于渲染和二次开发。
    5.  **持续学习**: 将每次成功的规划和用户反馈存储为记忆，不断优化未来的推荐逻辑。

---

### 2. 核心能力：工具集（Amap MCP Tools）

Agent的能力来自于对以下15个高德API工具的编排和调用。

| 分类 | 工具名称 | 核心功能与用途 |
| :--- | :--- | :--- |
| **基础定位与地址解析** | `maps_geo` | **地址 → 经纬度**：所有空间计算的基础，流程的起点。 |
| | `maps_regeocode` | **经纬度 → 地址**：用于丰富地点信息，提供可读地址。 |
| | `maps_ip_location` | **IP地址 → 城市**：当用户未明确指定城市时，用于猜测默认城市。 |
| **路线规划与距离计算** | `maps_direction_driving` | **驾车路线规划**：用于规划城际、市内自驾路线。 |
| | `maps_direction_walking` | **步行路线规划**：用于景点之间、停车场到景点的短途路线。 |
| | `maps_direction_bicycling` | **骑行路线规划**：适用于城市慢行、景区游览。 |
| | `maps_direction_transit_integrated` | **公共交通路线规划**：适用于公共交通出行的用户。 |
| | `maps_distance` | **距离测量**：轻量级计算，用于快速筛选POI。 |
| **信息检索与周边搜索** | `maps_text_search` | **关键字搜索(POI)**：根据用户提到的特定名称搜索地点。 |
| | `maps_around_search` | **周边搜索(POI)**：**Agent核心工具**，用于动态发现目的的周边的美食、景点、停车场等。|
| | `maps_search_detail` | **POI 详情查询**：获取特定POI的评分、图片、营业时间等丰富信息。|
| **辅助上下文信息** | `maps_weather` | **天气查询**：决策行程安排的关键依据（如雨天推荐室内活动）。 |
| **高德App集成与唤醒** | `maps_schema_personal_map` | **生成行程地图链接**：将完整行程打包成一个高德地图链接。 |
| | `maps_schema_navi` | **生成导航链接**：为单段路程提供"一键导航"功能。 |
| | `maps_schema_take_taxi` | **生成打车链接**：提供便捷的出行方式。 |

---

### 3. 核心工作流（The Core Workflow）

Agent的工作流被设计为一个四阶段的、集成了数据库交互的闭环流程。

```mermaid
flowchart TD
    A["用户输入<br/>周末带孩子从亦庄开车去故宫"] 
    
    A --> B["Phase 1: 意图理解与个性化融合"]
    B --> B_DB[("读取用户数据库<br/>MySQL")]
    B_DB --> B1["构建个性化查询指令"]
    
    B1 --> C["Phase 2: 动态工具规划与并行执行"]
    
    %% 第一层并行调用
    C --> L1["第一层并行调用 (基础信息层)"]
    L1 --> L1_0["get_current_time()"]
    L1 --> L1_1["maps_geo(起点)"]
    L1 --> L1_2["maps_geo(目的地)"]  
    L1 --> L1_3["maps_weather(目的地城市)"]
    
    L1_0 --> WAIT1["等待基础信息返回"]
    L1_1 --> WAIT1
    L1_2 --> WAIT1
    L1_3 --> WAIT1
    
    %% 第二层并行调用
    WAIT1 --> L2["第二层并行调用 (核心POI信息层)"]
    
    L2 --> L2_PARK["🅿️ 停车信息组"]
    L2_PARK --> L2_1["maps_around_search(停车场)"]
    L2_PARK --> L2_2["maps_search_detail(停车详情)"]
    
    L2 --> L2_CHARGE["🔋 充电信息组"]
    L2_CHARGE --> L2_3["maps_around_search(充电桩)"]
    L2_CHARGE --> L2_4["maps_search_detail(充电详情)"]
    
    L2 --> L2_FOOD["🍽️ 美食信息组"]
    L2_FOOD --> L2_5["maps_around_search(美食/当地特色)"]
    L2_FOOD --> L2_6["maps_around_search(用户偏好餐厅)"]
    L2_FOOD --> L2_7["maps_search_detail(餐厅详情)"]
    
    L2 --> L2_ATTR["🎯 景点信息组"]
    L2_ATTR --> L2_8["maps_around_search(景点)"]
    L2_ATTR --> L2_9["maps_search_detail(景点详情)"]
    
    L2 --> L2_HOTEL["🏨 住宿信息组"]
    L2_HOTEL --> L2_10["maps_around_search(酒店/民宿)"]
    L2_HOTEL --> L2_11["maps_search_detail(住宿详情)"]
    
    L2_1 --> WAIT2["等待核心POI信息返回"]
    L2_2 --> WAIT2
    L2_3 --> WAIT2
    L2_4 --> WAIT2
    L2_5 --> WAIT2
    L2_6 --> WAIT2
    L2_7 --> WAIT2
    L2_8 --> WAIT2
    L2_9 --> WAIT2
    L2_10 --> WAIT2
    L2_11 --> WAIT2
    
    %% 第三层并行调用
    WAIT2 --> L3["第三层并行调用 (路线与辅助信息层)"]
    
    L3 --> L3_ROUTE["🛣️ 路线规划组"]
    L3_ROUTE --> L3_1["maps_direction_driving(主路线)"]
    L3_ROUTE --> L3_2["maps_direction_walking(步行接驳)"]
    
    L3 --> L3_MEDIA["📸 图片媒体组"]
    L3_MEDIA --> L3_3["高质量POI图片获取"]
    
    L3_1 --> WAIT3["等待路线信息返回"]
    L3_2 --> WAIT3
    L3_3 --> WAIT3
    
    %% 第四层并行调用
    WAIT3 --> L4["第四层并行调用 (深度信息挖掘层)"]
    
    L4 --> L4_PERSONAL["🎯 个性化深度挖掘"]
    L4_PERSONAL --> L4_1["maps_around_search(个性化关键词)"]
    
    L4 --> L4_EMERGENCY["🚨 应急便民信息"]
    L4_EMERGENCY --> L4_2["maps_around_search(医院/药店/厕所)"]
    
    L4_1 --> WAIT4["等待深度信息返回"]
    L4_2 --> WAIT4
    
    %% Phase 3 决策分析
    WAIT4 --> F["Phase 3: 数据综合与智能决策"]
    F --> F1["天气影响分析<br/>雨天/高温应对"]
    F --> F2["POI综合评分模型<br/>停车场/餐厅/景点评分"]
    F --> F3["行程动态编排<br/>智能匹配+时间优化"]
    F --> F4["方案生成与预案<br/>Plan B + 旅行小贴士"]
    
    F1 --> G["Phase 4: 结构化结果生成与记忆存储"]
    F2 --> G
    F3 --> G
    F4 --> G
    
    G --> G1["生成结构化JSON行程单"]
    G --> G2["生成高德地图链接<br/>personal_map + navi"]
    G --> G3["写入记忆数据库<br/>trips + memories"]
    
    G3 --> G_DB[("MySQL+MongoDB<br/>数据持久化")]
    
    G1 --> H["输出: 完整旅行规划<br/>JSON + 地图链接 + 小贴士"]
    G2 --> H
    G_DB --> H
    
    %% 样式定义
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style L1 fill:#fff9c4
    style L2 fill:#f0f4c3
    style L3 fill:#e8f5e8
    style L4 fill:#dcedc8
    style F fill:#fff3e0
    style G fill:#fce4ec
    style H fill:#e0f2f1
    
    %% 组样式
    style L2_PARK fill:#ffecb3
    style L2_CHARGE fill:#c8e6c9
    style L2_FOOD fill:#ffcdd2
    style L2_ATTR fill:#d1c4e9
    style L2_HOTEL fill:#b3e5fc
```

#### **Phase 1: 意图理解与个性化融合 (Intent Parsing & Personalization)**

-   **目标**: 将模糊的自然语言转化为结构化的、带有用户偏好的查询指令。
-   **步骤**:
    1.  **实体抽取**: 从用户输入中提取核心实体：
        -   `出发点`: 亦庄
        -   `目的地`: 故宫
        -   `出行方式`: 开车
        -   `时间`: 周末 (解析为具体日期)
        -   `修饰词/偏好`: "带孩子" -> `亲子`, "好停车" -> `停车场`, "小孩吃的餐厅" -> `美食`
    2.  **时间基准获取**: 获取当前系统时间，作为解析相对时间概念的基准（如"周末"、"明天"、"五一假期"）。
    3.  **用户画像加载**: 根据`UserID`，从**MySQL**的`dh_user_profile`数据库中读取用户数据。
        -   `长期记忆`: 从`user_memories`表中获取用户偏好（如"喜欢摄影"、"对海鲜过敏"）
        -   `画像摘要`: 从`user_summaries`表中获取AI生成的用户画像概要
        -   `旅行画像`: 从`user_travel_profiles`表中获取专属旅行偏好（如旅行风格、住宿偏好、交通偏好）
        -   `家庭关系`: 从用户记忆中识别家庭成员信息（如"用户李小乐(ID:3)是该用户的儿子"）
    4.  **查询指令构建**: 将提取的实体、时间基准和用户画像融合，形成内部查询对象。
        ```json
        {
          "current_time": "2025-01-27T14:30:00+08:00",
          "origin": "北京亦庄",
          "destination": "北京故宫",
          "transportMode": "driving",
          "dates": ["2025-06-21", "2025-06-22"],
          "userProfile": { 
            "user_id": 1,
            "travel_style": "FAMILY",
            "family_members": [{"user_id": 3, "relation": "儿子", "age": 4}],
            "accommodation_pref": ["连锁品牌", "主题酒店"],
            "transportation_pref": ["自驾"]
          },
          "interests": [
            { "type": "parking", "priority": 1 },
            { "type": "food", "tags": ["亲子餐厅", "儿童餐"], "priority": 2 },
            { "type": "attraction", "tags": ["适合儿童", "室内"], "priority": 3 }
          ]
        }
        ```

#### **Phase 2: 动态工具规划与并行执行 (业务维度扩展)**

-   **目标**: 基于内部查询指令，智能规划需要调用的工具组合，并以最高效、最全面的方式（并行）执行，为后续决策提供丰富的数据支持。
-   **策略**: 采用分层并行调用的策略，确保逻辑清晰和执行高效。

**第一层并行调用 (基础信息层)**
*获取规划所必需的时间基准、起点、终点坐标和天气。*
- `get_current_time()` 获取当前系统时间作为时间解析基准
- `maps_geo(address="起点")`
- `maps_geo(address="目的地")`
- `maps_weather(city="目的地城市")`

**第二层并行调用 (核心POI信息层)**
*基于第一层返回的目的地坐标，并发获取目的地周边的五大核心要素：*

-   **🅿️ 停车信息组**:
    -   `maps_around_search(location=dest_loc, keywords="停车场")`
    -   `maps_around_search(location=dest_loc, keywords="地下停车场")`
    -   对每个返回的停车场POI，调用 `maps_search_detail` 获取：收费标准、营业时间、到景点的步行距离、用户评分。

-   **🔋 新能源车充电信息组**:
    -   `maps_around_search(location=dest_loc, keywords="充电桩")`
    -   对每个返回的充电站POI，调用 `maps_search_detail` 获取：充电价格、快/慢充、充电桩功率、实时可用状态。

-   **🍽️ 美食餐饮信息组**:
    -   `maps_around_search(location=dest_loc, keywords="美食 OR 当地特色")`
    -   `maps_around_search(location=dest_loc, keywords="[用户偏好标签，如'亲子餐厅']")`
    -   对每个返回的餐厅POI，调用 `maps_search_detail` 获取：菜品图片、人均消费、营业时间、用户评分、特色标签。

-   **🎯 景点与娱乐信息组**:
    -   `maps_around_search(location=dest_loc, keywords="景点 OR [用户偏好标签，如'博物馆']")`
    -   对每个返回的景点POI，调用 `maps_search_detail` 获取：高清图片、门票价格、开放时间、建议游玩时长。**如果"建议游玩时长"缺失，将在Phase 3由LLM根据POI类别进行常识性估算（例如：博物馆约3小时，公园约2小时）。**

-   **🏨 住宿信息组** (如果行程跨天):
    -   `maps_around_search(location=dest_loc, keywords="酒店 OR 民宿")`
    -   对每个返回的住宿POI，调用 `maps_search_detail` 获取：房间图片、价格区间、用户评分、停车政策。

**第三层并行调用 (路线与辅助信息层)**
*在获取核心POI后，开始规划具体路线，并汇总费用信息。*

-   **🛣️ 路线规划组**:
    -   `maps_direction_driving(origin_loc, dest_loc)` 获取主干路线。
    -   `maps_direction_walking(停车场_loc, 景点_loc)` 获取步行接驳路线。
    -   对驾车路线，解析返回结果获得：过路费明细、预估耗时、路况。

-   **📸 图片与媒体组**:
    -   在所有`maps_search_detail`调用中，优先获取高质量的官方图片和用户上传图片，确保每个POI都有视觉参考。

**第四层并行调用 (深度信息挖掘层)**
*进行更深层次的个性化挖掘和应急信息准备。*

-   **🎯 个性化深度挖掘组**:
    -   根据用户画像标签（如"亲子"、"摄影"），调用 `maps_around_search` 搜索特定关键词（如"母婴室"、"拍照打卡地"）。
-   **🚨 应急与便民信息组**:
    -   `maps_around_search(location=dest_loc, keywords="医院 OR 药店 OR 公共厕所")`

#### **Phase 3: 数据综合与智能决策 (LLM-Powered Intelligent Reasoning)**

-   **核心理念**: 这是Agent的"智慧大脑"，完全依靠**LLM模型的推理能力**进行复杂决策，而非传统的基于规则的硬编码算法。LLM将整合海量数据、用户偏好、常识知识和情境理解，生成人性化的旅行方案。

-   **LLM推理决策框架**:

    **3.1 上下文数据整合**
    ```python
    # LLM输入：完整的决策上下文
    reasoning_context = {
        "user_profile": user_data,           # MySQL用户画像
        "travel_intent": extracted_intent,   # Phase 1解析结果
        "weather_forecast": weather_data,    # 多日天气预报
        "poi_candidates": all_poi_results,   # Phase 2搜索到的所有POI
        "budget_constraint": user_budget,    # 用户预算约束
        "time_preferences": user_schedule,   # 用户时间偏好
        "current_context": datetime.now()    # 当前时间基准
    }
    ```

    **3.2 LLM智能决策任务**

    -   **🌤️ 天气适应性分析** (LLM推理任务):
        ```
        任务: 基于天气预报和用户偏好，智能调整行程安排
        输入: 天气数据 + 用户活动偏好 + 候选景点列表
        LLM推理: 
        - "雨天时，用户带着4岁孩子，应优先安排室内活动，如科技馆、购物中心"
        - "高温天气下，建议上午户外景点，下午安排室内休息或地下商城"
        - "风和日丽适合户外徒步，可安排公园、古镇等开放景点"
        输出: 天气适应的行程建议 + 备选方案
        ```

    -   **🎯 POI智能匹配与推荐** (LLM推理任务):
        ```
        任务: 从候选POI中智能选择最符合用户需求的组合
        输入: 候选POI详情 + 用户画像 + 预算约束 + 行程天数
        LLM推理:
        - "用户是亲子家庭，餐厅需要考虑儿童座椅、卫生条件、菜品口味"
        - "预算2000元/人，酒店选择中等偏上，餐饮控制在200元/餐"
        - "用户偏好摄影，景点选择需要考虑光线、背景、人流量"
        - "行程总共只有1天，候选景点有8个。根据各景点游玩时间和用户亲子主题，选择核心的故宫博物院(半天)和景山公园(1小时)作为核心，放弃其他较远的景点。"
        输出: 个性化POI推荐列表 + 推荐理由
        ```

    -   **🏨 住宿需求与策略智能决策** (LLM推理任务):
        ```
        任务: 智能判断是否需要住宿，并制定最优住宿策略
        
        Step 1: 住宿需求判断
        输入: 出发地 + 目的地 + 行程天数 + 用户偏好
        LLM推理:
        - "用户从北京亦庄去天津2天，距离120公里，建议住宿"
        - "用户上海市内1日游，无需住宿安排"
        - "用户周边50公里内短途游，询问用户是否需要住宿体验"
        输出: 住宿必要性判断 + 询问建议
        
        Step 2: 同城/异地住宿策略分析
        输入: 各日景点地理分布 + 交通成本 + 用户住宿偏好
        LLM推理:
        - "第一天故宫+天安门(东城区)，第二天颐和园+圆明园(海淀区)"
        - "同城多区域：距离25公里，选择中心位置如西直门，方便往返"
        - "异地跨城：第一天北京，第二天天津，建议分别住宿减少舟车劳顿"
        - "用户重视睡眠质量，避开商业街噪音区，选择安静居住区"
        输出: 同城集中 vs 异地分散 的住宿策略决策
        
        Step 3: 住宿交互式确认  
        LLM推理:
        - "根据分析建议住宿，是否需要为您安排？"
        - "推荐同城统一住宿，是否接受？或偏好分区域住宿？"
        - "预算范围内推荐中档连锁酒店，是否符合您的期望？"
        输出: 交互式住宿确认 + 个性化住宿搜索策略
        ```

    -   **⏰ 行程时间智能编排** (LLM推理任务):
        ```
        任务: 综合考虑景点特性、交通耗时、用户习惯，编排合理时间线，并优化当日内景点的游览顺序
        输入: 景点开放时间 + 建议游玩时长 + 路线耗时 + 用户作息偏好
        LLM推理:
        - "故宫建议游玩3小时，但带孩子可能需要4-5小时，中间安排休息"
        - "用户不喜欢早起，第一个景点安排在10点以后"
        - "午餐时间考虑避开高峰期，选择12:30或13:30"
        - "同一天的景点，根据地理位置和开放时间，智能规划出最优访问顺序，如先去A再去B可节省20分钟车程"
        输出: 详细时间安排 + 缓冲时间 + 应急调整方案
        ```

    **3.3 LLM综合决策流程**
    ```python
    async def llm_intelligent_reasoning(context):
        """LLM驱动的智能决策主流程"""
        
        # Step 1: LLM分析用户需求和约束条件
        user_analysis = await reasoning_llm.analyze_user_context(
            prompt=f"分析用户旅行需求：{context}",
            task="深度理解用户偏好、约束条件和潜在需求"
        )
        
        # Step 2: LLM进行POI智能筛选和匹配
        poi_selection = await reasoning_llm.select_optimal_pois(
            candidates=context['poi_candidates'],
            user_profile=user_analysis,
            constraints=context['budget_constraint']
        )
        
        # Step 3: LLM制定住宿需求判断和交通策略
        logistics_strategy = await reasoning_llm.plan_logistics_with_accommodation(
            selected_pois=poi_selection,
            origin=context['travel_intent']['origin'],
            destination=context['travel_intent']['destination'],
            travel_dates=context['travel_intent']['dates'],
            user_preferences=user_analysis,
            task="先判断住宿必要性，再制定同城/异地住宿策略，最后生成交互确认建议"
        )
        
        # Step 4: LLM编排最终行程时间线
        final_itinerary = await reasoning_llm.orchestrate_timeline(
            pois=poi_selection,
            logistics=logistics_strategy,
            weather=context['weather_forecast'],
            user_schedule=context['time_preferences']
        )
        
        # Step 5: LLM生成备选方案和应急预案
        contingency_plans = await reasoning_llm.generate_alternatives(
            main_plan=final_itinerary,
            weather_risks=context['weather_forecast'],
            budget_alternatives=context['budget_constraint']
        )
        
        return {
            "main_itinerary": final_itinerary,
            "alternatives": contingency_plans,
            "reasoning_log": user_analysis,
            "optimization_suggestions": logistics_strategy
        }
    ```

    **3.4 住宿决策专项代码示例**
    ```python
    async def llm_accommodation_decision(context):
        """LLM驱动的住宿需求判断与策略制定"""
        
        accommodation_prompt = f"""
        基于以下信息，进行住宿需求分析和策略制定：
        
        出发地：{context['origin']}
        目的地：{context['destination']} 
        行程天数：{context['days']}天
        出行方式：{context['transport_mode']}
        用户预算：{context['budget']}
        景点分布：{context['attraction_distribution']}
        
        请按以下步骤进行分析：
        
        1. 住宿必要性判断：
        - 计算往返距离和时间成本
        - 评估是否需要住宿安排
        - 给出明确的建议和理由
        
        2. 住宿策略分析：
        - 如果需要住宿，分析景点地理分布
        - 判断是"同城集中"还是"异地分散"模式
        - 推荐具体的住宿区域选择
        
        3. 用户交互建议：
        - 生成需要向用户确认的问题
        - 提供住宿偏好选择建议
        
        请以JSON格式返回分析结果。
        """
        
        result = await reasoning_llm.generate(accommodation_prompt)
        return json.loads(result)
    ```

    **3.5 LLM决策的核心优势**
    -   **情境理解**: LLM能理解"带4岁孩子"意味着需要考虑安全、卫生、趣味性、休息频率等多维度因素
    -   **常识推理**: LLM知道"雨天不适合爬山"、"午餐时间餐厅会很拥挤"等生活常识
    -   **个性化权衡**: LLM能在预算、时间、偏好之间进行复杂的权衡决策
    -   **动态适应**: LLM能根据实时数据（如天气变化）动态调整推荐策略
    -   **解释性强**: LLM能为每个决策提供清晰的推理过程和解释
    -   **智能交互**: LLM能主动识别信息缺失，生成合适的用户交互问题

#### **Phase 4: 结构化结果生成与记忆存储 (Structured Response Generation & Memory Storage)**

-   **目标**: 将Agent的智能决策转化为用户友好的最终产品，并构建持续学习的数据闭环。
-   **核心产出**:

**4.1 多格式结果生成**
-   **结构化JSON行程单**: 
    -   包含完整的日程安排、POI详情、费用明细、地图链接
    -   支持前端渲染的标准化数据格式
    -   内嵌图片URL、评分、营业时间等丰富信息
-   **可视化地图产品**:
    -   调用 `maps_schema_personal_map` 生成完整行程地图
    -   为每个关键路段调用 `maps_schema_navi` 生成一键导航链接
    -   为打车需求调用 `maps_schema_take_taxi` 生成打车链接
-   **用户友好的文档**:
    -   自动生成旅行攻略HTML页面（如您的天津攻略案例）
    -   包含天气、费用、小贴士、路线图的完整攻略
    -   支持分享和离线查看

**4.2 质量检验与优化**
-   **逻辑一致性检查**: 验证时间安排的合理性、路线的连贯性
-   **费用合理性验证**: 确保预算计算准确，价格信息时效性
-   **用户体验优化**: 根据用户画像调整内容展示顺序和重点

**4.3 智能记忆构建**
-   **行程记忆存储**:
    -   `trips`集合: 完整行程JSON + 用户反馈 + 执行效果
    -   `poi_preferences`集合: 用户对特定POI的偏好度量
    -   `route_efficiency`集合: 路线规划的实际效果反馈
-   **用户画像更新**:
    -   基于本次规划，更新用户的兴趣标签权重
    -   记录预算偏好、出行习惯的变化趋势
    -   构建用户的"旅行DNA"档案
-   **系统学习优化**:
    -   分析成功规划的共同特征，优化推荐算法
    -   识别失败案例的问题原因，改进决策逻辑
    -   建立POI质量评价的动态更新机制

---

### 4. 新增：交互式分步规划工作流 (Interactive Step-by-Step Planning)

基于您的最新需求，我们对原有的四阶段并行架构进行了重要调整，新的工作流更加贴近用户的真实规划思维和UI交互体验。

#### **核心改进点**

1. **时间基准前置**: 在任何规划开始前，Agent必须获取当前系统时间，确保"周末"、"明天"等相对时间概念的准确解析。

2. **分步交互式体验**: 从"黑箱并行"改为"分步可见"，用户可以实时看到每一步的规划结果，并参与决策过程。

3. **数据库架构调整**: 从MongoDB单一存储改为**MySQL(结构化用户数据) + MongoDB(执行日志)**的双数据库架构。

4. **LLM决策增强**: 在住宿策略、POI匹配等关键决策点，使用LLM进行智能判断，结合预算约束提供更合理的方案。

#### **详细工作流程**

**阶段0: 准备与上下文构建**
1. **获取当前时间**: `get_current_time()` → 作为所有时间解析的基准
2. **意图理解**: 解析用户输入，提取基本实体
3. **用户画像加载**: 从MySQL `dh_user_profile`库读取完整用户档案
4. **【界面展示】**: 在"思考过程"区域展示已识别的信息和从记忆中获取的偏好
5. **交互式补全**: Agent主动询问缺失信息，用户补充后更新"思考过程"
6. **点击"立即规划"**: 启动正式规划流程

**阶段1: 先规划景点 (Core Attraction Planning)**
1. **基础信息获取**: 并行调用地理编码和天气查询
2. **景点搜索与初步筛选**: 基于用户偏好和天气情况，搜索并评分一批（如5-8个）候选景点。
3. **【LLM决策】景点精选与推荐**: LLM从候选列表中进行智能**精选**，综合考虑行程天数、游玩价值、用户兴趣等，挑选出1-2个核心景点作为主要推荐。
4. **【用户交互确认 - 可选环节】**: Agent向用户展示推荐的核心景点及推荐理由（例如："根据您的亲子需求和行程时间，我为您精选了【故宫】和【科技馆】，您看可以吗？"）。同时，界面上可展示其他候选景点，允许用户确认、替换或增选。**此环节为可插拔模块，在初期实现时可跳过，由AI直接决策。**
5. **【界面展示】**: 显示最终确定的核心景点安排。

**阶段2: 住宿需求判断与策略分析**
1. **【LLM住宿需求判断】**: 基于**已确定的核心景点**、出发地、目的地、行程天数，智能判断是否需要住宿安排。
   - 同城短途（<50km）：询问用户是否需要住宿体验
   - 异地中长途（>50km）：建议住宿安排
   - 多日行程：必须住宿规划
2. **【LLM地理分析】**: 计算各日景点的地理分布，判断"同城集中"还是"异地分散"模式
   - 同城多区域：选择中心位置，方便各区域往返
   - 异地跨城：分别住宿，减少每日长途通勤
3. **【用户确认交互】**: Agent主动询问住宿偏好和预算接受度
   - "根据您的行程，建议安排2晚住宿，是否需要为您规划？"
   - "推荐统一住在市中心，方便往返各景点，您是否接受？"
4. **【LLM预算决策】**: 结合用户预算和住宿偏好，制定具体住宿策略
5. **【酒店搜索与推荐】**: 基于确认的策略执行具体的酒店搜索
6. **【界面展示】**: 展示住宿方案、推荐理由和价格预估

**阶段3: 每日配套填充 (Daily Support Services)**
1. **遍历每日行程**: 以确定的景点为核心，逐日填充配套信息
2. **【美食规划】**: 围绕景点搜索餐厅，LLM根据用户口味偏好和预算筛选
3. **【停车/充电规划】**: 查找每个景点附近的停车场和充电桩
4. **【内部交通规划】**: 规划当日的景点间路线和最优行进顺序
5. **【界面展示】**: 逐步完善每日行程的详细信息

**阶段4: 最终整合与生成**
1. **行程优化**: 对整体时间安排进行最后调整
2. **费用汇总**: 计算总体预算和各项费用明细
3. **生成导航链接**: 为每个关键路段生成高德导航链接
4. **【界面展示】**: 呈现完整、可执行的旅行规划

#### **关键技术实现**

**时间解析增强**:
```python
# 伪代码示例
current_time = get_current_time()  # "2025-01-27T14:30:00+08:00"
user_input = "周末去故宫"

# LLM Prompt
prompt = f"""
当前时间: {current_time}
用户请求: {user_input}

请解析用户的时间意图，将"周末"转换为具体日期。
考虑中国的工作日制度(周一到周五工作，周六周日休息)。
"""
```

**LLM住宿策略决策**:
```python
# 伪代码示例
def decide_accommodation_strategy(daily_attractions, user_budget):
    prompt = f"""
    行程分析:
    Day 1 景点: {daily_attractions['day1']} (中心点: 116.4, 39.9)
    Day 2 景点: {daily_attractions['day2']} (中心点: 116.6, 40.1)
    Day 3 景点: {daily_attractions['day3']} (中心点: 116.3, 39.8)
    
    用户预算: 每晚{user_budget}元
    
    请分析:
    1. 两日景点间距离是否超过50公里？
    2. 推荐"单一酒店住2晚"还是"每天换酒店"？
    3. 基于预算推荐具体酒店类型
    """
    return llm_call(prompt)
```

这个新的工作流既保持了AI的智能决策能力，又提供了用户可控的交互体验，完美契合了您提出的产品需求。

---

### 5. 调用策略示例

基于业务复杂度和用户需求，Agent采用不同的工具调用策略：

#### **4.1 基础信息查询 (Level 1)**
*单一信息点查询，1-2个工具调用*

**示例**: "查一下北京今天的天气"
```
工具链: maps_weather(city="北京") 
响应: 直接返回天气信息，包含温度、降雨、建议穿衣指数
```

**示例**: "故宫博物院现在是否开放？"
```
工具链: maps_text_search(keywords="故宫博物院") -> maps_search_detail(id)
响应: 营业时间、门票价格、今日状态
```

#### **4.2 路线规划查询 (Level 2)**
*涉及地理计算，3-5个工具调用*

**示例**: "从三里屯开车到颐和园要多久？停车方便吗？"
```
工具链: 
- 并行: maps_geo("三里屯") + maps_geo("颐和园")
- maps_direction_driving(起点, 终点)
- maps_around_search(location=颐和园, keywords="停车场")
- maps_search_detail(停车场详情)
响应: 路线时间、费用、推荐停车场及收费标准
```

**示例**: "我在国贸，想坐地铁去王府井，最快路线是什么？"
```
工具链:
- 并行: maps_geo("国贸") + maps_geo("王府井")  
- maps_direction_transit_integrated(起点, 终点, city="北京", cityd="北京")
响应: 地铁换乘方案、用时、票价
```

#### **4.3 周边探索查询 (Level 3)**
*区域性需求，8-15个工具调用*

**示例**: "我在三里屯，附近有什么好吃的川菜馆？要有停车位的"
```
工具链:
- maps_geo("三里屯") 获取坐标
- 并行调用:
  - maps_around_search(location, keywords="川菜")
  - maps_around_search(location, keywords="停车场", radius=500m)
- 对top5川菜馆并行调用 maps_search_detail 获取详情
- 计算各餐厅到停车场的 maps_distance
响应: 推荐3家川菜馆 + 最佳停车方案 + 步行路线
```

#### **4.4 全方位旅行规划 (Level 4)**
*复合需求，20-40个工具调用，四层并行架构*

**示例**: "周末带孩子从亦庄开车去故宫，想玩一整天，求推荐路线"
```
工具链: 完整四阶段工作流
- Phase 1: 意图解析 + 用户画像加载
- Phase 2: 四层并行调用
  * 第一层: 基础信息(3个调用)
  * 第二层: 五大业务组并行(15-20个调用)
  * 第三层: 路线规划组(5-8个调用)  
  * 第四层: 个性化挖掘(3-5个调用)
- Phase 3: 综合决策与智能匹配
- Phase 4: 多格式结果生成
响应: 完整一日游攻略 + 高德地图链接 + 费用明细 + 实用贴士
```

#### **4.5 跨城市复杂规划 (Level 5)**
*最高复杂度，50+工具调用*

**示例**: "五一假期从北京自驾去天津2天1夜游，求详细攻略"
```
工具链: 超级工作流
- 多城市并行信息获取:
  * 北京: 出发准备信息(天气、路况、充电站)
  * 天津: 全套旅游资源(景点、美食、住宿、停车)
  * 路线: 跨城驾车路线 + 城内接驳路线
- 二日行程优化算法:
  * 第一天: 到达 -> 游览 -> 住宿
  * 第二天: 继续游览 -> 返程
- 预案与应急:
  * Plan B方案(天气变化)
响应: 详细2日攻略 + 每日导航链接 + 费用预算 + 应急预案
```

#### **4.6 智能调用优化策略**

-   **并行度动态调整**: 根据查询复杂度自动确定并行调用的层次和数量
-   **缓存策略**: 相同区域的基础信息(天气、POI)采用缓存减少重复调用
-   **错误降级**: 若某个工具调用失败，自动采用备选方案继续执行
-   **结果质量控制**: 实时评估API返回的数据质量，过滤低质量POI
-   **用户反馈学习**: 根据用户对推荐结果的反馈，动态调整工具调用的权重和策略
